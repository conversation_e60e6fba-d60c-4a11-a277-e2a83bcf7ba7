"""
Selenium Testing Agent - Production-ready Selenium automation agent
Inherits from autogen.ConversableAgent with comprehensive testing capabilities
"""

import os
import json
import csv
import time
import logging
import platform
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path

# Selenium imports
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, WebDriverException,
    ElementNotInteractableException, StaleElementReferenceException
)

# WebDriver Manager imports
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager

# Additional imports
from PIL import Image
import pandas as pd
import yaml

# AutoGen import
from autogen import ConversableAgent

class SeleniumTestAgent(ConversableAgent):
    """
    Production-ready Selenium Testing Agent with comprehensive automation capabilities
    """
    
    def __init__(self, name="SeleniumTestAgent", claude_client=None, **kwargs):
        """Initialize the Selenium Test Agent"""
        super().__init__(name=name, human_input_mode="NEVER", **kwargs)
        
        self.claude_client = claude_client
        self.driver = None
        self.wait = None
        self.config = {}
        self.test_results = []
        self.screenshots_dir = "test_screenshots"
        self.logs_dir = "test_logs"
        self.test_data_dir = "test_data"
        
        # Supported browsers
        self.supported_browsers = ["chrome", "firefox", "edge"]
        
        # Locator strategies mapping
        self.locator_strategies = {
            "id": By.ID,
            "name": By.NAME,
            "class": By.CLASS_NAME,
            "tag": By.TAG_NAME,
            "xpath": By.XPATH,
            "css": By.CSS_SELECTOR,
            "link_text": By.LINK_TEXT,
            "partial_link_text": By.PARTIAL_LINK_TEXT
        }
        
        # Setup directories and logging
        self._setup_directories()
        self._setup_logging()
        
        # Load default configuration
        self._load_default_config()
    
    def _setup_directories(self):
        """Create necessary directories for screenshots, logs, and test data"""
        for directory in [self.screenshots_dir, self.logs_dir, self.test_data_dir]:
            Path(directory).mkdir(exist_ok=True)
    
    def _setup_logging(self):
        """Setup structured logging"""
        log_file = os.path.join(self.logs_dir, f"selenium_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(self.name)
        self.logger.info(f"Selenium Test Agent initialized - Log file: {log_file}")
    
    def _load_default_config(self):
        """Load default configuration"""
        self.config = {
            "browser": "chrome",
            "headless": False,
            "implicit_wait": 10,
            "explicit_wait": 30,
            "page_load_timeout": 60,
            "window_size": {"width": 1920, "height": 1080},
            "retry_attempts": 3,
            "retry_delay": 2,
            "screenshot_on_failure": True,
            "environment": "test",
            "demo_delay": 2  # Delay between actions for demo visibility
        }
    
    def load_config(self, config_path: str) -> bool:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as file:
                user_config = yaml.safe_load(file)
                self.config.update(user_config)
            self.logger.info(f"Configuration loaded from {config_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to load config from {config_path}: {str(e)}")
            return False
    
    def setup_browser(self, browser: str = None, headless: bool = None) -> bool:
        """Setup browser with webdriver-manager integration"""
        try:
            browser = browser or self.config.get("browser", "chrome")
            headless = headless if headless is not None else self.config.get("headless", False)
            
            if browser.lower() not in self.supported_browsers:
                raise ValueError(f"Unsupported browser: {browser}. Supported: {self.supported_browsers}")
            
            self.logger.info(f"Setting up {browser} browser (headless: {headless})")
            
            if browser.lower() == "chrome":
                options = webdriver.ChromeOptions()
                if headless:
                    options.add_argument("--headless")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-gpu")
                options.add_argument(f"--window-size={self.config['window_size']['width']},{self.config['window_size']['height']}")
                
                service = ChromeService(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=options)
                
            elif browser.lower() == "firefox":
                options = webdriver.FirefoxOptions()
                if headless:
                    options.add_argument("--headless")
                
                service = FirefoxService(GeckoDriverManager().install())
                self.driver = webdriver.Firefox(service=service, options=options)
                
            elif browser.lower() == "edge":
                options = webdriver.EdgeOptions()
                if headless:
                    options.add_argument("--headless")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                
                service = EdgeService(EdgeChromiumDriverManager().install())
                self.driver = webdriver.Edge(service=service, options=options)
            
            # Configure timeouts
            self.driver.implicitly_wait(self.config.get("implicit_wait", 10))
            self.driver.set_page_load_timeout(self.config.get("page_load_timeout", 60))
            
            # Setup WebDriverWait
            self.wait = WebDriverWait(self.driver, self.config.get("explicit_wait", 30))
            
            # Set window size for non-headless mode
            if not headless:
                self.driver.set_window_size(
                    self.config['window_size']['width'],
                    self.config['window_size']['height']
                )
            
            self.logger.info(f"Browser {browser} setup completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup browser {browser}: {str(e)}")
            return False
    
    def navigate_to(self, url: str) -> bool:
        """Navigate to a URL with retry mechanism"""
        for attempt in range(self.config.get("retry_attempts", 3)):
            try:
                self.logger.info(f"Navigating to: {url} (attempt {attempt + 1})")
                self.driver.get(url)
                
                # Wait for page to load
                self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
                
                self.logger.info(f"Successfully navigated to: {url}")
                # Add demo delay
                time.sleep(self.config.get("demo_delay", 2))
                return True
                
            except Exception as e:
                self.logger.warning(f"Navigation attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.config.get("retry_attempts", 3) - 1:
                    time.sleep(self.config.get("retry_delay", 2))
                else:
                    self.logger.error(f"Failed to navigate to {url} after all attempts")
                    if self.config.get("screenshot_on_failure", True):
                        self.capture_screenshot(f"navigation_failure_{int(time.time())}")
        
        return False
    
    def find_element(self, locator_type: str, locator_value: str, timeout: int = None) -> Optional[Any]:
        """Find element with dynamic wait and comprehensive error handling"""
        try:
            timeout = timeout or self.config.get("explicit_wait", 30)
            by_strategy = self.locator_strategies.get(locator_type.lower())
            
            if not by_strategy:
                raise ValueError(f"Unsupported locator type: {locator_type}")
            
            self.logger.debug(f"Finding element: {locator_type}='{locator_value}'")
            
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by_strategy, locator_value))
            )
            
            return element
            
        except TimeoutException:
            self.logger.error(f"Element not found: {locator_type}='{locator_value}' within {timeout}s")
            return None
        except Exception as e:
            self.logger.error(f"Error finding element {locator_type}='{locator_value}': {str(e)}")
            return None
    
    def find_elements(self, locator_type: str, locator_value: str) -> List[Any]:
        """Find multiple elements"""
        try:
            by_strategy = self.locator_strategies.get(locator_type.lower())
            if not by_strategy:
                raise ValueError(f"Unsupported locator type: {locator_type}")
            
            elements = self.driver.find_elements(by_strategy, locator_value)
            self.logger.debug(f"Found {len(elements)} elements: {locator_type}='{locator_value}'")
            return elements
            
        except Exception as e:
            self.logger.error(f"Error finding elements {locator_type}='{locator_value}': {str(e)}")
            return []
    
    def click_element(self, locator_type: str, locator_value: str, timeout: int = None) -> bool:
        """Click element with retry mechanism"""
        for attempt in range(self.config.get("retry_attempts", 3)):
            try:
                element = self.find_element(locator_type, locator_value, timeout)
                if not element:
                    return False
                
                # Wait for element to be clickable
                clickable_element = self.wait.until(EC.element_to_be_clickable(element))
                clickable_element.click()
                
                self.logger.info(f"Successfully clicked element: {locator_type}='{locator_value}'")
                # Add demo delay
                time.sleep(self.config.get("demo_delay", 2))
                return True
                
            except (ElementNotInteractableException, StaleElementReferenceException) as e:
                self.logger.warning(f"Click attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.config.get("retry_attempts", 3) - 1:
                    time.sleep(self.config.get("retry_delay", 2))
                else:
                    self.logger.error(f"Failed to click element after all attempts")
                    if self.config.get("screenshot_on_failure", True):
                        self.capture_screenshot(f"click_failure_{int(time.time())}")
            except Exception as e:
                self.logger.error(f"Unexpected error clicking element: {str(e)}")
                break
        
        return False
    
    def type_text(self, locator_type: str, locator_value: str, text: str, clear_first: bool = True) -> bool:
        """Type text into element"""
        try:
            element = self.find_element(locator_type, locator_value)
            if not element:
                return False
            
            if clear_first:
                element.clear()
            
            element.send_keys(text)
            self.logger.info(f"Successfully typed text into element: {locator_type}='{locator_value}'")
            # Add demo delay
            time.sleep(self.config.get("demo_delay", 2))
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to type text into element {locator_type}='{locator_value}': {str(e)}")
            if self.config.get("screenshot_on_failure", True):
                self.capture_screenshot(f"type_failure_{int(time.time())}")
            return False
    
    def select_dropdown(self, locator_type: str, locator_value: str, selection_method: str, selection_value: str) -> bool:
        """Select from dropdown using various methods"""
        try:
            element = self.find_element(locator_type, locator_value)
            if not element:
                return False
            
            select = Select(element)
            
            if selection_method.lower() == "value":
                select.select_by_value(selection_value)
            elif selection_method.lower() == "text":
                select.select_by_visible_text(selection_value)
            elif selection_method.lower() == "index":
                select.select_by_index(int(selection_value))
            else:
                raise ValueError(f"Unsupported selection method: {selection_method}")
            
            self.logger.info(f"Successfully selected from dropdown: {selection_method}='{selection_value}'")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to select from dropdown: {str(e)}")
            if self.config.get("screenshot_on_failure", True):
                self.capture_screenshot(f"select_failure_{int(time.time())}")
            return False
    
    def upload_file(self, locator_type: str, locator_value: str, file_path: str) -> bool:
        """Upload file to file input element"""
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"File not found: {file_path}")
                return False
            
            element = self.find_element(locator_type, locator_value)
            if not element:
                return False
            
            # Convert to absolute path
            abs_file_path = os.path.abspath(file_path)
            element.send_keys(abs_file_path)
            
            self.logger.info(f"Successfully uploaded file: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to upload file {file_path}: {str(e)}")
            if self.config.get("screenshot_on_failure", True):
                self.capture_screenshot(f"upload_failure_{int(time.time())}")
            return False
    
    def handle_alert(self, action: str = "accept", text: str = None) -> bool:
        """Handle JavaScript alerts"""
        try:
            alert = self.wait.until(EC.alert_is_present())
            
            if text:
                alert.send_keys(text)
            
            if action.lower() == "accept":
                alert.accept()
            elif action.lower() == "dismiss":
                alert.dismiss()
            else:
                raise ValueError(f"Unsupported alert action: {action}")
            
            self.logger.info(f"Successfully handled alert with action: {action}")
            return True
            
        except TimeoutException:
            self.logger.warning("No alert present")
            return False
        except Exception as e:
            self.logger.error(f"Failed to handle alert: {str(e)}")
            return False
    
    def switch_to_frame(self, frame_reference: Union[str, int, Any]) -> bool:
        """Switch to iframe or frame"""
        try:
            if isinstance(frame_reference, str):
                # Try to find frame by name or id
                try:
                    frame = self.find_element("name", frame_reference) or self.find_element("id", frame_reference)
                    if frame:
                        self.driver.switch_to.frame(frame)
                    else:
                        self.driver.switch_to.frame(frame_reference)
                except:
                    self.driver.switch_to.frame(frame_reference)
            elif isinstance(frame_reference, int):
                self.driver.switch_to.frame(frame_reference)
            else:
                self.driver.switch_to.frame(frame_reference)
            
            self.logger.info(f"Successfully switched to frame: {frame_reference}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to switch to frame {frame_reference}: {str(e)}")
            return False
    
    def switch_to_default_content(self) -> bool:
        """Switch back to default content"""
        try:
            self.driver.switch_to.default_content()
            self.logger.info("Successfully switched to default content")
            return True
        except Exception as e:
            self.logger.error(f"Failed to switch to default content: {str(e)}")
            return False
    
    def switch_to_window(self, window_handle: str = None, window_index: int = None) -> bool:
        """Switch to window by handle or index"""
        try:
            if window_handle:
                self.driver.switch_to.window(window_handle)
                self.logger.info(f"Switched to window handle: {window_handle}")
            elif window_index is not None:
                handles = self.driver.window_handles
                if 0 <= window_index < len(handles):
                    self.driver.switch_to.window(handles[window_index])
                    self.logger.info(f"Switched to window index: {window_index}")
                else:
                    raise IndexError(f"Window index {window_index} out of range")
            else:
                # Switch to latest window
                self.driver.switch_to.window(self.driver.window_handles[-1])
                self.logger.info("Switched to latest window")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to switch window: {str(e)}")
            return False
    
    def capture_screenshot(self, filename: str = None) -> str:
        """Capture screenshot with automatic naming"""
        try:
            if not filename:
                filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            if not filename.endswith(('.png', '.jpg', '.jpeg')):
                filename += '.png'
            
            screenshot_path = os.path.join(self.screenshots_dir, filename)
            self.driver.save_screenshot(screenshot_path)
            
            self.logger.info(f"Screenshot saved: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"Failed to capture screenshot: {str(e)}")
            return ""
    
    def wait_for_element(self, locator_type: str, locator_value: str, condition: str = "presence", timeout: int = None) -> bool:
        """Wait for element with various conditions"""
        try:
            timeout = timeout or self.config.get("explicit_wait", 30)
            by_strategy = self.locator_strategies.get(locator_type.lower())
            
            if not by_strategy:
                raise ValueError(f"Unsupported locator type: {locator_type}")
            
            locator = (by_strategy, locator_value)
            
            if condition == "presence":
                self.wait.until(EC.presence_of_element_located(locator))
            elif condition == "visible":
                self.wait.until(EC.visibility_of_element_located(locator))
            elif condition == "clickable":
                self.wait.until(EC.element_to_be_clickable(locator))
            elif condition == "invisible":
                self.wait.until(EC.invisibility_of_element_located(locator))
            else:
                raise ValueError(f"Unsupported wait condition: {condition}")
            
            self.logger.info(f"Wait condition '{condition}' met for element: {locator_type}='{locator_value}'")
            return True
            
        except TimeoutException:
            self.logger.error(f"Wait condition '{condition}' not met for element: {locator_type}='{locator_value}' within {timeout}s")
            return False
        except Exception as e:
            self.logger.error(f"Error waiting for element: {str(e)}")
            return False
    
    def execute_javascript(self, script: str, *args) -> Any:
        """Execute JavaScript code"""
        try:
            result = self.driver.execute_script(script, *args)
            self.logger.info(f"Successfully executed JavaScript: {script[:50]}...")
            return result
        except Exception as e:
            self.logger.error(f"Failed to execute JavaScript: {str(e)}")
            return None
    
    def get_element_text(self, locator_type: str, locator_value: str) -> str:
        """Get text content of element"""
        try:
            element = self.find_element(locator_type, locator_value)
            if element:
                text = element.text
                self.logger.debug(f"Retrieved text from element: '{text}'")
                return text
            return ""
        except Exception as e:
            self.logger.error(f"Failed to get element text: {str(e)}")
            return ""
    
    def get_element_attribute(self, locator_type: str, locator_value: str, attribute: str) -> str:
        """Get attribute value of element"""
        try:
            element = self.find_element(locator_type, locator_value)
            if element:
                value = element.get_attribute(attribute)
                self.logger.debug(f"Retrieved attribute '{attribute}' from element: '{value}'")
                return value or ""
            return ""
        except Exception as e:
            self.logger.error(f"Failed to get element attribute: {str(e)}")
            return ""
    
    def load_test_data_json(self, file_path: str) -> Dict[str, Any]:
        """Load test data from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            self.logger.info(f"Loaded test data from JSON: {file_path}")
            return data
        except Exception as e:
            self.logger.error(f"Failed to load JSON test data from {file_path}: {str(e)}")
            return {}
    
    def load_test_data_csv(self, file_path: str) -> List[Dict[str, Any]]:
        """Load test data from CSV file"""
        try:
            df = pd.read_csv(file_path)
            data = df.to_dict('records')
            self.logger.info(f"Loaded {len(data)} records from CSV: {file_path}")
            return data
        except Exception as e:
            self.logger.error(f"Failed to load CSV test data from {file_path}: {str(e)}")
            return []
    
    def run_test_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test scenario"""
        scenario_name = scenario.get("name", "Unnamed Scenario")
        start_time = time.time()
        
        self.logger.info(f"Starting test scenario: {scenario_name}")
        
        result = {
            "scenario_name": scenario_name,
            "start_time": datetime.now().isoformat(),
            "status": "PASS",
            "steps": [],
            "errors": [],
            "screenshots": [],
            "execution_time": 0
        }
        
        try:
            steps = scenario.get("steps", [])
            
            for i, step in enumerate(steps, 1):
                step_start = time.time()
                step_result = self._execute_step(step, i)
                step_result["execution_time"] = time.time() - step_start
                
                result["steps"].append(step_result)
                
                if not step_result["success"]:
                    result["status"] = "FAIL"
                    result["errors"].append(step_result.get("error", "Unknown error"))
                    
                    # Capture screenshot on failure
                    if self.config.get("screenshot_on_failure", True):
                        screenshot_path = self.capture_screenshot(f"{scenario_name}_step_{i}_failure")
                        if screenshot_path:
                            result["screenshots"].append(screenshot_path)
                    
                    # Stop on first failure if configured
                    if scenario.get("stop_on_failure", True):
                        break
        
        except Exception as e:
            result["status"] = "ERROR"
            result["errors"].append(f"Scenario execution error: {str(e)}")
            self.logger.error(f"Error in scenario {scenario_name}: {str(e)}")
        
        finally:
            result["execution_time"] = time.time() - start_time
            result["end_time"] = datetime.now().isoformat()
            
            self.logger.info(f"Completed scenario: {scenario_name} - Status: {result['status']} - Time: {result['execution_time']:.2f}s")
        
        return result
    
    def _add_smart_methods(self):
        """Add smart element finding methods"""
        self.smart_selectors = {
            "username": ["input[name='username']", "input[name='email']", "input[type='email']", "#username", "#email", "input[placeholder*='username']", "input[placeholder*='email']"],
            "password": ["input[name='password']", "input[type='password']", "#password", "input[placeholder*='password']"],
            "submit": ["button[type='submit']", "input[type='submit']", "button", ".login-btn", "#login", "#submit"]
        }
    
    def smart_find_element(self, element_type: str):
        """Find element using multiple fallback selectors"""
        selectors = self.smart_selectors.get(element_type, [])
        for selector in selectors:
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selector)
                if element.is_displayed():
                    return element
            except:
                continue
        return None
    
    def _execute_step(self, step: Dict[str, Any], step_number: int) -> Dict[str, Any]:
        """Execute a single test step"""
        action = step.get("action", "").lower()
        step_result = {
            "step_number": step_number,
            "action": action,
            "success": False,
            "error": None,
            "execution_time": 0
        }
        
        try:
            if action == "navigate":
                step_result["success"] = self.navigate_to(step["url"])
                
            elif action == "click":
                step_result["success"] = self.click_element(
                    step["locator_type"], step["locator_value"]
                )
                
            elif action == "type":
                step_result["success"] = self.type_text(
                    step["locator_type"], step["locator_value"], step["text"]
                )
                
            elif action == "select":
                step_result["success"] = self.select_dropdown(
                    step["locator_type"], step["locator_value"],
                    step["selection_method"], step["selection_value"]
                )
                
            elif action == "upload":
                step_result["success"] = self.upload_file(
                    step["locator_type"], step["locator_value"], step["file_path"]
                )
                
            elif action == "wait":
                step_result["success"] = self.wait_for_element(
                    step["locator_type"], step["locator_value"],
                    step.get("condition", "presence"), step.get("timeout")
                )
                
            elif action == "alert":
                step_result["success"] = self.handle_alert(
                    step.get("alert_action", "accept"), step.get("text")
                )
                
            elif action == "switch_frame":
                step_result["success"] = self.switch_to_frame(step["frame_reference"])
                
            elif action == "switch_default":
                step_result["success"] = self.switch_to_default_content()
                
            elif action == "switch_window":
                step_result["success"] = self.switch_to_window(
                    step.get("window_handle"), step.get("window_index")
                )
                
            elif action == "screenshot":
                screenshot_path = self.capture_screenshot(step.get("filename"))
                step_result["success"] = bool(screenshot_path)
                step_result["screenshot_path"] = screenshot_path
                
            elif action == "javascript":
                result = self.execute_javascript(step["script"])
                step_result["success"] = result is not None
                step_result["result"] = result
                
            elif action == "verify_text":
                actual_text = self.get_element_text(step["locator_type"], step["locator_value"])
                expected_text = step["expected_text"]
                step_result["success"] = expected_text in actual_text
                step_result["actual_text"] = actual_text
                step_result["expected_text"] = expected_text
                
            elif action == "verify_attribute":
                actual_value = self.get_element_attribute(
                    step["locator_type"], step["locator_value"], step["attribute"]
                )
                expected_value = step["expected_value"]
                step_result["success"] = actual_value == expected_value
                step_result["actual_value"] = actual_value
                step_result["expected_value"] = expected_value
                
            elif action == "sleep":
                time.sleep(step.get("duration", 1))
                step_result["success"] = True
                
            else:
                step_result["error"] = f"Unsupported action: {action}"
                
        except Exception as e:
            step_result["error"] = str(e)
            self.logger.error(f"Step {step_number} failed: {str(e)}")
        
        return step_result
    
    def run_test_suite(self, test_file_path: str) -> Dict[str, Any]:
        """Run complete test suite from JSON file"""
        try:
            test_suite = self.load_test_data_json(test_file_path)
            if not test_suite:
                return {"error": "Failed to load test suite"}
            
            suite_name = test_suite.get("suite_name", "Test Suite")
            scenarios = test_suite.get("scenarios", [])
            
            self.logger.info(f"Starting test suite: {suite_name} with {len(scenarios)} scenarios")
            
            suite_result = {
                "suite_name": suite_name,
                "start_time": datetime.now().isoformat(),
                "total_scenarios": len(scenarios),
                "passed_scenarios": 0,
                "failed_scenarios": 0,
                "scenario_results": [],
                "execution_time": 0
            }
            
            start_time = time.time()
            
            # Setup browser if not already done
            if not self.driver:
                browser = test_suite.get("browser", self.config.get("browser", "chrome"))
                headless = test_suite.get("headless", self.config.get("headless", False))
                if not self.setup_browser(browser, headless):
                    return {"error": "Failed to setup browser"}
            
            # Run each scenario
            for scenario in scenarios:
                scenario_result = self.run_test_scenario(scenario)
                suite_result["scenario_results"].append(scenario_result)
                
                if scenario_result["status"] == "PASS":
                    suite_result["passed_scenarios"] += 1
                else:
                    suite_result["failed_scenarios"] += 1
            
            suite_result["execution_time"] = time.time() - start_time
            suite_result["end_time"] = datetime.now().isoformat()
            suite_result["success_rate"] = (suite_result["passed_scenarios"] / suite_result["total_scenarios"]) * 100
            
            # Save results
            self._save_test_results(suite_result)
            
            self.logger.info(f"Test suite completed: {suite_name} - Success Rate: {suite_result['success_rate']:.1f}%")
            
            return suite_result
            
        except Exception as e:
            self.logger.error(f"Failed to run test suite: {str(e)}")
            return {"error": str(e)}
    
    def _save_test_results(self, results: Dict[str, Any]):
        """Save test results to JSON file"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_file = os.path.join(self.logs_dir, f"test_results_{timestamp}.json")
            
            with open(results_file, 'w', encoding='utf-8') as file:
                json.dump(results, file, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Test results saved to: {results_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save test results: {str(e)}")
    
    def generate_test_report(self, results: Dict[str, Any]) -> str:
        """Generate HTML test report"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(self.logs_dir, f"test_report_{timestamp}.html")
            
            html_content = self._create_html_report(results)
            
            with open(report_file, 'w', encoding='utf-8') as file:
                file.write(html_content)
            
            self.logger.info(f"Test report generated: {report_file}")
            return report_file
            
        except Exception as e:
            self.logger.error(f"Failed to generate test report: {str(e)}")
            return ""
    
    def _create_html_report(self, results: Dict[str, Any]) -> str:
        """Create HTML report content"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Selenium Test Report - {results.get('suite_name', 'Test Suite')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .summary {{ margin: 20px 0; }}
                .scenario {{ margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }}
                .scenario-header {{ background-color: #e9e9e9; padding: 10px; }}
                .pass {{ color: green; }}
                .fail {{ color: red; }}
                .error {{ color: orange; }}
                .steps {{ margin: 10px; }}
                .step {{ margin: 5px 0; padding: 5px; border-left: 3px solid #ddd; }}
                .step.pass {{ border-left-color: green; }}
                .step.fail {{ border-left-color: red; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Selenium Test Report</h1>
                <h2>{results.get('suite_name', 'Test Suite')}</h2>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p>Platform: {platform.system()} {platform.release()}</p>
            </div>
            
            <div class="summary">
                <h3>Summary</h3>
                <p>Total Scenarios: {results.get('total_scenarios', 0)}</p>
                <p class="pass">Passed: {results.get('passed_scenarios', 0)}</p>
                <p class="fail">Failed: {results.get('failed_scenarios', 0)}</p>
                <p>Success Rate: {results.get('success_rate', 0):.1f}%</p>
                <p>Execution Time: {results.get('execution_time', 0):.2f} seconds</p>
            </div>
        """
        
        # Add scenario details
        for scenario in results.get('scenario_results', []):
            status_class = scenario['status'].lower()
            html += f"""
            <div class="scenario">
                <div class="scenario-header">
                    <h3 class="{status_class}">{scenario['scenario_name']} - {scenario['status']}</h3>
                    <p>Execution Time: {scenario['execution_time']:.2f} seconds</p>
                </div>
                <div class="steps">
            """
            
            for step in scenario.get('steps', []):
                step_class = 'pass' if step['success'] else 'fail'
                html += f"""
                <div class="step {step_class}">
                    <strong>Step {step['step_number']}:</strong> {step['action']}
                    {f" - Error: {step['error']}" if step.get('error') else ""}
                </div>
                """
            
            html += "</div></div>"
        
        html += "</body></html>"
        return html
    
    def cleanup(self):
        """Clean up resources and close browser"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.wait = None
                self.logger.info("Browser closed and resources cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
    
    def generate_reply(self, messages=None, **kwargs):
        """Generate reply for AutoGen agent communication"""
        if not messages:
            return "No message to respond to."
        
        try:
            last_message = messages[-1]['content']
            
            # Parse the message to determine what test action to perform
            if "run test" in last_message.lower():
                # Extract test file path or test scenario from message
                return self._handle_test_request(last_message)
            elif "setup browser" in last_message.lower():
                # Extract browser type from message
                return self._handle_browser_setup(last_message)
            elif "navigate to" in last_message.lower():
                # Extract URL from message
                return self._handle_navigation_request(last_message)
            else:
                return f"Selenium Test Agent received: {last_message}. Available commands: 'run test', 'setup browser', 'navigate to'"
                
        except Exception as e:
            return f"Error processing message: {str(e)}"
    
    def _handle_test_request(self, message: str) -> str:
        """Handle test execution request"""
        try:
            # Simple parsing - in production, use more sophisticated NLP
            if "file:" in message:
                file_path = message.split("file:")[1].strip()
                results = self.run_test_suite(file_path)
                
                if "error" in results:
                    return f"Test execution failed: {results['error']}"
                
                report_file = self.generate_test_report(results)
                return f"Test suite completed. Success rate: {results.get('success_rate', 0):.1f}%. Report: {report_file}"
            else:
                return "Please specify test file path using 'file: path/to/test.json'"
                
        except Exception as e:
            return f"Error handling test request: {str(e)}"
    
    def _handle_browser_setup(self, message: str) -> str:
        """Handle browser setup request"""
        try:
            browser = "chrome"  # default
            headless = False
            
            if "firefox" in message.lower():
                browser = "firefox"
            elif "edge" in message.lower():
                browser = "edge"
            
            if "headless" in message.lower():
                headless = True
            
            success = self.setup_browser(browser, headless)
            
            if success:
                return f"Browser {browser} setup completed successfully (headless: {headless})"
            else:
                return f"Failed to setup browser {browser}"
                
        except Exception as e:
            return f"Error setting up browser: {str(e)}"
    
    def _handle_navigation_request(self, message: str) -> str:
        """Handle navigation request"""
        try:
            # Extract URL from message
            words = message.split()
            url = None
            
            for word in words:
                if word.startswith("http"):
                    url = word
                    break
            
            if not url:
                return "Please provide a valid URL starting with http:// or https://"
            
            if not self.driver:
                self.setup_browser()
            
            success = self.navigate_to(url)
            
            if success:
                return f"Successfully navigated to {url}"
            else:
                return f"Failed to navigate to {url}"
                
        except Exception as e:
            return f"Error handling navigation: {str(e)}"

# Example usage and test scenario templates
def create_example_test_scenario():
    """Create example test scenario JSON file"""
    example_scenario = {
        "suite_name": "Example Web Test Suite",
        "browser": "chrome",
        "headless": False,
        "scenarios": [
            {
                "name": "Login Test",
                "description": "Test user login functionality",
                "stop_on_failure": True,
                "steps": [
                    {
                        "action": "navigate",
                        "url": "https://example.com/login"
                    },
                    {
                        "action": "type",
                        "locator_type": "id",
                        "locator_value": "username",
                        "text": "testuser"
                    },
                    {
                        "action": "type",
                        "locator_type": "id",
                        "locator_value": "password",
                        "text": "testpass"
                    },
                    {
                        "action": "click",
                        "locator_type": "xpath",
                        "locator_value": "//button[@type='submit']"
                    },
                    {
                        "action": "wait",
                        "locator_type": "class",
                        "locator_value": "dashboard",
                        "condition": "visible",
                        "timeout": 10
                    },
                    {
                        "action": "verify_text",
                        "locator_type": "class",
                        "locator_value": "welcome-message",
                        "expected_text": "Welcome"
                    },
                    {
                        "action": "screenshot",
                        "filename": "login_success"
                    }
                ]
            }
        ]
    }
    
    # Save example to test_data directory
    os.makedirs("test_data", exist_ok=True)
    with open("test_data/example_test_suite.json", "w") as f:
        json.dump(example_scenario, f, indent=2)
    
    return "test_data/example_test_suite.json"

if __name__ == "__main__":
    # Example usage
    agent = SeleniumTestAgent()
    
    # Create example test scenario
    test_file = create_example_test_scenario()
    print(f"Example test scenario created: {test_file}")
    
    # Run test suite
    # results = agent.run_test_suite(test_file)
    # print(f"Test results: {results}")
    
    # Cleanup
    agent.cleanup()