#!/bin/bash

echo "🚀 Starting VRepair Customer Management Service with Oracle Database"
echo "=================================================================="

# Clean up any existing processes
echo "🧹 Cleaning up existing processes..."
pkill -f customer-management-service 2>/dev/null
sleep 3

# Navigate to service directory
cd /Users/<USER>/Downloads/Vrepair_Arch/customer-management-service

# Ensure Oracle container is running
echo "🐳 Checking Oracle Database container..."
if ! docker ps | grep -q vrepair-oracle-xe; then
    echo "📦 Starting Oracle Database container..."
    docker-compose up -d
    echo "⏳ Waiting for Oracle to initialize (30 seconds)..."
    sleep 30
else
    echo "✅ Oracle Database container is already running"
fi

# Test Oracle connection
echo "🔍 Testing Oracle Database connection..."
if docker exec vrepair-oracle-xe sqlplus -s 'system/VRepair123!@XEPDB1' <<< "SELECT 1 FROM DUAL;" > /dev/null 2>&1; then
    echo "✅ Oracle Database connection successful"
else
    echo "⚠️  Oracle Database not ready yet, waiting additional 20 seconds..."
    sleep 20
fi

# Start the service with Oracle database but without security for demo
echo "🌟 Starting Customer Management Service with Oracle Database..."
echo "📊 Database: Oracle XE (Docker container)"
echo "🌐 Service will be available at: http://localhost:8091/customer-service"
echo "📖 Swagger UI: http://localhost:8091/customer-service/swagger-ui/index.html"
echo "💾 Oracle Console: http://localhost:5500/em (admin/VRepair123!)"
echo ""

# Create modified Oracle configuration for demo (without security)
export DB_HOST=localhost
export DB_PORT=1521
export DB_SERVICE_NAME=XEPDB1
export DB_USERNAME=system
export DB_PASSWORD=VRepair123!

# Start service in background with Oracle database
nohup java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar \
  --spring.profiles.active=oracle \
  --server.port=8091 \
  --spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration \
  --spring.datasource.url=jdbc:oracle:thin:@${DB_HOST}:${DB_PORT}/${DB_SERVICE_NAME} \
  --spring.datasource.username=${DB_USERNAME} \
  --spring.datasource.password=${DB_PASSWORD} \
  --spring.jpa.hibernate.ddl-auto=create-drop \
  --logging.level.root=INFO > oracle-demo-service.log 2>&1 &

SERVICE_PID=$!
echo "🔄 Service starting with PID: $SERVICE_PID"
echo "⏳ Waiting for service to initialize with Oracle..."

# Wait for service to start (Oracle takes longer)
for i in {1..30}; do
    if curl -s http://localhost:8091/customer-service/actuator/health > /dev/null 2>&1; then
        echo "✅ Service is ready with Oracle Database!"
        break
    fi
    echo "   Attempt $i/30: Waiting for Oracle service..."
    sleep 3
done

# Test if service is running
if curl -s http://localhost:8091/customer-service/actuator/health > /dev/null 2>&1; then
    echo ""
    echo "🎉 SUCCESS! Service is running with Oracle Database!"
    echo "=================================================================="
    echo "📋 Demo URLs:"
    echo "   🏥 Health Check:  http://localhost:8091/customer-service/actuator/health"
    echo "   📖 Swagger UI:    http://localhost:8091/customer-service/swagger-ui/index.html"
    echo "   💾 Oracle EM:     http://localhost:5500/em"
    echo "   📊 Metrics:       http://localhost:8091/customer-service/actuator/metrics"
    echo ""
    echo "🔑 Oracle Database Access:"
    echo "   Host: localhost:1521"
    echo "   Service: XEPDB1"
    echo "   Username: system"
    echo "   Password: VRepair123!"
    echo ""
    echo "🔑 Oracle Enterprise Manager:"
    echo "   URL: http://localhost:5500/em"
    echo "   Username: admin"
    echo "   Password: VRepair123!"
    echo ""
    echo "🎯 Opening Swagger UI and Oracle EM in browser..."
    sleep 2
    open http://localhost:8091/customer-service/swagger-ui/index.html
    open http://localhost:5500/em
    echo ""
    echo "🛑 To stop the service, run: kill $SERVICE_PID"
    echo "=================================================================="
    
    # Show current health status
    echo "📊 Current Service Health:"
    curl -s http://localhost:8091/customer-service/actuator/health | jq '.'
    
else
    echo "❌ Service failed to start with Oracle. Check oracle-demo-service.log for details."
    echo "📋 Recent logs:"
    tail -20 oracle-demo-service.log
fi
