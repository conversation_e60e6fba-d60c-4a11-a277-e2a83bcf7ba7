# PRD_mop.py - Method of Procedure for Product Requirements Document Generation

PRD_MOP_CONTENT = """
MOP: Product Requirements Document (PRD) Generation

Objective:
The purpose of this MOP is to outline the comprehensive steps for generating a Product Requirements Document (PRD) for software applications, including user stories, acceptance criteria, technical requirements, and integration with project management tools like Jira.

Prerequisites:
1. Access to project stakeholders and business requirements
2. Understanding of the target application domain and user base
3. Access to Jira API credentials for ticket creation and management
4. Technical architecture knowledge for system requirements
5. User research data and market analysis (if available)

Steps:
1. **Project Overview & Scope Definition**
   - Define product vision and objectives
   - Identify target users and personas
   - Establish project scope and boundaries
   - Define success metrics and KPIs

2. **Requirements Gathering**
   - Collect functional requirements from stakeholders
   - Identify non-functional requirements (performance, security, scalability)
   - Document business rules and constraints
   - Gather integration requirements with external systems

3. **User Story Creation**
   - Write user stories in standard format: "As a [user], I want [goal] so that [benefit]"
   - Prioritize user stories using MoSCoW method (Must have, Should have, Could have, Won't have)
   - Define story points and effort estimation
   - Create epic-level groupings for related stories

4. **Acceptance Criteria Definition**
   - Define clear, testable acceptance criteria for each user story
   - Specify Given-When-Then scenarios for behavior-driven development
   - Include edge cases and error handling scenarios
   - Define data validation and security requirements

5. **Technical Requirements Specification**
   - Define system architecture and technology stack
   - Specify API requirements and data models
   - Document security and compliance requirements
   - Define performance and scalability requirements
   - Specify integration points and dependencies

6. **Jira Integration & Project Setup**
   - Create Jira project for PRD tracking
   - Generate Jira epics for major feature groups
   - Create individual Jira stories with acceptance criteria
   - Set up project workflows and issue types
   - Configure project permissions and notifications

7. **Documentation Generation**
   - Generate comprehensive PRD document in Markdown format
   - Include all sections: Overview, Requirements, User Stories, Technical Specs
   - Create visual diagrams and wireframes (if applicable)
   - Generate project timeline and milestone definitions

8. **Review & Validation**
   - Conduct stakeholder review sessions
   - Validate technical feasibility with development team
   - Ensure alignment with business objectives
   - Update documentation based on feedback

Post-Generation Actions:
1. Distribute PRD to all project stakeholders
2. Set up regular review cycles for requirement updates
3. Establish change management process for requirement modifications
4. Create traceability matrix linking requirements to implementation
5. Set up monitoring for requirement compliance during development

Output Deliverables:
- Comprehensive PRD document (prd.md)
- Jira project with structured epics and stories
- Requirements traceability matrix
- Project timeline and milestone definitions
- Stakeholder communication plan
"""
