browser: chrome
headless: false
implicit_wait: 10
explicit_wait: 30
page_load_timeout: 60
window_size:
  width: 1920
  height: 1080
retry_attempts: 3
retry_delay: 2
screenshot_on_failure: true
environment: test

# Environment-specific configurations
environments:
  test:
    base_url: "https://test.example.com"
    timeout: 30
  staging:
    base_url: "https://staging.example.com"
    timeout: 45
  production:
    base_url: "https://example.com"
    timeout: 60

# Test data configurations
test_data:
  users:
    - username: "testuser1"
      password: "testpass1"
      email: "<EMAIL>"
    - username: "testuser2"
      password: "testpass2"
      email: "<EMAIL>"
  
  upload_files:
    - "test_data/sample_upload.txt"
    - "test_data/sample_image.png"

# Browser-specific options
browser_options:
  chrome:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"
    - "--disable-gpu"
    - "--disable-extensions"
  firefox:
    - "--safe-mode"
  edge:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"