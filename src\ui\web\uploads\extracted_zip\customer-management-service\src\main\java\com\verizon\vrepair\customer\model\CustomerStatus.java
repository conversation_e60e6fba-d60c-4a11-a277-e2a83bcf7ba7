package com.verizon.vrepair.customer.model;

/**
 * Customer status enumeration.
 * Replaces legacy C++ customer status constants with type-safe enum.
 * 
 * Legacy C++ mapping:
 * - A -> ACTIVE
 * - I -> INACTIVE
 * - P -> PENDING
 * - S -> SUSPENDED
 * - T -> TERMINATED
 */
public enum CustomerStatus {
    ACTIVE("Active customer with full service"),
    INACTIVE("Inactive customer - service suspended"),
    PENDING("Pending activation"),
    SUSPENDED("Service suspended for non-payment"),
    TERMINATED("Service permanently terminated");
    
    private final String description;
    
    CustomerStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Convert from legacy C++ customer status codes.
     * Maintains backward compatibility with legacy system.
     * 
     * @param legacyCode Legacy customer status code (A, I, P, S, T)
     * @return Corresponding CustomerStatus enum
     * @throws IllegalArgumentException if legacy code is not recognized
     */
    public static CustomerStatus fromLegacyCode(String legacyCode) {
        if (legacyCode == null || legacyCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Legacy customer status code cannot be null or empty");
        }
        
        return switch (legacyCode.trim().toUpperCase()) {
            case "A" -> ACTIVE;
            case "I" -> INACTIVE;
            case "P" -> PENDING;
            case "S" -> SUSPENDED;
            case "T" -> TERMINATED;
            default -> throw new IllegalArgumentException("Unknown legacy customer status code: " + legacyCode);
        };
    }
    
    /**
     * Convert to legacy C++ customer status code.
     * Used for integration with legacy systems that still expect old codes.
     * 
     * @return Legacy customer status code
     */
    public String toLegacyCode() {
        return switch (this) {
            case ACTIVE -> "A";
            case INACTIVE -> "I";
            case PENDING -> "P";
            case SUSPENDED -> "S";
            case TERMINATED -> "T";
        };
    }
    
    /**
     * Check if customer can be activated from current status.
     * Implements legacy business rules for status transitions.
     * 
     * @return true if activation is allowed from current status
     */
    public boolean canActivate() {
        return switch (this) {
            case INACTIVE, PENDING, SUSPENDED -> true;
            case ACTIVE, TERMINATED -> false;
        };
    }
    
    /**
     * Check if customer can be deactivated from current status.
     * Implements legacy business rules for status transitions.
     * 
     * @return true if deactivation is allowed from current status
     */
    public boolean canDeactivate() {
        return switch (this) {
            case ACTIVE, PENDING -> true;
            case INACTIVE, SUSPENDED, TERMINATED -> false;
        };
    }
    
    /**
     * Check if customer can be suspended from current status.
     * Implements legacy business rules for status transitions.
     * 
     * @return true if suspension is allowed from current status
     */
    public boolean canSuspend() {
        return switch (this) {
            case ACTIVE, INACTIVE -> true;
            case PENDING, SUSPENDED, TERMINATED -> false;
        };
    }
    
    /**
     * Check if customer can be terminated from current status.
     * Implements legacy business rules for status transitions.
     * 
     * @return true if termination is allowed from current status
     */
    public boolean canTerminate() {
        return switch (this) {
            case ACTIVE, INACTIVE, SUSPENDED -> true;
            case PENDING, TERMINATED -> false;
        };
    }
    
    /**
     * Check if customer is in an active service state.
     * Used for business logic that requires active service.
     * 
     * @return true if customer has active service
     */
    public boolean isActiveService() {
        return this == ACTIVE;
    }
}
