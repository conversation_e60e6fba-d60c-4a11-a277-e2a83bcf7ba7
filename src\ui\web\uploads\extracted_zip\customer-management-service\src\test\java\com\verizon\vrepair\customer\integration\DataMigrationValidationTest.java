package com.verizon.vrepair.customer.integration;

import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import com.verizon.vrepair.customer.repository.CustomerRepository;
import com.verizon.vrepair.customer.service.CustomerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Data migration validation tests for Customer Service.
 * Tests production-like data volumes and migration scenarios.
 * Validates data integrity and consistency after migration from legacy C++ system.
 */
@SpringBootTest
@ActiveProfiles("integration-test")
@Transactional
class DataMigrationValidationTest {
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    /**
     * Test large volume data migration (simulating production scale).
     * Validates system can handle production-like customer volumes.
     */
    @Test
    void largeBatchMigration_ProductionVolume_HandlesSuccessfully() {
        // Given - Production-like volume (100,000+ customers)
        int batchSize = 1000;
        int numberOfBatches = 10; // Total: 10,000 customers for test performance
        
        long startTime = System.currentTimeMillis();
        
        // When - Migrate customers in batches
        for (int batch = 0; batch < numberOfBatches; batch++) {
            List<Customer> batchCustomers = createMigrationBatch(batch, batchSize);
            
            // Simulate batch processing
            batchCustomers.forEach(customer -> {
                try {
                    customerService.createCustomer(customer);
                } catch (Exception e) {
                    // Log but continue (production migration would handle failures)
                    System.err.println("Failed to migrate customer: " + e.getMessage());
                }
            });
            
            // Progress tracking (like production migration)
            if (batch % 2 == 0) {
                long currentTime = System.currentTimeMillis();
                long elapsed = currentTime - startTime;
                int processed = (batch + 1) * batchSize;
                double rate = processed / (elapsed / 1000.0);
                
                System.out.printf("Migration progress: %d/%d customers (%.1f customers/sec)%n", 
                                processed, numberOfBatches * batchSize, rate);
            }
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        // Then - Validate migration results
        long totalCustomers = customerRepository.count();
        assertThat(totalCustomers).isEqualTo(numberOfBatches * batchSize);
        
        // Performance validation (should handle at least 100 customers/second)
        double migrationRate = totalCustomers / (totalTime / 1000.0);
        assertThat(migrationRate).isGreaterThan(100.0);
        
        System.out.printf("Migration completed: %d customers in %d ms (%.1f customers/sec)%n", 
                          totalCustomers, totalTime, migrationRate);
    }
    
    /**
     * Test concurrent migration processing.
     * Validates system can handle multiple migration threads.
     */
    @Test
    void concurrentMigration_MultipleThreads_MaintainsDataIntegrity() throws Exception {
        // Given - Multiple migration threads
        int numberOfThreads = 5;
        int customersPerThread = 200;
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        
        // When - Run concurrent migrations
        List<CompletableFuture<Integer>> futures = new ArrayList<>();
        
        for (int thread = 0; thread < numberOfThreads; thread++) {
            final int threadId = thread;
            
            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                int successCount = 0;
                
                for (int i = 0; i < customersPerThread; i++) {
                    try {
                        Customer customer = createMigrationCustomer(threadId, i);
                        customerService.createCustomer(customer);
                        successCount++;
                    } catch (Exception e) {
                        // Expected in concurrent scenarios due to potential conflicts
                    }
                }
                
                return successCount;
            }, executor);
            
            futures.add(future);
        }
        
        // Wait for all threads to complete
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        allFutures.get();
        
        executor.shutdown();
        
        // Then - Validate results
        int totalSuccessful = futures.stream()
                .mapToInt(CompletableFuture::join)
                .sum();
        
        long actualCount = customerRepository.count();
        
        assertThat(actualCount).isEqualTo(totalSuccessful);
        assertThat(totalSuccessful).isGreaterThan(numberOfThreads * customersPerThread * 0.8); // At least 80% success
        
        // Validate no duplicate customer codes
        List<String> customerCodes = customerRepository.findAll().stream()
                .map(Customer::getCustomerCode)
                .toList();
        
        long uniqueCount = customerCodes.stream().distinct().count();
        assertThat(uniqueCount).isEqualTo(customerCodes.size());
    }
    
    /**
     * Test data consistency validation after migration.
     * Ensures all legacy data fields are properly migrated and validated.
     */
    @Test
    void dataConsistencyValidation_LegacyFields_AllFieldsMigratedCorrectly() {
        // Given - Legacy customer data with all fields populated
        List<LegacyCustomerData> legacyData = createLegacyCustomerDataSet();
        
        // When - Migrate legacy data
        List<Customer> migratedCustomers = new ArrayList<>();
        for (LegacyCustomerData legacy : legacyData) {
            Customer customer = convertLegacyToCustomer(legacy);
            Customer migrated = customerService.createCustomer(customer);
            migratedCustomers.add(migrated);
        }
        
        // Then - Validate field mapping consistency
        for (int i = 0; i < legacyData.size(); i++) {
            LegacyCustomerData legacy = legacyData.get(i);
            Customer migrated = migratedCustomers.get(i);
            
            // Validate core fields
            assertThat(migrated.getBillingTelephoneNumber()).isEqualTo(legacy.phoneNumber);
            assertThat(migrated.getServiceAddress()).isEqualTo(legacy.serviceAddress);
            assertThat(migrated.getCustomerName()).isEqualTo(legacy.customerName);
            assertThat(migrated.getAccountNumber()).isEqualTo(legacy.accountNumber);
            
            // Validate enum conversions
            assertThat(migrated.getCustomerType()).isEqualTo(
                    CustomerType.fromLegacyCode(legacy.customerTypeCode));
            assertThat(migrated.getCustomerStatus()).isEqualTo(
                    CustomerStatus.fromLegacyCode(legacy.statusCode));
            
            // Validate optional fields
            if (legacy.contactPhone != null) {
                assertThat(migrated.getContactPhone()).isEqualTo(legacy.contactPhone);
            }
            
            // Validate system fields
            assertThat(migrated.getCreatedDate()).isNotNull();
            assertThat(migrated.getModifiedDate()).isNotNull();
            assertThat(migrated.getCustomerCode()).isNotNull();
        }
    }
    
    /**
     * Test rollback scenario validation.
     * Validates system can handle migration rollback requirements.
     */
    @Test
    void migrationRollback_PartialFailure_HandlesGracefully() {
        // Given - Batch with some valid and some invalid customers
        List<Customer> mixedBatch = createMixedValidityBatch(100);
        
        List<Customer> successful = new ArrayList<>();
        List<String> failures = new ArrayList<>();
        
        // When - Process batch with failure handling
        for (Customer customer : mixedBatch) {
            try {
                Customer created = customerService.createCustomer(customer);
                successful.add(created);
            } catch (Exception e) {
                failures.add(customer.getBillingTelephoneNumber() + ": " + e.getMessage());
            }
        }
        
        // Then - Validate partial success handling
        assertThat(successful.size()).isGreaterThan(0);
        assertThat(failures.size()).isGreaterThan(0);
        
        // Validate successful customers are properly stored
        for (Customer customer : successful) {
            Optional<Customer> found = customerRepository.findById(customer.getCustomerCode());
            assertThat(found).isPresent();
        }
        
        // Validate total count matches successful migrations
        long totalCount = customerRepository.count();
        assertThat(totalCount).isEqualTo(successful.size());
        
        System.out.printf("Migration batch results: %d successful, %d failed%n", 
                          successful.size(), failures.size());
        
        // Log failures for analysis (production would log to migration report)
        failures.forEach(failure -> System.out.println("Migration failure: " + failure));
    }
    
    /**
     * Test reference data integrity during migration.
     * Validates foreign key relationships and data consistency.
     */
    @Test
    void referenceDataIntegrity_ForeignKeyConstraints_MaintainedDuringMigration() {
        // Given - Customers with reference data relationships
        List<Customer> customersWithReferences = createCustomersWithReferenceData();
        
        // When - Migrate customers
        List<Customer> migrated = new ArrayList<>();
        for (Customer customer : customersWithReferences) {
            Customer created = customerService.createCustomer(customer);
            migrated.add(created);
        }
        
        // Then - Validate reference data integrity
        for (Customer customer : migrated) {
            // Validate customer type reference
            assertThat(customer.getCustomerType()).isNotNull();
            assertThat(customer.getCustomerType()).isIn(CustomerType.values());
            
            // Validate status reference
            assertThat(customer.getCustomerStatus()).isNotNull();
            assertThat(customer.getCustomerStatus()).isIn(CustomerStatus.values());
            
            // Validate service class code format (legacy requirement)
            if (customer.getServiceClassCode() != null) {
                assertThat(customer.getServiceClassCode()).matches("[A-Z]{3}\\d{3}");
            }
            
            // Validate maintenance level values (legacy enumeration)
            if (customer.getMaintenanceLevel() != null) {
                assertThat(customer.getMaintenanceLevel())
                        .isIn("STANDARD", "PREMIUM", "BASIC", "ENHANCED");
            }
        }
        
        // Validate no orphaned records
        long customerCount = customerRepository.count();
        assertThat(customerCount).isEqualTo(customersWithReferences.size());
    }
    
    // Helper methods for test data creation
    
    private List<Customer> createMigrationBatch(int batchNumber, int batchSize) {
        return IntStream.range(0, batchSize)
                .mapToObj(i -> createMigrationCustomer(batchNumber, i))
                .toList();
    }
    
    private Customer createMigrationCustomer(int batchNumber, int customerNumber) {
        String suffix = String.format("%03d%04d", batchNumber, customerNumber);
        
        Customer customer = new Customer();
        customer.setBillingTelephoneNumber("555" + suffix.substring(0, 7));
        customer.setServiceAddress(String.format("Address %d, City %d, ST %s", 
                                                customerNumber, batchNumber, suffix.substring(0, 5)));
        customer.setCustomerName(String.format("Migration Customer %d-%d", batchNumber, customerNumber));
        customer.setCustomerType(CustomerType.values()[customerNumber % CustomerType.values().length]);
        customer.setAccountNumber("MIG" + suffix);
        customer.setServiceClassCode("MIG001");
        customer.setMaintenanceLevel("STANDARD");
        customer.setCustomerStatus(CustomerStatus.ACTIVE);
        
        return customer;
    }
    
    private List<LegacyCustomerData> createLegacyCustomerDataSet() {
        List<LegacyCustomerData> legacyData = new ArrayList<>();
        
        // Residential customers
        legacyData.add(new LegacyCustomerData("**********", "123 Main St", 
                "John Smith", "RES", "A", "RESACC001", "**********"));
        
        // Business customers
        legacyData.add(new LegacyCustomerData("**********", "456 Business Ave", 
                "Acme Corporation", "BUS", "A", "BUSACC001", "**********"));
        
        // Government customers
        legacyData.add(new LegacyCustomerData("**********", "789 Government Way", 
                "City Hall", "GOV", "A", "GOVACC001", null));
        
        // Wholesale customers
        legacyData.add(new LegacyCustomerData("5554567890", "321 Wholesale Blvd", 
                "Wholesale Inc", "WHO", "I", "WHOACC001", "5554567891"));
        
        return legacyData;
    }
    
    private Customer convertLegacyToCustomer(LegacyCustomerData legacy) {
        Customer customer = new Customer();
        customer.setBillingTelephoneNumber(legacy.phoneNumber);
        customer.setServiceAddress(legacy.serviceAddress);
        customer.setCustomerName(legacy.customerName);
        customer.setCustomerType(CustomerType.fromLegacyCode(legacy.customerTypeCode));
        customer.setCustomerStatus(CustomerStatus.fromLegacyCode(legacy.statusCode));
        customer.setAccountNumber(legacy.accountNumber);
        customer.setContactPhone(legacy.contactPhone);
        customer.setServiceClassCode("LEG001");
        customer.setMaintenanceLevel("STANDARD");
        
        return customer;
    }
    
    private List<Customer> createMixedValidityBatch(int size) {
        List<Customer> batch = new ArrayList<>();
        
        for (int i = 0; i < size; i++) {
            Customer customer = new Customer();
            
            if (i % 10 == 0) {
                // Invalid customer (missing required field)
                customer.setBillingTelephoneNumber(null); // Will cause validation error
                customer.setServiceAddress("Invalid Address " + i);
                customer.setCustomerName("Invalid Customer " + i);
                customer.setCustomerType(CustomerType.RESIDENTIAL);
            } else if (i % 15 == 0) {
                // Invalid phone number
                customer.setBillingTelephoneNumber("invalid-phone");
                customer.setServiceAddress("Address " + i);
                customer.setCustomerName("Customer " + i);
                customer.setCustomerType(CustomerType.RESIDENTIAL);
            } else {
                // Valid customer
                customer.setBillingTelephoneNumber("555" + String.format("%07d", i));
                customer.setServiceAddress("Address " + i);
                customer.setCustomerName("Customer " + i);
                customer.setCustomerType(CustomerType.values()[i % CustomerType.values().length]);
                customer.setAccountNumber("ACC" + String.format("%06d", i));
                customer.setServiceClassCode("STD001");
                customer.setMaintenanceLevel("STANDARD");
            }
            
            batch.add(customer);
        }
        
        return batch;
    }
    
    private List<Customer> createCustomersWithReferenceData() {
        List<Customer> customers = new ArrayList<>();
        
        // Create customers with various reference data combinations
        for (CustomerType type : CustomerType.values()) {
            for (CustomerStatus status : CustomerStatus.values()) {
                Customer customer = new Customer();
                customer.setBillingTelephoneNumber("555" + 
                        String.format("%07d", type.ordinal() * 10 + status.ordinal()));
                customer.setServiceAddress(String.format("Address for %s %s", type, status));
                customer.setCustomerName(String.format("Customer %s %s", type, status));
                customer.setCustomerType(type);
                customer.setCustomerStatus(status);
                customer.setAccountNumber(String.format("%s%s%03d", 
                        type.name().substring(0, 3), 
                        status.name().substring(0, 3), 
                        customers.size()));
                customer.setServiceClassCode("REF001");
                customer.setMaintenanceLevel("STANDARD");
                
                customers.add(customer);
            }
        }
        
        return customers;
    }
    
    /**
     * Legacy customer data structure for migration testing.
     */
    private static class LegacyCustomerData {
        final String phoneNumber;
        final String serviceAddress;
        final String customerName;
        final String customerTypeCode;
        final String statusCode;
        final String accountNumber;
        final String contactPhone;
        
        LegacyCustomerData(String phoneNumber, String serviceAddress, String customerName,
                          String customerTypeCode, String statusCode, String accountNumber,
                          String contactPhone) {
            this.phoneNumber = phoneNumber;
            this.serviceAddress = serviceAddress;
            this.customerName = customerName;
            this.customerTypeCode = customerTypeCode;
            this.statusCode = statusCode;
            this.accountNumber = accountNumber;
            this.contactPhone = contactPhone;
        }
    }
}
