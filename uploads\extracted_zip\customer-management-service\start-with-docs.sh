#!/bin/bash

# Customer Management Service - Start with Documentation
# This script starts the service and provides links to access the API documentation

echo "🚀 Starting Customer Management Service with API Documentation..."
echo ""

# Check if <PERSON><PERSON> is available
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed. Please install <PERSON><PERSON> to run the service."
    exit 1
fi

# Set the port and context path
PORT=8080
CONTEXT_PATH="/customer-service"
BASE_URL="http://localhost:${PORT}${CONTEXT_PATH}"

echo "📋 Service Configuration:"
echo "   Port: ${PORT}"
echo "   Context Path: ${CONTEXT_PATH}"
echo "   Base URL: ${BASE_URL}"
echo ""

echo "🔧 Building the application..."
mvn clean compile -q

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the Maven output above."
    exit 1
fi

echo ""
echo "🌐 API Documentation will be available at:"
echo "   📖 Swagger UI:     ${BASE_URL}/swagger-ui.html"
echo "   📄 OpenAPI Spec:   ${BASE_URL}/v3/api-docs"
echo "   📝 OpenAPI YAML:   ${BASE_URL}/v3/api-docs.yaml"
echo "   ❤️  Health Check:  ${BASE_URL}/actuator/health"
echo ""

echo "🔐 Authentication Information:"
echo "   All API endpoints require OAuth 2.0 JWT authentication"
echo "   Include Bearer token in Authorization header"
echo "   Roles: USER, TECHNICIAN, ADMIN, SUPERVISOR"
echo ""

echo "📚 Documentation Files:"
echo "   📖 API Guide:      ./API_Documentation.md"
echo "   📋 Project Summary: ../Customer_Service_MVP_Development_Summary.md"
echo ""

echo "🏃 Starting the service..."
echo "   Press Ctrl+C to stop the service"
echo "   The service will start in a few seconds..."
echo ""

# Start the Spring Boot application
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dserver.port=${PORT} -Dserver.servlet.context-path=${CONTEXT_PATH}"
