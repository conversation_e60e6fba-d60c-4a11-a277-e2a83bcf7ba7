package com.verizon.vrepair.customer.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Customer entity representing the CUSTOMERS table.
 * Modernized version of legacy C++ Customer data structure.
 * 
 * Maps to legacy C++ struct Customer in customer_mgmt.h
 * Replaces Pro*C embedded SQL with JPA annotations.
 */
@Entity
@Table(name = "CUSTOMERS", indexes = {
    @Index(name = "idx_customer_phone", columnList = "BILLING_TELEPHONE_NUM"),
    @Index(name = "idx_customer_name", columnList = "CUSTOMER_NAME"),
    @Index(name = "idx_customer_account", columnList = "ACCOUNT_NUMBER"),
    @Index(name = "idx_customer_status", columnList = "CUSTOMER_STATUS"),
    @Index(name = "idx_customer_type", columnList = "CUSTOMER_TYPE")
})
@EntityListeners(AuditingEntityListener.class)
public class Customer {
    
    @Id
    @Column(name = "CUSTOMER_CODE", length = 20)
    @NotNull
    private String customerCode;
    
    @Column(name = "BILLING_TELEPHONE_NUM", length = 15)
    @Pattern(regexp = "^\\d{10}$", message = "Phone number must be 10 digits")
    private String billingTelephoneNumber;
    
    @Column(name = "SERVICE_ADDRESS", length = 100)
    @NotNull
    @Size(min = 1, max = 100, message = "Service address must be between 1 and 100 characters")
    private String serviceAddress;
    
    @Column(name = "CUSTOMER_NAME", length = 50)
    @Size(max = 50, message = "Customer name must not exceed 50 characters")
    private String customerName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "CUSTOMER_TYPE", length = 20)
    @NotNull
    private CustomerType customerType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "CUSTOMER_STATUS", length = 20)
    @NotNull
    private CustomerStatus customerStatus = CustomerStatus.ACTIVE;
    
    @Column(name = "ACCOUNT_NUMBER", length = 20)
    private String accountNumber;
    
    @Column(name = "SERVICE_CLASS_CODE", length = 10)
    private String serviceClassCode;
    
    @Column(name = "MAINTENANCE_LEVEL", length = 20)
    private String maintenanceLevel;
    
    @Column(name = "CONTACT_PHONE", length = 15)
    @Pattern(regexp = "^\\d{10}$|^$", message = "Contact phone must be 10 digits or empty")
    private String contactPhone;
    
    @Column(name = "ALTERNATE_CONTACT", length = 15)
    @Pattern(regexp = "^\\d{10}$|^$", message = "Alternate contact must be 10 digits or empty")
    private String alternateContact;
    
    @Column(name = "EMAIL_ADDRESS", length = 100)
    private String emailAddress;
    
    @Column(name = "SPECIAL_INSTRUCTIONS", length = 500)
    private String specialInstructions;
    
    @CreatedDate
    @Column(name = "CREATED_DATE", nullable = false, updatable = false)
    private LocalDateTime createdDate;
    
    @LastModifiedDate
    @Column(name = "MODIFIED_DATE", nullable = false)
    private LocalDateTime modifiedDate;
    
    @Column(name = "CREATED_BY", length = 50, updatable = false)
    private String createdBy;
    
    @Column(name = "MODIFIED_BY", length = 50)
    private String modifiedBy;
    
    @Version
    @Column(name = "VERSION")
    private Long version;
    
    // Constructors
    public Customer() {}
    
    public Customer(String customerCode, String billingTelephoneNumber, 
                   String serviceAddress, CustomerType customerType) {
        this.customerCode = customerCode;
        this.billingTelephoneNumber = billingTelephoneNumber;
        this.serviceAddress = serviceAddress;
        this.customerType = customerType;
        this.customerStatus = CustomerStatus.ACTIVE;
    }
    
    // Getters and Setters
    public String getCustomerCode() { return customerCode; }
    public void setCustomerCode(String customerCode) { this.customerCode = customerCode; }
    
    public String getBillingTelephoneNumber() { return billingTelephoneNumber; }
    public void setBillingTelephoneNumber(String billingTelephoneNumber) { 
        this.billingTelephoneNumber = billingTelephoneNumber; 
    }
    
    public String getServiceAddress() { return serviceAddress; }
    public void setServiceAddress(String serviceAddress) { this.serviceAddress = serviceAddress; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public CustomerType getCustomerType() { return customerType; }
    public void setCustomerType(CustomerType customerType) { this.customerType = customerType; }
    
    public CustomerStatus getCustomerStatus() { return customerStatus; }
    public void setCustomerStatus(CustomerStatus customerStatus) { this.customerStatus = customerStatus; }
    
    public String getAccountNumber() { return accountNumber; }
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }
    
    public String getServiceClassCode() { return serviceClassCode; }
    public void setServiceClassCode(String serviceClassCode) { this.serviceClassCode = serviceClassCode; }
    
    public String getMaintenanceLevel() { return maintenanceLevel; }
    public void setMaintenanceLevel(String maintenanceLevel) { this.maintenanceLevel = maintenanceLevel; }
    
    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }
    
    public String getAlternateContact() { return alternateContact; }
    public void setAlternateContact(String alternateContact) { this.alternateContact = alternateContact; }
    
    public String getEmailAddress() { return emailAddress; }
    public void setEmailAddress(String emailAddress) { this.emailAddress = emailAddress; }
    
    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }
    
    public LocalDateTime getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate = createdDate; }
    
    public LocalDateTime getModifiedDate() { return modifiedDate; }
    public void setModifiedDate(LocalDateTime modifiedDate) { this.modifiedDate = modifiedDate; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public String getModifiedBy() { return modifiedBy; }
    public void setModifiedBy(String modifiedBy) { this.modifiedBy = modifiedBy; }
    
    public Long getVersion() { return version; }
    public void setVersion(Long version) { this.version = version; }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Customer customer = (Customer) o;
        return Objects.equals(customerCode, customer.customerCode);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(customerCode);
    }
    
    @Override
    public String toString() {
        return "Customer{" +
                "customerCode='" + customerCode + '\'' +
                ", billingTelephoneNumber='" + billingTelephoneNumber + '\'' +
                ", customerName='" + customerName + '\'' +
                ", customerType=" + customerType +
                ", customerStatus=" + customerStatus +
                ", accountNumber='" + accountNumber + '\'' +
                '}';
    }
}
