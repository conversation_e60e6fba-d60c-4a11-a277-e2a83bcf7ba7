# 🗄️ Database Setup Guide
## VRepair Customer Management Service

This guide provides multiple options to set up and run the VRepair Customer Management Service with different database configurations.

## 🚀 Quick Start Options

### Option 1: Oracle Database with Docker (Recommended for Development)

**Prerequisites:**
- Docker Desktop installed and running
- Docker Compose available

**Setup Steps:**

1. **Start Oracle Database:**
   ```bash
   ./start-with-oracle.sh
   ```
   
   Or manually:
   ```bash
   # Load environment variables
   source oracle.env
   
   # Start containers
   docker-compose up -d
   
   # Wait for database to be ready (about 2-3 minutes)
   # Then start the application
   java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=oracle,prod
   ```

2. **Access Points:**
   - **Application:** http://localhost:8080/customer-service
   - **API Docs:** http://localhost:8080/customer-service/swagger-ui.html
   - **Oracle EM:** http://localhost:5500/em (admin/VRepair123!)
   - **Health Check:** http://localhost:8080/customer-service/actuator/health

3. **Database Details:**
   - **Host:** localhost:1521
   - **Service:** VREPAIR
   - **Username:** vrepair_user
   - **Password:** vrepair_pass

### Option 2: H2 Database (Quick Testing)

**For immediate testing without Docker:**

```bash
./start-with-h2.sh
```

**Access Points:**
- **Application:** http://localhost:8080/customer-service
- **H2 Console:** http://localhost:8080/customer-service/h2-console
  - JDBC URL: `jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE`
  - Username: `sa`
  - Password: (leave blank)

## 🐳 Docker Oracle Setup Details

### Container Configuration

The Docker setup includes:
- **Oracle XE 21c** (Free Edition)
- **Redis** (for caching)
- **Automatic schema creation**
- **Sample data insertion**

### Database Schema

The Oracle container automatically creates:

```sql
CREATE TABLE CUSTOMERS (
    CUSTOMER_CODE VARCHAR2(20) PRIMARY KEY,
    BILLING_TELEPHONE_NUM VARCHAR2(15) NOT NULL,
    SERVICE_ADDRESS VARCHAR2(500),
    CUSTOMER_NAME VARCHAR2(100),
    CUSTOMER_TYPE VARCHAR2(20),
    CUSTOMER_STATUS VARCHAR2(20) DEFAULT 'ACTIVE',
    ACCOUNT_NUMBER VARCHAR2(50),
    SERVICE_CLASS_CODE VARCHAR2(10),
    CONTACT_PHONE VARCHAR2(15),
    EMAIL_ADDRESS VARCHAR2(100),
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    MODIFIED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
    MODIFIED_BY VARCHAR2(50) DEFAULT 'SYSTEM'
);
```

### Sample Data

Three test customers are automatically inserted:
- **CUST001** - John Doe (Residential)
- **CUST002** - ABC Corporation (Business)  
- **CUST003** - XYZ Enterprises (Enterprise)

## 🔧 Manual Configuration

### Environment Variables

Create your own environment setup:

```bash
# Database Configuration
export DB_HOST=localhost
export DB_PORT=1521
export DB_SERVICE_NAME=VREPAIR
export DB_USERNAME=vrepair_user
export DB_PASSWORD=vrepair_pass

# Redis Configuration
export REDIS_HOST=localhost
export REDIS_PORT=6379
```

### Production Oracle Database

For connecting to an existing Oracle database:

```bash
# Set environment variables
export DB_HOST=your-oracle-server.com
export DB_PORT=1521
export DB_SERVICE_NAME=YOUR_SERVICE
export DB_USERNAME=your_username
export DB_PASSWORD=your_password

# Run application
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=oracle,prod
```

## 🛠️ Troubleshooting

### Common Issues

1. **"jdbcUrl is required" Error:**
   - Ensure environment variables are set
   - Check that Oracle database is running
   - Verify connection parameters

2. **Oracle Container Won't Start:**
   - Check Docker Desktop is running
   - Ensure port 1521 is not in use
   - Check container logs: `docker-compose logs oracle-db`

3. **Application Won't Connect:**
   - Wait for Oracle to fully initialize (2-3 minutes)
   - Check database health: `docker exec vrepair-oracle-xe sqlplus vrepair_user/vrepair_pass@VREPAIR`

### Useful Commands

```bash
# Check container status
docker-compose ps

# View Oracle logs
docker-compose logs oracle-db

# Connect to Oracle directly
docker exec -it vrepair-oracle-xe sqlplus vrepair_user/vrepair_pass@VREPAIR

# Stop all containers
docker-compose down

# Clean restart (removes data)
docker-compose down -v
docker-compose up -d
```

## 📊 Testing the Setup

### Health Check
```bash
curl http://localhost:8080/customer-service/actuator/health
```

### API Test
```bash
# Get all customers
curl "http://localhost:8080/customer-service/api/v1/customers?page=0&size=10"

# Get specific customer
curl "http://localhost:8080/customer-service/api/v1/customers/CUST001"
```

### Database Query
```bash
# Connect to Oracle and run query
docker exec -it vrepair-oracle-xe sqlplus vrepair_user/vrepair_pass@VREPAIR <<< "SELECT * FROM CUSTOMERS;"
```

## 🔄 Switching Between Databases

You can easily switch between database configurations:

```bash
# Oracle Database
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=oracle,prod

# H2 Database  
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=simple

# Development profile (H2)
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=dev
```

---

## ✅ Success Indicators

When everything is working correctly, you should see:

1. **Application Startup:**
   ```
   Started CustomerManagementServiceApplication in X.X seconds
   ```

2. **Database Connection:**
   ```
   HikariPool-1 - Start completed.
   ```

3. **Health Check Response:**
   ```json
   {
     "status": "UP",
     "components": {
       "db": {"status": "UP"},
       "redis": {"status": "UP"}
     }
   }
   ```

4. **API Response:**
   ```json
   {
     "content": [
       {
         "customerCode": "CUST001",
         "customerName": "John Doe",
         "customerType": "RESIDENTIAL"
       }
     ]
   }
   ```

Happy coding! 🎉
