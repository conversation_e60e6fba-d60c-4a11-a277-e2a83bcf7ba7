import pytest
from app import app, db, User

@pytest.fixture
def client():
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    
    with app.test_client() as client:
        with app.app_context():
            db.create_all()
            yield client
            db.drop_all()

def test_create_user(client):
    """Test user creation endpoint."""
    response = client.post('/api/users', json={
        'username': 'testuser',
        'email': '<EMAIL>'
    })
    assert response.status_code == 201
    assert b'User created successfully' in response.data

def test_get_users_unauthorized(client):
    """Test that get users requires authentication."""
    response = client.get('/api/users')
    assert response.status_code == 401
