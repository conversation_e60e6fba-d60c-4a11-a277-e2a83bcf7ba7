#!/bin/bash

# VRepair Customer Management Service - Documentation Server Only
# This script starts ONLY the documentation server on port 8082
# The main service should be running separately on port 8080

clear
echo "📖 VRepair Customer Management Service - Documentation Server"
echo "============================================================="
echo ""
echo "📋 Configuration:"
echo "   🌐 Documentation Port: 8082"
echo "   🔧 Profile:            docs"
echo "   📂 Context Path:       /docs"
echo "   ⚠️  Main Service:       Should be running on port 8080"
echo ""
echo "📖 Documentation URLs:"
echo "   📖 Swagger UI:         http://localhost:8082/docs/swagger-ui/index.html"
echo "   📄 OpenAPI Spec:       http://localhost:8082/docs/v3/api-docs"
echo "   📝 OpenAPI YAML:       http://localhost:8082/docs/v3/api-docs.yaml"
echo "   ❤️  Health Check:      http://localhost:8082/docs/actuator/health"
echo ""
echo "⚠️  IMPORTANT:"
echo "   This is a documentation-only server!"
echo "   The main Customer Management Service should be running on port 8080"
echo "   API calls will be proxied to http://localhost:8080/customer-service"
echo ""
echo "🏃 Starting documentation server on port 8082..."
echo "   Press Ctrl+C to stop the documentation server"
echo ""

# Start the Spring Boot application with docs-only profile
mvn spring-boot:run -Dspring-boot.run.profiles=docs
