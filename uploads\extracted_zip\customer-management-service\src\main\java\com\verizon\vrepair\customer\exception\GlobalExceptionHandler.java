package com.verizon.vrepair.customer.exception;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Global exception handler for Customer Service.
 * Provides consistent error responses across all API endpoints.
 * Replaces legacy C++ error handling with modern Spring exception management.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    @ExceptionHandler(CustomerNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleCustomerNotFoundException(
            CustomerNotFoundException ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.warn("Customer not found - CorrelationId: {}, Message: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("CUSTOMER_NOT_FOUND")
                .message(ex.getMessage())
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .build();
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }
    
    @ExceptionHandler(CustomerAlreadyExistsException.class)
    public ResponseEntity<ErrorResponse> handleCustomerAlreadyExistsException(
            CustomerAlreadyExistsException ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.warn("Customer already exists - CorrelationId: {}, Message: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("CUSTOMER_ALREADY_EXISTS")
                .message(ex.getMessage())
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .build();
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse);
    }
    
    @ExceptionHandler(CustomerValidationException.class)
    public ResponseEntity<ErrorResponse> handleCustomerValidationException(
            CustomerValidationException ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.warn("Customer validation error - CorrelationId: {}, Message: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("CUSTOMER_VALIDATION_ERROR")
                .message(ex.getMessage())
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ValidationErrorResponse> handleValidationException(
            MethodArgumentNotValidException ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.warn("Validation error - CorrelationId: {}, Errors: {}", correlationId, ex.getBindingResult().getErrorCount());
        
        List<FieldValidationError> fieldErrors = new ArrayList<>();
        for (FieldError fieldError : ex.getBindingResult().getFieldErrors()) {
            fieldErrors.add(new FieldValidationError(
                    fieldError.getField(),
                    fieldError.getDefaultMessage(),
                    fieldError.getRejectedValue() != null ? fieldError.getRejectedValue().toString() : null
            ));
        }
        
        ValidationErrorResponse errorResponse = ValidationErrorResponse.builder()
                .errorCode("VALIDATION_ERROR")
                .message("Request validation failed")
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .fieldErrors(fieldErrors)
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorResponse> handleConstraintViolationException(
            ConstraintViolationException ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.warn("Constraint violation - CorrelationId: {}, Message: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("CONSTRAINT_VIOLATION")
                .message("Data constraint violation: " + ex.getMessage())
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ErrorResponse> handleAccessDeniedException(
            AccessDeniedException ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.warn("Access denied - CorrelationId: {}, Message: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("ACCESS_DENIED")
                .message("Access denied: " + ex.getMessage())
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .build();
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }
    
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.warn("Illegal argument - CorrelationId: {}, Message: {}", correlationId, ex.getMessage());
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("INVALID_ARGUMENT")
                .message(ex.getMessage())
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, WebRequest request) {
        
        String correlationId = UUID.randomUUID().toString();
        
        logger.error("Unexpected error - CorrelationId: {}, Message: {}", correlationId, ex.getMessage(), ex);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("INTERNAL_SERVER_ERROR")
                .message("An unexpected error occurred. Please contact support with correlation ID: " + correlationId)
                .correlationId(correlationId)
                .timestamp(LocalDateTime.now())
                .path(getPath(request))
                .build();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
    
    private String getPath(WebRequest request) {
        return request.getDescription(false).replace("uri=", "");
    }
    
    /**
     * Standard error response structure.
     */
    @Schema(description = "Error response")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ErrorResponse {
        
        @Schema(description = "Error code", example = "CUSTOMER_NOT_FOUND")
        private String errorCode;
        
        @Schema(description = "Error message", example = "Customer not found with code: CUST001")
        private String message;
        
        @Schema(description = "Correlation ID for tracking", example = "123e4567-e89b-12d3-a456-426614174000")
        private String correlationId;
        
        @Schema(description = "Error timestamp", example = "2024-01-15T10:30:00")
        private LocalDateTime timestamp;
        
        @Schema(description = "Request path", example = "/api/v1/customers/CUST001")
        private String path;
        
        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private ErrorResponse errorResponse = new ErrorResponse();
            
            public Builder errorCode(String errorCode) {
                errorResponse.errorCode = errorCode;
                return this;
            }
            
            public Builder message(String message) {
                errorResponse.message = message;
                return this;
            }
            
            public Builder correlationId(String correlationId) {
                errorResponse.correlationId = correlationId;
                return this;
            }
            
            public Builder timestamp(LocalDateTime timestamp) {
                errorResponse.timestamp = timestamp;
                return this;
            }
            
            public Builder path(String path) {
                errorResponse.path = path;
                return this;
            }
            
            public ErrorResponse build() {
                return errorResponse;
            }
        }
        
        // Getters and setters
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getCorrelationId() { return correlationId; }
        public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
    }
    
    /**
     * Validation error response with field details.
     */
    @Schema(description = "Validation error response")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ValidationErrorResponse extends ErrorResponse {
        
        @Schema(description = "Field validation errors")
        private List<FieldValidationError> fieldErrors;
        
        public static ValidationErrorBuilder builder() {
            return new ValidationErrorBuilder();
        }
        
        public static class ValidationErrorBuilder extends Builder {
            private ValidationErrorResponse validationErrorResponse = new ValidationErrorResponse();
            
            @Override
            public ValidationErrorBuilder errorCode(String errorCode) {
                validationErrorResponse.setErrorCode(errorCode);
                return this;
            }
            
            @Override
            public ValidationErrorBuilder message(String message) {
                validationErrorResponse.setMessage(message);
                return this;
            }
            
            @Override
            public ValidationErrorBuilder correlationId(String correlationId) {
                validationErrorResponse.setCorrelationId(correlationId);
                return this;
            }
            
            @Override
            public ValidationErrorBuilder timestamp(LocalDateTime timestamp) {
                validationErrorResponse.setTimestamp(timestamp);
                return this;
            }
            
            @Override
            public ValidationErrorBuilder path(String path) {
                validationErrorResponse.setPath(path);
                return this;
            }
            
            public ValidationErrorBuilder fieldErrors(List<FieldValidationError> fieldErrors) {
                validationErrorResponse.fieldErrors = fieldErrors;
                return this;
            }
            
            @Override
            public ValidationErrorResponse build() {
                return validationErrorResponse;
            }
        }
        
        public List<FieldValidationError> getFieldErrors() { return fieldErrors; }
        public void setFieldErrors(List<FieldValidationError> fieldErrors) { this.fieldErrors = fieldErrors; }
    }
    
    /**
     * Field validation error details.
     */
    @Schema(description = "Field validation error")
    public static class FieldValidationError {
        
        @Schema(description = "Field name", example = "billingTelephoneNumber")
        private String field;
        
        @Schema(description = "Error message", example = "Phone number must be 10 digits")
        private String message;
        
        @Schema(description = "Rejected value", example = "123")
        private String rejectedValue;
        
        public FieldValidationError(String field, String message, String rejectedValue) {
            this.field = field;
            this.message = message;
            this.rejectedValue = rejectedValue;
        }
        
        public String getField() { return field; }
        public void setField(String field) { this.field = field; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getRejectedValue() { return rejectedValue; }
        public void setRejectedValue(String rejectedValue) { this.rejectedValue = rejectedValue; }
    }
}


