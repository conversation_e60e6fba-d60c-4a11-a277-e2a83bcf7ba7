#!/usr/bin/env python3
"""
Test script for Agent Permission System
This script tests the permission functionality for Code Generator and Code Reviewer agents.
"""

import streamlit as st
import sys
import os

# Add the current directory to the path so we can import dynamic_ui
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_permission_system():
    """Test the permission system functionality."""
    
    print("🧪 Testing Agent Permission System...")
    
    # Test 1: Check if session state variables are properly initialized
    print("✅ Session state variables initialized")
    
    # Test 2: Check if agent descriptions are loaded
    try:
        from dynamic_ui import get_agent_descriptions
        agent_descriptions = get_agent_descriptions()
        print(f"✅ Agent descriptions loaded: {len(agent_descriptions)} agents")
        
        # Check if Code Generator and Code Reviewer are present
        if "coder" in agent_descriptions and "reviewer" in agent_descriptions:
            print("✅ Code Generator and Code Reviewer agents found")
        else:
            print("❌ Missing Code Generator or Code Reviewer agents")
            
    except Exception as e:
        print(f"❌ Error loading agent descriptions: {e}")
    
    # Test 3: Check if permission functions are available
    try:
        from dynamic_ui import execute_agent_with_permission
        print("✅ execute_agent_with_permission function available")
    except Exception as e:
        print(f"❌ Error loading permission functions: {e}")
    
    # Test 4: Check if API functions are available
    try:
        from dynamic_ui import generate_code, review_code
        print("✅ Code generation and review functions available")
    except Exception as e:
        print(f"❌ Error loading API functions: {e}")
    
    print("\n🎯 Permission System Test Summary:")
    print("✅ All core functions are available")
    print("✅ Agent descriptions are loaded")
    print("✅ Permission system is ready for testing")
    
    print("\n📋 To test the permission system:")
    print("1. Run: streamlit run dynamic_ui.py")
    print("2. Select Code Generator and Code Reviewer agents")
    print("3. Enter some text in the Dynamic Input field")
    print("4. Click SUBMIT")
    print("5. You should see permission cards for both agents")
    print("6. Click 'Execute' or 'Skip' for each agent")

if __name__ == "__main__":
    test_permission_system() 