#!/bin/bash

echo "🎯 VRepair Customer Management Service - API Demo"
echo "=================================================="
echo ""

BASE_URL="http://localhost:8090/customer-service"

echo "1️⃣ Testing Health Check..."
curl -s "$BASE_URL/actuator/health" | jq '.'
echo ""
echo ""

echo "2️⃣ Creating a Demo Customer..."
CUSTOMER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/customers" \
  -H "Content-Type: application/json" \
  -d '{
    "billingTelephoneNumber": "**********",
    "customerName": "Demo Customer",
    "customerType": "RESIDENTIAL",
    "serviceAddress": "123 Demo Street, Demo City, DC 12345",
    "accountNumber": "DEMO001",
    "serviceClassCode": "RES",
    "contactPhone": "**********",
    "emailAddress": "<EMAIL>"
  }')

echo "$CUSTOMER_RESPONSE" | jq '.'
CUSTOMER_CODE=$(echo "$CUSTOMER_RESPONSE" | jq -r '.customerCode')
echo ""
echo "✅ Customer created with code: $CUSTOMER_CODE"
echo ""

echo "3️⃣ Retrieving the Customer..."
curl -s "$BASE_URL/api/v1/customers/$CUSTOMER_CODE" | jq '.'
echo ""
echo ""

echo "4️⃣ Getting All Customers (paginated)..."
curl -s "$BASE_URL/api/v1/customers?page=0&size=10" | jq '.'
echo ""
echo ""

echo "5️⃣ Searching Customers by Name..."
curl -s "$BASE_URL/api/v1/customers/search?customerName=Demo" | jq '.'
echo ""
echo ""

echo "6️⃣ Getting Customer Statistics..."
curl -s "$BASE_URL/api/v1/customers/statistics" | jq '.'
echo ""
echo ""

echo "7️⃣ Creating a Business Customer..."
curl -s -X POST "$BASE_URL/api/v1/customers" \
  -H "Content-Type: application/json" \
  -d '{
    "billingTelephoneNumber": "**********",
    "customerName": "Demo Business Corp",
    "customerType": "BUSINESS",
    "serviceAddress": "456 Business Ave, Corporate City, CC 54321",
    "accountNumber": "BUS001",
    "serviceClassCode": "BUS",
    "contactPhone": "**********",
    "emailAddress": "<EMAIL>"
  }' | jq '.'
echo ""
echo ""

echo "8️⃣ Updated Statistics after adding Business Customer..."
curl -s "$BASE_URL/api/v1/customers/statistics" | jq '.'
echo ""
echo ""

echo "🎉 Demo Complete!"
echo "=================================================="
echo "📋 Available URLs:"
echo "   🏥 Health:     $BASE_URL/actuator/health"
echo "   📖 Swagger:    $BASE_URL/swagger-ui/index.html"
echo "   💾 H2 Console: $BASE_URL/h2-console"
echo "   📊 Metrics:    $BASE_URL/actuator/metrics"
echo "=================================================="
