server:
  port: 8080
  servlet:
    context-path: /customer-service

spring:
  application:
    name: customer-management-service
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        show_sql: true
    open-in-view: false
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  cache:
    type: simple
  
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://localhost:8080/.well-known/jwks.json
          # Disable security for development
    user:
      name: admin
      password: admin
      roles: ADMIN

# Minimal management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
  health:
    redis:
      enabled: false
    db:
      enabled: true

# Simple logging
logging:
  level:
    com.verizon.vrepair.customer: DEBUG
    org.springframework: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Development Database Configuration
app:
  database:
    type: h2
    schema: PUBLIC

# OpenAPI/Swagger configuration
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
  info:
    title: Customer Management Service API (Development)
    description: VRepair Customer Management Microservice - Development Environment with H2 Database
    version: 1.0.0-DEV
