"""
RadAgents - AI Agent Automation Platform

This package contains all the AI agents used in the RadAgents platform.
"""

# Import core agents
from .core.base_agent import BaseAgent
from .core.code_generator_agent import CodeGeneratorAgent
from .core.reviewer_agent import ReviewerAgent
from .core.improver_agent import ImproverAgent
from .core.documentation_agent import DocumentationAgent
from .core.prd_agent import PRDAgent
from .core.qa_agent import QAAgent

# Import specialized agents
from .specialized.api_integration_agent import APIIntegrationAgent

# Optional imports for specialized agents that require additional dependencies
try:
    from .specialized.selenium_test_agent import SeleniumTestAgent
except ImportError:
    SeleniumTestAgent = None

__all__ = [
    'BaseAgent',
    'CodeGeneratorAgent', 
    'ReviewerAgent',
    'ImproverAgent',
    'DocumentationAgent',
    'PRDAgent',
    'QAAgent',
    'APIIntegrationAgent',
]

# Add optional agents if available
if SeleniumTestAgent is not None:
    __all__.append('SeleniumTestAgent')