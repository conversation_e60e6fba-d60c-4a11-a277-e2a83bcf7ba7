import streamlit as st
import os
from datetime import datetime
import subprocess

# Import local modules
from local_agents import (
    CodeGeneratorAgent, ReviewerAgent, ImproverAgent, 
    APIIntegrationAgent, DocumentationAgent, PRDAgent, QAAgent
)
from config import API_KEY, google_api_key, OUTPUT_PATH
from anthropic import Anthropic
from generated_scripts.create_jira_issues_from_prd import create_jira_issues_from_prd
from local_agents.qa_agent import create_jira_bug

# Configure Streamlit page
st.set_page_config(
    page_title="MOP Agent Workflow UI",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        background-color: #f8f9fa;
    }
    .status-success {
        color: #28a745;
        font-weight: bold;
    }
    .status-error {
        color: #dc3545;
        font-weight: bold;
    }
    .status-running {
        color: #ffc107;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if 'workflow_results' not in st.session_state:
        st.session_state.workflow_results = {}
    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'selected_agents' not in st.session_state:
        st.session_state.selected_agents = set()
    if 'mop_content' not in st.session_state:
        st.session_state.mop_content = ""
    if 'mop_source' not in st.session_state:
        st.session_state.mop_source = ""

def get_agent_descriptions():
    """Return descriptions for each agent."""
    return {
        "coder": {
            "name": "Code Generator",
            "description": "Generates clean Python scripts based on MOP content with best practices and detailed comments.",
            "icon": "💻"
        },
        "reviewer": {
            "name": "Code Reviewer", 
            "description": "Reviews generated code for clarity, functionality, and adherence to best practices.",
            "icon": "🔍"
        },
        "improver": {
            "name": "Code Improver",
            "description": "Enhances and optimizes existing code with performance improvements and better structure.",
            "icon": "✨"
        },
        "integrator": {
            "name": "API Integration",
            "description": "Handles API integration tasks, analysis, design, testing, and optimization.",
            "icon": "🔗"
        },
        "Document": {
            "name": "Documentation",
            "description": "Creates comprehensive documentation for generated code and processes.",
            "icon": "📚"
        },
        "prd_agent": {
            "name": "PRD Generator",
            "description": "Generates Product Requirements Documents with user stories and technical specifications.",
            "icon": "📄"
        },
        "qa_agent": {
            "name": "QA & Testing",
            "description": "Performs static code analysis, generates test cases, and checks compliance.",
            "icon": "🧪"
        }
    }

def load_predefined_mops():
    """Load predefined MOP files."""
    mops = {}

    # Add a simple test MOP
    mops["Simple Test MOP"] = """
MOP: Simple Python Script Generation Test

Objective:
Create a basic Python script that demonstrates file operations.

Steps:
1. Create a function to read a text file
2. Create a function to process the data
3. Create a function to write output to a file
4. Add error handling
5. Include proper documentation

Expected Output:
A complete Python script with file I/O operations and error handling.
"""

    try:
        from mop_cisco_audit import MOP_CONTENT
        mops["Cisco XR Device Audit"] = MOP_CONTENT
    except ImportError:
        pass

    try:
        from PRDmop import PRD_MOP_CONTENT
        mops["PRD Generation"] = PRD_MOP_CONTENT
    except ImportError:
        pass

    return mops

def main():
    """Main application function."""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🤖 MOP Agent Workflow UI</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar for MOP selection
    with st.sidebar:
        st.header("📁 MOP File Selection")
        
        # MOP source selection
        mop_source = st.radio(
            "Choose MOP source:",
            ["Upload File", "Upload Folder (ZIP)", "Predefined MOPs", "Text Input"],
            key="mop_source_radio"
        )
        
        if mop_source == "Upload File":
            uploaded_file = st.file_uploader(
                "Choose any file",
                type=None,  # Allow all file types
                help="Upload any file containing your MOP content"
            )
            
            if uploaded_file is not None:
                try:
                    content = uploaded_file.read().decode('utf-8')
                except Exception:
                    content = uploaded_file.read()  # fallback for binary
                st.session_state.mop_content = content
                st.session_state.mop_source = f"Uploaded: {uploaded_file.name}"
                st.success(f"✅ Loaded {uploaded_file.name}")
        
        elif mop_source == "Upload Folder (ZIP)":
            uploaded_zip = st.file_uploader(
                "Upload a ZIP file (folder)",
                type=["zip"],
                help="Upload a ZIP file containing multiple files as a folder. All files will be processed."
            )
            import zipfile
            import io
            if uploaded_zip is not None:
                try:
                    with zipfile.ZipFile(io.BytesIO(uploaded_zip.read())) as z:
                        file_list = z.namelist()
                        st.write(f"Files in ZIP: {file_list}")
                        folder_content = {}
                        for fname in file_list:
                            if not fname.endswith("/"):
                                try:
                                    folder_content[fname] = z.read(fname).decode('utf-8')
                                except Exception:
                                    folder_content[fname] = z.read(fname)  # fallback for binary
                        st.session_state.mop_content = folder_content
                        st.session_state.mop_source = f"Uploaded folder: {uploaded_zip.name}"
                        st.success(f"✅ Loaded folder {uploaded_zip.name} with {len(folder_content)} files")
                except Exception as e:
                    st.error(f"Failed to read ZIP: {e}")
        
        elif mop_source == "Predefined MOPs":
            predefined_mops = load_predefined_mops()
            if predefined_mops:
                selected_mop = st.selectbox(
                    "Select a predefined MOP:",
                    list(predefined_mops.keys())
                )
                if selected_mop:
                    st.session_state.mop_content = predefined_mops[selected_mop]
                    st.session_state.mop_source = f"Predefined: {selected_mop}"
                    st.success(f"✅ Loaded {selected_mop}")
            else:
                st.warning("No predefined MOPs available")
                
        elif mop_source == "Text Input":
            mop_text = st.text_area(
                "Enter MOP content:",
                height=200,
                placeholder="Paste your MOP content here..."
            )
            if mop_text:
                st.session_state.mop_content = mop_text
                st.session_state.mop_source = "Text Input"
                st.success("✅ MOP content entered")
        
        # Display current MOP info
        if st.session_state.mop_content:
            st.info(f"📋 Current MOP: {st.session_state.mop_source}")
            with st.expander("Preview MOP Content"):
                st.text(st.session_state.mop_content[:500] + "..." if len(st.session_state.mop_content) > 500 else st.session_state.mop_content)
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("🤖 Agent Selection")
        
        if not st.session_state.mop_content:
            st.warning("⚠️ Please select a MOP file first")
            return
        
        agent_descriptions = get_agent_descriptions()
        
        # Agent selection with descriptions
        st.markdown("Select the agents you want to run:")
        
        selected_agents = set()
        for agent_key, agent_info in agent_descriptions.items():
            col_check, col_info = st.columns([1, 4])
            
            with col_check:
                if st.checkbox(
                    f"{agent_info['icon']} {agent_info['name']}", 
                    key=f"agent_{agent_key}",
                    value=agent_key in st.session_state.selected_agents
                ):
                    selected_agents.add(agent_key)
            
            with col_info:
                st.markdown(f"*{agent_info['description']}*")
        
        st.session_state.selected_agents = selected_agents
        
        # Run workflow button
        if st.button("🚀 Run Workflow", disabled=st.session_state.workflow_running or not selected_agents):
            run_workflow()
    
    with col2:
        st.header("📊 Results & Progress")
        
        # Workflow controls
        if st.button("🗑️ Clear Results", disabled=st.session_state.workflow_running):
            st.session_state.workflow_results = {}
            st.rerun()
        
        if st.session_state.workflow_running:
            st.markdown('<p class="status-running">🔄 Workflow is running...</p>', unsafe_allow_html=True)
            st.progress(0.5)
        
        # Display results
        if st.session_state.workflow_results:
            # Summary statistics
            total_agents = len(st.session_state.workflow_results)
            successful_agents = sum(1 for result in st.session_state.workflow_results.values() if result.get('status') == 'success')
            failed_agents = total_agents - successful_agents
            
            col_stats1, col_stats2, col_stats3 = st.columns(3)
            with col_stats1:
                st.metric("Total Agents", total_agents)
            with col_stats2:
                st.metric("Successful", successful_agents, delta=successful_agents)
            with col_stats3:
                st.metric("Failed", failed_agents, delta=-failed_agents if failed_agents > 0 else 0)
            
            st.markdown("---")
            
            # Results display
            for agent_name, result in st.session_state.workflow_results.items():
                with st.expander(f"📋 {agent_name} Results", expanded=True):
                    if result.get('status') == 'success':
                        st.markdown('<p class="status-success">✅ Completed Successfully</p>', unsafe_allow_html=True)
                        
                        # Output display
                        output_text = result.get('output', '')
                        st.text_area(
                            f"{agent_name} Output", 
                            output_text, 
                            height=200, 
                            key=f"result_{agent_name}",
                            help="Click to select all text, then Ctrl+C to copy"
                        )
                        
                        # Download button for file outputs
                        if result.get('file_path') and os.path.exists(result['file_path']):
                            try:
                                with open(result['file_path'], 'r', encoding='utf-8') as f:
                                    file_content = f.read()
                                
                                file_name = os.path.basename(result['file_path'])
                                st.download_button(
                                    f"📥 Download {file_name}",
                                    file_content,
                                    file_name=file_name,
                                    key=f"download_{agent_name}",
                                    help=f"Download the generated {file_name} file"
                                )
                                
                                # Show file info
                                file_size = len(file_content.encode('utf-8'))
                                st.caption(f"📄 File: {file_name} ({file_size} bytes)")
                                
                            except Exception as e:
                                st.error(f"Error reading file: {e}")
                    
                    elif result.get('status') == 'error':
                        st.markdown('<p class="status-error">❌ Error Occurred</p>', unsafe_allow_html=True)
                        st.error(result.get('error', 'Unknown error'))
        
        else:
            st.info("👆 Select agents and run the workflow to see results here")

def run_workflow():
    """Execute the selected workflow."""
    st.session_state.workflow_running = True
    st.session_state.workflow_results = {}

    # Show progress message
    progress_placeholder = st.empty()
    progress_placeholder.info("🔄 Initializing workflow...")

    try:
        # Validate inputs
        if not st.session_state.mop_content:
            st.error("❌ No MOP content provided!")
            return

        if not st.session_state.selected_agents:
            st.error("❌ No agents selected!")
            return

        if not API_KEY:
            st.error("❌ API_KEY not configured in config.py!")
            return

        progress_placeholder.info("🔄 Initializing Claude client...")

        # Initialize Claude client and agents
        claude_client = Anthropic(api_key=API_KEY)

        progress_placeholder.info("🔄 Initializing agents...")

        # Initialize all agents
        agents = {}

        if 'coder' in st.session_state.selected_agents:
            agents['coder'] = CodeGeneratorAgent(claude_client=claude_client, name="coder", api_key=API_KEY)
        if 'reviewer' in st.session_state.selected_agents:
            agents['reviewer'] = ReviewerAgent(claude_client=claude_client, name="reviewer", api_key=API_KEY)
        if 'improver' in st.session_state.selected_agents:
            agents['improver'] = ImproverAgent(claude_client=claude_client, name="improver", api_key=API_KEY)
        if 'integrator' in st.session_state.selected_agents:
            agents['integrator'] = APIIntegrationAgent(claude_client=claude_client, name="integrator", google_api_key=google_api_key)
        if 'Document' in st.session_state.selected_agents:
            agents['Document'] = DocumentationAgent(claude_client=claude_client, name="DocumentAgent", api_key=API_KEY)
        if 'prd_agent' in st.session_state.selected_agents:
            agents['prd_agent'] = PRDAgent(claude_client=claude_client, name="PRDAgent", api_key=API_KEY)
        if 'qa_agent' in st.session_state.selected_agents:
            agents['qa_agent'] = QAAgent(claude_client=claude_client, name="QAAgent", api_key=API_KEY)

        progress_placeholder.info("🔄 Executing workflow...")

        # Execute workflow based on selected agents
        execute_agent_workflow(agents, st.session_state.selected_agents, st.session_state.mop_content)

        progress_placeholder.success("✅ Workflow completed!")

    except Exception as e:
        st.error(f"❌ Workflow execution failed: {str(e)}")
        st.exception(e)  # Show full traceback for debugging
    finally:
        st.session_state.workflow_running = False
        st.rerun()

def execute_agent_workflow(agents, selected_agents, mop_content):
    """Execute the workflow with selected agents."""
    generated_code = None
    improved_code = None
    integration_result = None

    # Code Generation
    if "coder" in selected_agents:
        try:
            generated_code = agents['coder'].generate_reply([{"content": mop_content}])
            st.session_state.workflow_results['Code Generator'] = {
                'status': 'success',
                'output': generated_code
            }
        except Exception as e:
            st.session_state.workflow_results['Code Generator'] = {
                'status': 'error',
                'error': str(e)
            }

    # Code Review
    if "reviewer" in selected_agents and generated_code:
        try:
            review_feedback = agents['reviewer'].generate_reply([{"content": f"Review this code:\n\n{generated_code}"}])
            st.session_state.workflow_results['Code Reviewer'] = {
                'status': 'success',
                'output': review_feedback
            }
        except Exception as e:
            st.session_state.workflow_results['Code Reviewer'] = {
                'status': 'error',
                'error': str(e)
            }

    # Code Improvement
    if "improver" in selected_agents and generated_code:
        try:
            improved_code = agents['improver'].generate_reply([{"content": f"Improve this code:\n\n{generated_code}"}])
            st.session_state.workflow_results['Code Improver'] = {
                'status': 'success',
                'output': improved_code
            }
        except Exception as e:
            st.session_state.workflow_results['Code Improver'] = {
                'status': 'error',
                'error': str(e)
            }

    # API Integration
    if "integrator" in selected_agents and (improved_code or generated_code):
        try:
            api_task = f"Integrate the following MOP content into a suitable API workflow:\n\n{mop_content}"
            integration_result = agents['integrator'].handle_task(api_task)
            st.session_state.workflow_results['API Integration'] = {
                'status': 'success',
                'output': integration_result
            }
        except Exception as e:
            st.session_state.workflow_results['API Integration'] = {
                'status': 'error',
                'error': str(e)
            }

    # Documentation
    if "Document" in selected_agents and (generated_code or improved_code):
        try:
            doc_input = improved_code if improved_code else generated_code
            documented_code = agents['Document'].generate_reply([{"content": f"Document this code:\n\n{doc_input}"}])

            # Save documented code
            os.makedirs(OUTPUT_PATH, exist_ok=True)
            output_file = os.path.join(OUTPUT_PATH, "documented_script.py")
            with open(output_file, "w", encoding="utf-8") as file:
                file.write(documented_code)

            st.session_state.workflow_results['Documentation'] = {
                'status': 'success',
                'output': documented_code,
                'file_path': output_file
            }
        except Exception as e:
            st.session_state.workflow_results['Documentation'] = {
                'status': 'error',
                'error': str(e)
            }

    # PRD Generation
    if "prd_agent" in selected_agents:
        try:
            prd_output_file = os.path.join(OUTPUT_PATH, "prd.md")
            os.makedirs(OUTPUT_PATH, exist_ok=True)
            result = agents['prd_agent'].generate_prd(mop_content=mop_content, output_file=prd_output_file)

            # Read the generated PRD content
            with open(prd_output_file, 'r', encoding='utf-8') as f:
                prd_content = f.read()

            st.session_state.workflow_results['PRD Generator'] = {
                'status': 'success',
                'output': prd_content,
                'file_path': prd_output_file
            }

            # Create Jira issues from PRD
            try:
                create_jira_issues_from_prd()
                st.session_state.workflow_results['PRD Generator']['output'] += "\n\n✅ Jira issues created successfully."
            except Exception as jira_e:
                st.session_state.workflow_results['PRD Generator']['output'] += f"\n\n⚠️ Jira issue creation failed: {str(jira_e)}"

        except Exception as e:
            st.session_state.workflow_results['PRD Generator'] = {
                'status': 'error',
                'error': str(e)
            }

    # QA & Testing
    if "qa_agent" in selected_agents and (generated_code or improved_code):
        try:
            code_to_analyze = improved_code if improved_code else generated_code

            # Static code analysis
            analyzed_code = agents['qa_agent'].analyze_code(code_to_analyze)

            # Generate test cases
            test_cases = agents['qa_agent'].generate_test_cases(mop_content)

            # Save test cases
            os.makedirs(OUTPUT_PATH, exist_ok=True)
            test_file_path = os.path.join(OUTPUT_PATH, "test_generated.py")
            with open(test_file_path, "w", encoding="utf-8") as f:
                f.write(test_cases)

            # Run tests (simplified for UI)
            test_output = "Test execution completed in UI mode"

            # Check compliance
            compliance_results = agents['qa_agent'].check_compliance(
                integration_result if integration_result else code_to_analyze
            )

            # Compile QA results
            qa_output = f"Static Code Analysis:\n{analyzed_code}\n\n"
            qa_output += f"Test Cases Generated:\n{test_cases[:500]}...\n\n"
            qa_output += f"Test Results:\n{test_output}\n\n"
            qa_output += f"Compliance Results:\n{compliance_results}"

            # Save comprehensive QA results
            qa_output_file = os.path.join(OUTPUT_PATH, "qa_results.txt")
            with open(qa_output_file, "w", encoding="utf-8") as file:
                file.write(qa_output)

            st.session_state.workflow_results['QA & Testing'] = {
                'status': 'success',
                'output': qa_output,
                'file_path': qa_output_file
            }

        except Exception as e:
            st.session_state.workflow_results['QA & Testing'] = {
                'status': 'error',
                'error': str(e)
            }

if __name__ == "__main__":
    main()
