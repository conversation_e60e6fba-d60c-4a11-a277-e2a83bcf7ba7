package com.verizon.vrepair.customer.controller;

import com.verizon.vrepair.customer.dto.CreateCustomerRequest;
import com.verizon.vrepair.customer.dto.CustomerDto;
import com.verizon.vrepair.customer.dto.UpdateCustomerRequest;
import com.verizon.vrepair.customer.dto.PagedCustomerResponse;
import com.verizon.vrepair.customer.mapper.CustomerMapper;
import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import com.verizon.vrepair.customer.service.CustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Customer REST controller.
 * Provides RESTful APIs for customer management operations.
 * Replaces legacy C++ customer service interfaces.
 */
@RestController
@RequestMapping("/api/v1/customers")
@Tag(name = "Customer Management", description = "Customer CRUD operations and search")
@SecurityRequirement(name = "bearerAuth")
public class CustomerController {
    
    private final CustomerService customerService;
    private final CustomerMapper customerMapper;
    
    @Autowired
    public CustomerController(CustomerService customerService, CustomerMapper customerMapper) {
        this.customerService = customerService;
        this.customerMapper = customerMapper;
    }
    
    @GetMapping
    @Operation(summary = "Get all customers", description = "Retrieve all customers with pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customers retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<PagedCustomerResponse> getAllCustomers(
            @Parameter(description = "Page number (0-based)") 
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Page size") 
            @RequestParam(defaultValue = "20") int size,
            
            @Parameter(description = "Sort field") 
            @RequestParam(defaultValue = "customerName") String sort,
            
            @Parameter(description = "Sort direction (ASC/DESC)") 
            @RequestParam(defaultValue = "ASC") String direction) {
        
        Pageable pageable = PageRequest.of(page, size, 
            Sort.Direction.fromString(direction), sort);
        Page<Customer> customerPage = customerService.getAllCustomers(pageable);
        
        PagedCustomerResponse response = customerMapper.toPagedResponse(customerPage);
        
        return ResponseEntity.ok(response);
    }

    @PostMapping
    @Operation(summary = "Create a new customer", description = "Create a new customer with validation")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Customer created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid customer data"),
        @ApiResponse(responseCode = "409", description = "Customer already exists"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<CustomerDto> createCustomer(
            @Valid @RequestBody CreateCustomerRequest request) {
        
        Customer customer = customerMapper.toEntity(request);
        Customer createdCustomer = customerService.createCustomer(customer);
        CustomerDto response = customerMapper.toDto(createdCustomer);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @GetMapping("/{customerCode}")
    @Operation(summary = "Get customer by code", description = "Retrieve customer details by customer code")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customer found"),
        @ApiResponse(responseCode = "404", description = "Customer not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<CustomerDto> getCustomer(
            @Parameter(description = "Customer code") @PathVariable String customerCode) {
        
        Customer customer = customerService.findCustomerById(customerCode);
        CustomerDto response = customerMapper.toDto(customer);
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/phone/{phoneNumber}")
    @Operation(summary = "Get customer by phone number", description = "Retrieve customer by phone number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customer found"),
        @ApiResponse(responseCode = "404", description = "Customer not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<CustomerDto> getCustomerByPhone(
            @Parameter(description = "Phone number") @PathVariable String phoneNumber) {
        
        Customer customer = customerService.findCustomerByPhoneNumber(phoneNumber);
        CustomerDto response = customerMapper.toDto(customer);
        
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{customerCode}")
    @Operation(summary = "Update customer", description = "Update existing customer information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customer updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid customer data"),
        @ApiResponse(responseCode = "404", description = "Customer not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    
    public ResponseEntity<CustomerDto> updateCustomer(
            @Parameter(description = "Customer code") @PathVariable String customerCode,
            @Valid @RequestBody UpdateCustomerRequest request) {
        
        Customer customer = customerMapper.toEntity(request);
        Customer updatedCustomer = customerService.updateCustomer(customerCode, customer);
        CustomerDto response = customerMapper.toDto(updatedCustomer);
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/search")
    @Operation(summary = "Search customers", description = "Search customers with multiple criteria and pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid search parameters"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    
    public ResponseEntity<PagedCustomerResponse> searchCustomers(
            @Parameter(description = "Customer name (partial match)") 
            @RequestParam(required = false) String customerName,
            
            @Parameter(description = "Phone number (exact match)") 
            @RequestParam(required = false) String phoneNumber,
            
            @Parameter(description = "Customer type") 
            @RequestParam(required = false) CustomerType customerType,
            
            @Parameter(description = "Customer status") 
            @RequestParam(required = false) CustomerStatus customerStatus,
            
            @Parameter(description = "Service address (partial match)") 
            @RequestParam(required = false) String serviceAddress,
            
            @Parameter(description = "Page number (0-based)") 
            @RequestParam(defaultValue = "0") int page,
            
            @Parameter(description = "Page size") 
            @RequestParam(defaultValue = "20") int size,
            
            @Parameter(description = "Sort field") 
            @RequestParam(defaultValue = "customerName") String sort,
            
            @Parameter(description = "Sort direction (ASC/DESC)") 
            @RequestParam(defaultValue = "ASC") String direction) {
        
        List<Customer> customers = customerService.searchCustomers(
                customerName, phoneNumber, customerType, customerStatus, serviceAddress, page, size);
        
        PagedCustomerResponse response = customerMapper.toPagedResponse(customers, page, size);
        
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{customerCode}/deactivate")
    @Operation(summary = "Deactivate customer", description = "Deactivate customer service")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customer deactivated successfully"),
        @ApiResponse(responseCode = "400", description = "Cannot deactivate customer"),
        @ApiResponse(responseCode = "404", description = "Customer not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    
    public ResponseEntity<CustomerDto> deactivateCustomer(
            @Parameter(description = "Customer code") @PathVariable String customerCode) {
        
        Customer deactivatedCustomer = customerService.deactivateCustomer(customerCode);
        CustomerDto response = customerMapper.toDto(deactivatedCustomer);
        
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{customerCode}/activate")
    @Operation(summary = "Activate customer", description = "Activate customer service")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customer activated successfully"),
        @ApiResponse(responseCode = "400", description = "Cannot activate customer"),
        @ApiResponse(responseCode = "404", description = "Customer not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    
    public ResponseEntity<CustomerDto> activateCustomer(
            @Parameter(description = "Customer code") @PathVariable String customerCode) {
        
        Customer activatedCustomer = customerService.activateCustomer(customerCode);
        CustomerDto response = customerMapper.toDto(activatedCustomer);
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/statistics")
    @Operation(summary = "Get customer statistics", description = "Retrieve customer statistics and metrics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    
    public ResponseEntity<CustomerService.CustomerStatistics> getCustomerStatistics() {
        
        CustomerService.CustomerStatistics statistics = customerService.getCustomerStatistics();
        
        return ResponseEntity.ok(statistics);
    }
    
    @GetMapping("/{customerCode}/eligibility")
    @Operation(summary = "Check customer eligibility", description = "Validate customer eligibility for services")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Eligibility check completed"),
        @ApiResponse(responseCode = "404", description = "Customer not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    
    public ResponseEntity<Boolean> checkCustomerEligibility(
            @Parameter(description = "Customer code") @PathVariable String customerCode) {
        
        boolean eligible = customerService.validateCustomerEligibility(customerCode);
        
        return ResponseEntity.ok(eligible);
    }
}
