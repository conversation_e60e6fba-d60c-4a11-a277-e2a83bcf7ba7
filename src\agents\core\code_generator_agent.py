# from .base_agent import BaseAgent

# class CodeGeneratorAgent(BaseAgent):
#     """Agent for generating Python code."""
    
#     def __init__(self, claude_client, name, api_key):
#         super().__init__(
#             name=name,
#             claude_client=claude_client,
#             system_message="You are a Python expert. Generate a clean Python script based on the provided MOP. Include detailed comments and proper structure."
#         )
#         self.api_key = api_key  # Add this to store the API key if needed

#     def generate_reply(self, messages=None, **kwargs):
#         return super().generate_reply(messages, **kwargs)

from .base_agent import BaseAgent

class CodeGeneratorAgent(BaseAgent):
    """Agent for generating code."""
    
    def __init__(self, claude_client, name, api_key):
        super().__init__(
            name=name,
            claude_client=claude_client,
            system_message="""
You are an expert software developer. Generate a clean and efficient script based on the provided MOP. 
Ensure the script follows best practices, includes clear comments, and is production-ready.
""",
            # api_key=api_key
        )
