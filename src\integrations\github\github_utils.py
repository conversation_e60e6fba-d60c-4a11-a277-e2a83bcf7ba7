import requests
import os

def create_github_repo(repo_name, token, username):
    url = "https://api.github.com/user/repos"
    headers = {
        "Authorization": f"token {token}",
        "Accept": "application/vnd.github.v3+json"
    }
    data = {
        "name": repo_name,
        "private": False
    }
    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 201:
        print(f"✅ GitHub repo '{repo_name}' created.")
    elif response.status_code == 422 and 'already exists' in response.text:
        print(f"ℹ️ Repo '{repo_name}' already exists.")
    else:
        print(f"❌ Failed to create repo: {response.text}")
    return f"https://github.com/{username}/{repo_name}.git"

def push_to_github(repo_url, branch, file_path, commit_message, token, username):
    # Set up git config and push using os.system for simplicity
    repo_dir = os.path.dirname(file_path)
    file_basename = os.path.basename(file_path)
    cwd = os.getcwd()
    try:
        os.chdir(repo_dir)
        # Use the correct repo name from the full repo_url (not truncated)
        if repo_url.endswith('.git'):
            remote_url = f"https://{username}:{token}@github.com/{username}/" + repo_url.split('/')[-1]
        else:
            remote_url = f"https://{username}:{token}@github.com/{username}/" + repo_url.split('/')[-1] + ".git"
        # Clean up any previous git folder to avoid conflicts
        if os.path.exists(os.path.join(repo_dir, '.git')):
            import shutil
            shutil.rmtree(os.path.join(repo_dir, '.git'))
        os.system("git init")
        os.system(f"git config --local user.name \"{username}\"")
        os.system(f"git config --local user.email \"{username}@users.noreply.github.com\"")
        os.system("git remote remove origin 2>nul || true")
        os.system(f"git remote add origin {remote_url}")
        os.system(f"git checkout -B {branch}")
        os.system(f"git add {file_basename}")
        os.system(f"git commit -m \"{commit_message}\" --allow-empty")
        os.system(f"git push --set-upstream origin {branch} --force")
        print(f"✅ Pushed {file_path} to {remote_url} [{branch}]")
    finally:
        os.chdir(cwd)
