package com.verizon.vrepair.customer.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.verizon.vrepair.customer.dto.CreateCustomerRequest;
import com.verizon.vrepair.customer.dto.CustomerDto;
import com.verizon.vrepair.customer.dto.UpdateCustomerRequest;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvcSecurity;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * End-to-end workflow tests for Customer Service.
 * Validates complete business workflows matching legacy C++ system behavior.
 * 
 * This test class ensures 100% functional parity with the legacy system
 * by testing complete user workflows from start to finish.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvcSecurity
@ActiveProfiles("integration-test")
@Transactional
class EndToEndWorkflowTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * Test complete customer lifecycle workflow.
     * Validates the full customer journey from creation to termination.
     * This mirrors the legacy C++ customer lifecycle management.
     */
    @Test
    @WithMockUser(roles = {"USER", "ADMIN"})
    void completeCustomerLifecycle_E2EWorkflow_MatchesLegacyBehavior() throws Exception {
        // Step 1: Create residential customer (legacy workflow)
        CreateCustomerRequest createRequest = new CreateCustomerRequest();
        createRequest.setBillingTelephoneNumber("**********");
        createRequest.setServiceAddress("123 Legacy Test St, Legacy City, LC 12345");
        createRequest.setCustomerName("Legacy Test Customer");
        createRequest.setCustomerType("RESIDENTIAL");
        createRequest.setAccountNumber("E2EACC001");
        createRequest.setServiceClassCode("RES001");
        createRequest.setMaintenanceLevel("STANDARD");
        
        MvcResult createResult = mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.customerStatus", is("ACTIVE")))
                .andExpect(jsonPath("$.customerType", is("RESIDENTIAL")))
                .andReturn();
        
        CustomerDto createdCustomer = objectMapper.readValue(
                createResult.getResponse().getContentAsString(), CustomerDto.class);
        String customerCode = createdCustomer.getCustomerCode();
        
        // Step 2: Verify customer can be retrieved by ID (legacy lookup)
        mockMvc.perform(get("/api/v1/customers/" + customerCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.customerCode", is(customerCode)))
                .andExpect(jsonPath("$.billingTelephoneNumber", is("**********")))
                .andExpect(jsonPath("$.customerStatus", is("ACTIVE")));
        
        // Step 3: Verify customer can be found by phone (legacy search)
        mockMvc.perform(get("/api/v1/customers/phone/**********"))
                .andExpect(status().isOk())
                .andExpected(jsonPath("$.customerCode", is(customerCode)));
        
        // Step 4: Update customer information (legacy update workflow)
        UpdateCustomerRequest updateRequest = new UpdateCustomerRequest();
        updateRequest.setCustomerName("Updated Legacy Test Customer");
        updateRequest.setServiceAddress("456 Updated Legacy St, Updated City, UC 54321");
        updateRequest.setContactPhone("5559876543");
        
        mockMvc.perform(put("/api/v1/customers/" + customerCode)
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpected(jsonPath("$.customerName", is("Updated Legacy Test Customer")))
                .andExpected(jsonPath("$.serviceAddress", is("456 Updated Legacy St, Updated City, UC 54321")));
        
        // Step 5: Deactivate customer (legacy deactivation workflow)
        mockMvc.perform(put("/api/v1/customers/" + customerCode + "/deactivate")
                .with(csrf()))
                .andExpect(status().isOk())
                .andExpected(jsonPath("$.customerStatus", is("INACTIVE")));
        
        // Step 6: Verify deactivated customer cannot be activated by regular user
        mockMvc.perform(put("/api/v1/customers/" + customerCode + "/activate")
                .with(csrf()))
                .andExpect(status().isForbidden()); // Only ADMIN/SUPERVISOR can activate
        
        // Step 7: Reactivate customer as admin (legacy reactivation workflow)
        mockMvc.perform(put("/api/v1/customers/" + customerCode + "/activate")
                .with(csrf()))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.customerStatus", is("ACTIVE")));
        
        // Step 8: Search for customer in results (legacy search workflow)
        mockMvc.perform(get("/api/v1/customers/search")
                .param("customerName", "Updated Legacy")
                .param("page", "0")
                .param("size", "10"))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.content[0].customerCode", is(customerCode)))
                .andExpected(jsonPath("$.totalElements", is(1)));
    }
    
    /**
     * Test business customer workflow with account requirements.
     * Validates business-specific rules from legacy C++ system.
     */
    @Test
    @WithMockUser(roles = "USER")
    void businessCustomerWorkflow_E2EWorkflow_ValidatesBusinessRules() throws Exception {
        // Step 1: Attempt to create business customer without account number (should fail)
        CreateCustomerRequest invalidBusiness = new CreateCustomerRequest();
        invalidBusiness.setBillingTelephoneNumber("**********");
        invalidBusiness.setServiceAddress("789 Business Ave, Business City, BC 67890");
        invalidBusiness.setCustomerName("Test Business Corp");
        invalidBusiness.setCustomerType("BUSINESS");
        // Missing account number - legacy rule violation
        
        mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidBusiness)))
                .andExpected(status().isBadRequest());
        
        // Step 2: Create valid business customer with account number
        CreateCustomerRequest validBusiness = new CreateCustomerRequest();
        validBusiness.setBillingTelephoneNumber("**********");
        validBusiness.setServiceAddress("789 Business Ave, Business City, BC 67890");
        validBusiness.setCustomerName("Test Business Corp");
        validBusiness.setCustomerType("BUSINESS");
        validBusiness.setAccountNumber("BIZACC001"); // Required for business customers
        validBusiness.setServiceClassCode("BUS001");
        validBusiness.setMaintenanceLevel("PREMIUM");
        
        MvcResult businessResult = mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validBusiness)))
                .andExpected(status().isCreated())
                .andExpected(jsonPath("$.customerType", is("BUSINESS")))
                .andExpected(jsonPath("$.accountNumber", is("BIZACC001")))
                .andReturn();
        
        CustomerDto businessCustomer = objectMapper.readValue(
                businessResult.getResponse().getContentAsString(), CustomerDto.class);
        
        // Step 3: Verify business customer code follows legacy pattern (BUS prefix)
        assertThat(businessCustomer.getCustomerCode()).startsWith("BUS");
        
        // Step 4: Verify business customer appears in business customer statistics
        mockMvc.perform(get("/api/v1/customers/statistics"))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.businessCustomers").value(greaterThan(0)));
    }
    
    /**
     * Test error handling workflow matches legacy system behavior.
     * Validates that error responses are consistent with legacy expectations.
     */
    @Test
    @WithMockUser(roles = "USER")
    void errorHandlingWorkflow_E2EWorkflow_MatchesLegacyErrorBehavior() throws Exception {
        // Test 1: Invalid phone number format (legacy validation)
        CreateCustomerRequest invalidPhone = new CreateCustomerRequest();
        invalidPhone.setBillingTelephoneNumber("************"); // Hyphens not allowed in legacy
        invalidPhone.setServiceAddress("123 Error Test St");
        invalidPhone.setCustomerName("Error Test Customer");
        invalidPhone.setCustomerType("RESIDENTIAL");
        
        mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidPhone)))
                .andExpected(status().isBadRequest())
                .andExpected(jsonPath("$.errorCode").exists())
                .andExpected(jsonPath("$.message").exists())
                .andExpected(jsonPath("$.correlationId").exists());
        
        // Test 2: Customer not found (legacy error handling)
        mockMvc.perform(get("/api/v1/customers/NONEXISTENT"))
                .andExpected(status().isNotFound())
                .andExpected(jsonPath("$.errorCode", is("CUSTOMER_NOT_FOUND")));
        
        // Test 3: Duplicate phone number (legacy uniqueness constraint)
        CreateCustomerRequest customer1 = createValidCustomerRequest("5553456789", "Customer 1");
        CreateCustomerRequest customer2 = createValidCustomerRequest("5553456789", "Customer 2"); // Same phone
        
        // Create first customer
        mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(customer1)))
                .andExpected(status().isCreated());
        
        // Attempt to create second customer with same phone (should fail like legacy)
        mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(customer2)))
                .andExpected(status().isConflict())
                .andExpected(jsonPath("$.errorCode", is("CUSTOMER_ALREADY_EXISTS")));
    }
    
    /**
     * Test performance workflow meets legacy system performance requirements.
     * Validates response times are within acceptable limits.
     */
    @Test
    @WithMockUser(roles = "USER")
    void performanceWorkflow_E2EWorkflow_MeetsPerformanceTargets() throws Exception {
        // Create test customer for performance testing
        CreateCustomerRequest perfCustomer = createValidCustomerRequest("5554567890", "Performance Test Customer");
        
        MvcResult createResult = mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(perfCustomer)))
                .andExpected(status().isCreated())
                .andReturn();
        
        CustomerDto customer = objectMapper.readValue(
                createResult.getResponse().getContentAsString(), CustomerDto.class);
        String customerCode = customer.getCustomerCode();
        
        // Test customer lookup performance (should be <1 second like legacy)
        long startTime = System.currentTimeMillis();
        
        mockMvc.perform(get("/api/v1/customers/" + customerCode))
                .andExpected(status().isOk());
        
        long endTime = System.currentTimeMillis();
        long responseTime = endTime - startTime;
        
        // Validate response time meets legacy performance target
        assertThat(responseTime).isLessThan(1000); // <1 second
        
        // Test search performance
        startTime = System.currentTimeMillis();
        
        mockMvc.perform(get("/api/v1/customers/search")
                .param("customerName", "Performance")
                .param("page", "0")
                .param("size", "10"))
                .andExpected(status().isOk());
        
        endTime = System.currentTimeMillis();
        responseTime = endTime - startTime;
        
        // Search should also be fast like legacy system
        assertThat(responseTime).isLessThan(2000); // <2 seconds for search
    }
    
    // Helper method
    private CreateCustomerRequest createValidCustomerRequest(String phoneNumber, String customerName) {
        CreateCustomerRequest request = new CreateCustomerRequest();
        request.setBillingTelephoneNumber(phoneNumber);
        request.setServiceAddress("123 Test St, Test City, TC 12345");
        request.setCustomerName(customerName);
        request.setCustomerType("RESIDENTIAL");
        request.setAccountNumber("TESTACC" + phoneNumber.substring(6));
        request.setServiceClassCode("TEST001");
        request.setMaintenanceLevel("STANDARD");
        return request;
    }
}
