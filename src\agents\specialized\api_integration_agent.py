# from ..core.base_agent import BaseAgent

# class APIIntegrationAgent(BaseAgent):
#     """Agent for API integration tasks."""
    
#     def __init__(self, claude_client, name, api_key):
#         super().__init__(
#             name=name,
#             claude_client=claude_client,
#             system_message="You are an API expert. Perform tasks related to API analysis, design, testing, and optimization.",
#             api_key=api_key
#         )

#     def handle_task(self, task_description):
#         """Perform API-related tasks."""
#         return self.generate_reply([{"content": task_description}])


from ..core.base_agent import BaseAgent

class APIIntegrationAgent(BaseAgent):
    """Agent for API integration tasks."""
    
    def __init__(self, claude_client, name, google_api_key):
        super().__init__(
            name=name,
            claude_client=claude_client,
            system_message="You are an API expert. Perform tasks related to API analysis, design, testing, and optimization.",
            # api_key=api_key
            google_api_key=google_api_key
        )

    def handle_task(self, task_description):
        """Perform API-related tasks."""
        return self.generate_reply([{"content": task_description}])

    def generate_reply(self, messages=None, **kwargs):
        return super().generate_reply(messages, **kwargs)
