
from .base_agent import BaseAgent
import requests
import sys
from pathlib import Path

# Add project root to path for config imports
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from config.config import JIRA_API_CONFIG

class QAAgent(BaseAgent):
    """Agent for quality assurance and testing tasks."""

    def __init__(self, claude_client, name="QAAgent", api_key=None):
        super().__init__(
            name=name,
            claude_client=claude_client,
            system_message="""
You are a QA/Tester agent. Create test cases, perform automated/manual testing, and report bugs. Your tasks include:
1. Static code analysis to find potential bugs and performance issues, and vulnerabilities with actionable insights.
2. Develop comprehensive test cases (unit, integration,detect bugs,edge cases with clear inputs and outputs).
3. Ensuring compliance with security standards.
Respond in a detailed, structured format for each task.
            """,
            api_key=api_key,
        )

    def analyze_code(self, code_snippet):
        """Perform static code analysis."""
        task = f"Perform static code analysis on the following:\n\n{code_snippet}"
        return self.generate_reply([{"content": task}])

    def generate_test_cases(self, code_snippet):
        """Generate test cases."""
        task = f"Generate detailed test cases (unit, integration, edge cases) for the following code:\n\n{code_snippet}"
        return self.generate_reply([{"content": task}])

    def check_compliance(self, code_snippet):
        """Ensure compliance with security and standards."""
        task = f"Check the following code for security vulnerabilities and compliance with best practices:\n\n{code_snippet}"
        return self.generate_reply([{"content": task}])
   


def create_jira_bug(test_output_summary):
    url = f"{JIRA_API_CONFIG['base_url']}/rest/api/3/issue"
    headers = {
        "Content-Type": "application/json"  
    }
    auth = (JIRA_API_CONFIG["email"], JIRA_API_CONFIG["api_key"])
    
    # Convert plain text to Atlassian Document Format
    adf_description = {
        "type": "doc",
        "version": 1,
        "content": [
            {
                "type": "paragraph",
                "content": [
                    {
                        "type": "text",
                        "text": "QA Test Failure Details:\n" + test_output_summary[:3000]
                    }
                ]
            }
        ]
    }
  
    payload = {
        "fields": {
            "project": {
                "key": JIRA_API_CONFIG["project_key"]
            },
            "summary": "⚠️ QA Test Failure - AutoGen",
            "description": adf_description,
            "issuetype": {
                "name": "Bug"
            }
        }
    }

    response = requests.post(url, json=payload, auth=auth, headers=headers)

    if response.status_code == 201:
        print("✅ Bug created in Jira successfully!")
    else:
        print(f"❌ Failed to create Jira bug. Status: {response.status_code}, Response: {response.text}")