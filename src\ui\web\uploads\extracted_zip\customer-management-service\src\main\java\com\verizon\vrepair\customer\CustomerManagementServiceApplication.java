package com.verizon.vrepair.customer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Customer Management Service Application.
 * 
 * Java modernization of legacy VRepair LSTRESRV C++ customer management functionality.
 * Provides RESTful APIs for customer CRUD operations, search, and business logic.
 * 
 * Key Features:
 * - Customer lifecycle management
 * - Phone number and address validation
 * - Integration with external systems (ETMS, BAAIS, VI)
 * - Comprehensive audit logging
 * - Performance monitoring and caching
 * - OAuth 2.0/JWT security
 * 
 * <AUTHOR> Modernization Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaRepositories
@EnableJpaAuditing
@EnableTransactionManagement
@EnableCaching
public class CustomerManagementServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CustomerManagementServiceApplication.class, args);
    }
}
