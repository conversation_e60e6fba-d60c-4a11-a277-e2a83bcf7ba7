package com.verizon.vrepair.customer.validation;

import com.verizon.vrepair.customer.exception.CustomerValidationException;
import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerType;
import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * Customer data validator.
 * Implements legacy C++ validation rules with modern Java validation patterns.
 * 
 * Replaces legacy C++ validation functions:
 * - validate_phone_number()
 * - validate_address()
 * - validate_customer_data()
 * - validate_business_rules()
 */
@Component
public class CustomerValidator {
    
    // Legacy validation patterns
    private static final Pattern PHONE_PATTERN = Pattern.compile("^\\d{10}$");
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    
    /**
     * Validate customer data for creation.
     * Implements all legacy C++ validation rules.
     */
    public void validateCustomerForCreation(Customer customer) {
        if (customer == null) {
            throw new CustomerValidationException("Customer data cannot be null");
        }
        
        // Validate required fields
        validateRequiredFields(customer);
        
        // Validate phone number format
        validatePhoneNumber(customer.getBillingTelephoneNumber(), "billingTelephoneNumber");
        
        // Validate address
        validateAddress(customer.getServiceAddress());
        
        // Validate optional phone numbers
        if (customer.getContactPhone() != null && !customer.getContactPhone().trim().isEmpty()) {
            validatePhoneNumber(customer.getContactPhone(), "contactPhone");
        }
        
        if (customer.getAlternateContact() != null && !customer.getAlternateContact().trim().isEmpty()) {
            validatePhoneNumber(customer.getAlternateContact(), "alternateContact");
        }
        
        // Validate email if provided
        if (customer.getEmailAddress() != null && !customer.getEmailAddress().trim().isEmpty()) {
            validateEmailAddress(customer.getEmailAddress());
        }
        
        // Validate business rules
        validateBusinessRules(customer);
    }
    
    /**
     * Validate customer data for update.
     * Less strict validation for partial updates.
     */
    public void validateCustomerForUpdate(Customer customer) {
        if (customer == null) {
            throw new CustomerValidationException("Customer data cannot be null");
        }
        
        // Validate address if provided
        if (customer.getServiceAddress() != null && !customer.getServiceAddress().trim().isEmpty()) {
            validateAddress(customer.getServiceAddress());
        }
        
        // Validate optional phone numbers if provided
        if (customer.getContactPhone() != null && !customer.getContactPhone().trim().isEmpty()) {
            validatePhoneNumber(customer.getContactPhone(), "contactPhone");
        }
        
        if (customer.getAlternateContact() != null && !customer.getAlternateContact().trim().isEmpty()) {
            validatePhoneNumber(customer.getAlternateContact(), "alternateContact");
        }
        
        // Validate email if provided
        if (customer.getEmailAddress() != null && !customer.getEmailAddress().trim().isEmpty()) {
            validateEmailAddress(customer.getEmailAddress());
        }
    }
    
    /**
     * Validate required fields for customer creation.
     */
    private void validateRequiredFields(Customer customer) {
        if (customer.getBillingTelephoneNumber() == null || 
            customer.getBillingTelephoneNumber().trim().isEmpty()) {
            throw new CustomerValidationException("Billing telephone number is required", 
                    "billingTelephoneNumber");
        }
        
        if (customer.getServiceAddress() == null || 
            customer.getServiceAddress().trim().isEmpty()) {
            throw new CustomerValidationException("Service address is required", 
                    "serviceAddress");
        }
        
        if (customer.getCustomerType() == null) {
            throw new CustomerValidationException("Customer type is required", 
                    "customerType");
        }
    }
    
    /**
     * Validate phone number format.
     * Legacy C++ rule: exactly 10 digits, no formatting characters.
     */
    public boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber != null && PHONE_PATTERN.matcher(phoneNumber).matches();
    }
    
    private void validatePhoneNumber(String phoneNumber, String fieldName) {
        if (!isValidPhoneNumber(phoneNumber)) {
            throw new CustomerValidationException(
                    "Phone number must be exactly 10 digits: " + phoneNumber, fieldName);
        }
    }
    
    /**
     * Validate service address.
     * Legacy C++ rules: required, max 100 characters, basic format validation.
     */
    private void validateAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            throw new CustomerValidationException("Service address cannot be empty", "serviceAddress");
        }
        
        if (address.length() > 100) {
            throw new CustomerValidationException(
                    "Service address cannot exceed 100 characters", "serviceAddress");
        }
        
        // Basic format validation - address should contain some alphanumeric characters
        if (!address.matches(".*[a-zA-Z0-9].*")) {
            throw new CustomerValidationException(
                    "Service address must contain valid address information", "serviceAddress");
        }
    }
    
    /**
     * Validate email address format.
     */
    private void validateEmailAddress(String email) {
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw new CustomerValidationException(
                    "Invalid email address format: " + email, "emailAddress");
        }
    }
    
    /**
     * Validate business rules.
     * Legacy C++ business rules:
     * - Business customers must have account number
     * - Government customers must have account number
     * - Customer name length restrictions
     */
    private void validateBusinessRules(Customer customer) {
        // Business customer validation
        if (customer.getCustomerType() == CustomerType.BUSINESS) {
            if (customer.getAccountNumber() == null || customer.getAccountNumber().trim().isEmpty()) {
                throw new CustomerValidationException(
                        "Business customers must have an account number", "accountNumber");
            }
        }
        
        // Government customer validation
        if (customer.getCustomerType() == CustomerType.GOVERNMENT) {
            if (customer.getAccountNumber() == null || customer.getAccountNumber().trim().isEmpty()) {
                throw new CustomerValidationException(
                        "Government customers must have an account number", "accountNumber");
            }
        }
        
        // Customer name validation
        if (customer.getCustomerName() != null && customer.getCustomerName().length() > 50) {
            throw new CustomerValidationException(
                    "Customer name cannot exceed 50 characters", "customerName");
        }
        
        // Account number validation
        if (customer.getAccountNumber() != null && customer.getAccountNumber().length() > 20) {
            throw new CustomerValidationException(
                    "Account number cannot exceed 20 characters", "accountNumber");
        }
        
        // Service class code validation
        if (customer.getServiceClassCode() != null && customer.getServiceClassCode().length() > 10) {
            throw new CustomerValidationException(
                    "Service class code cannot exceed 10 characters", "serviceClassCode");
        }
        
        // Maintenance level validation
        if (customer.getMaintenanceLevel() != null) {
            if (customer.getMaintenanceLevel().length() > 20) {
                throw new CustomerValidationException(
                        "Maintenance level cannot exceed 20 characters", "maintenanceLevel");
            }
            
            // Validate against allowed values (legacy enumeration)
            String level = customer.getMaintenanceLevel().toUpperCase();
            if (!level.equals("BASIC") && !level.equals("STANDARD") && 
                !level.equals("PREMIUM") && !level.equals("ENHANCED")) {
                throw new CustomerValidationException(
                        "Invalid maintenance level. Must be BASIC, STANDARD, PREMIUM, or ENHANCED", 
                        "maintenanceLevel");
            }
        }
        
        // Special instructions validation
        if (customer.getSpecialInstructions() != null && 
            customer.getSpecialInstructions().length() > 500) {
            throw new CustomerValidationException(
                    "Special instructions cannot exceed 500 characters", "specialInstructions");
        }
    }
    
    /**
     * Validate customer code format.
     * Legacy format: [TYPE_PREFIX][6_DIGITS]
     */
    public boolean isValidCustomerCode(String customerCode) {
        if (customerCode == null || customerCode.length() != 9) {
            return false;
        }
        
        String prefix = customerCode.substring(0, 3);
        String sequence = customerCode.substring(3);
        
        // Validate prefix
        if (!prefix.equals("RES") && !prefix.equals("BUS") && 
            !prefix.equals("GOV") && !prefix.equals("WHO")) {
            return false;
        }
        
        // Validate sequence is 6 digits
        return sequence.matches("\\d{6}");
    }
}


