# Selenium Test Agent - Production-Ready Automation

A comprehensive Selenium testing agent built on AutoGen framework with multi-browser support, JSON test scenario parsing, and enterprise-grade features.

## 🚀 Features

### Core Capabilities
- **Multi-Browser Support**: Chrome, Firefox, Edge with webdriver-manager integration
- **JSON Test Scenarios**: Parse and execute complex test scenarios from JSON files
- **All Selenium Actions**: Navigate, click, type, select, upload, alerts, frames, windows
- **All Locator Strategies**: ID, XPath, CSS, Name, Class, Link Text, Partial Link Text
- **Dynamic Waits**: Intelligent waiting with multiple conditions
- **Comprehensive Error Handling**: Retry mechanisms with detailed logging
- **Screenshot Capture**: Automatic screenshots on failures and manual capture
- **Structured Logging**: Production-ready logging with multiple levels
- **Test Data Management**: JSON and CSV data loading capabilities
- **Environment Configuration**: YAML-based configuration management
- **Cross-Platform Compatibility**: Windows, macOS, Linux support

### Advanced Features
- **AutoGen Integration**: Seamless integration with AutoGen agent framework
- **Test Reporting**: HTML report generation with detailed results
- **Data-Driven Testing**: CSV and JSON data source support
- **Frame/Window Switching**: Complete iframe and multi-window support
- **JavaScript Execution**: Execute custom JavaScript in browser context
- **Alert Handling**: Complete JavaScript alert/confirm/prompt support
- **File Upload**: Robust file upload functionality
- **Element Verification**: Text and attribute verification capabilities
- **Test Suite Management**: Complete test suite execution and reporting

## 📦 Installation

### Dependencies
```bash
pip install selenium>=4.15.0 webdriver-manager Pillow pandas pyyaml autogen-agentchat
```

### Required Packages
- `selenium>=4.15.0` - Web automation framework
- `webdriver-manager` - Automatic webdriver management
- `Pillow` - Image processing for screenshots
- `pandas` - Data manipulation for CSV handling
- `pyyaml` - YAML configuration file support
- `autogen-agentchat` - AutoGen framework integration

## 🏗️ Project Structure

```
dynamic_ui/
├── local_agents/
│   └── selenium_test_agent.py          # Main agent implementation
├── test_data/
│   ├── example_test_suite.json         # Example test scenarios
│   ├── selenium_config.yaml            # Configuration file
│   ├── test_data.csv                   # Sample test data
│   └── sample_upload.txt               # Sample upload file
├── test_screenshots/                   # Screenshot storage
├── test_logs/                          # Log file storage
├── selenium_agent_example.py           # Usage examples
└── SELENIUM_AGENT_README.md           # This documentation
```

## 🚀 Quick Start

### Basic Usage

```python
from local_agents.selenium_test_agent import SeleniumTestAgent

# Initialize agent
agent = SeleniumTestAgent(name="MyTestAgent")

# Setup browser
agent.setup_browser(browser="chrome", headless=False)

# Navigate and interact
agent.navigate_to("https://example.com")
agent.type_text("id", "username", "testuser")
agent.click_element("xpath", "//button[@type='submit']")

# Capture screenshot
agent.capture_screenshot("test_result")

# Cleanup
agent.cleanup()
```

### Test Suite Execution

```python
# Run complete test suite
results = agent.run_test_suite("test_data/example_test_suite.json")

# Generate HTML report
report_path = agent.generate_test_report(results)
print(f"Report generated: {report_path}")
```

### AutoGen Integration

```python
# Use as AutoGen agent
messages = [{"content": "run test file: test_data/example_test_suite.json"}]
response = agent.generate_reply(messages)
print(response)
```

## 📋 Test Scenario Format

### JSON Test Suite Structure

```json
{
  "suite_name": "My Test Suite",
  "browser": "chrome",
  "headless": false,
  "scenarios": [
    {
      "name": "Login Test",
      "description": "Test user login functionality",
      "stop_on_failure": true,
      "steps": [
        {
          "action": "navigate",
          "url": "https://example.com/login"
        },
        {
          "action": "type",
          "locator_type": "id",
          "locator_value": "username",
          "text": "testuser"
        },
        {
          "action": "click",
          "locator_type": "xpath",
          "locator_value": "//button[@type='submit']"
        },
        {
          "action": "wait",
          "locator_type": "class",
          "locator_value": "dashboard",
          "condition": "visible",
          "timeout": 10
        },
        {
          "action": "verify_text",
          "locator_type": "class",
          "locator_value": "welcome-message",
          "expected_text": "Welcome"
        },
        {
          "action": "screenshot",
          "filename": "login_success"
        }
      ]
    }
  ]
}
```

## 🎯 Supported Actions

### Navigation Actions
- `navigate` - Navigate to URL
- `javascript` - Execute JavaScript code

### Element Interaction
- `click` - Click element
- `type` - Type text into element
- `select` - Select from dropdown
- `upload` - Upload file

### Verification Actions
- `verify_text` - Verify element text content
- `verify_attribute` - Verify element attribute value

### Wait Actions
- `wait` - Wait for element with conditions:
  - `presence` - Element exists in DOM
  - `visible` - Element is visible
  - `clickable` - Element is clickable
  - `invisible` - Element is not visible

### Window/Frame Management
- `switch_frame` - Switch to iframe
- `switch_default` - Switch to default content
- `switch_window` - Switch between windows

### Alert Handling
- `alert` - Handle JavaScript alerts
  - `accept` - Accept alert
  - `dismiss` - Dismiss alert

### Utility Actions
- `screenshot` - Capture screenshot
- `sleep` - Wait for specified duration

## 🔧 Configuration

### YAML Configuration File

```yaml
browser: chrome
headless: false
implicit_wait: 10
explicit_wait: 30
page_load_timeout: 60
window_size:
  width: 1920
  height: 1080
retry_attempts: 3
retry_delay: 2
screenshot_on_failure: true
environment: test

environments:
  test:
    base_url: "https://test.example.com"
    timeout: 30
  production:
    base_url: "https://example.com"
    timeout: 60

test_data:
  users:
    - username: "testuser1"
      password: "testpass1"
```

## 📊 Data-Driven Testing

### CSV Data Format

```csv
username,password,email,first_name,last_name
testuser1,SecurePass123!,<EMAIL>,John,Doe
testuser2,SecurePass456!,<EMAIL>,Jane,Smith
```

### Loading Test Data

```python
# Load CSV data
test_data = agent.load_test_data_csv("test_data/users.csv")

# Load JSON data
test_config = agent.load_test_data_json("test_data/config.json")

# Use in tests
for user in test_data:
    agent.type_text("id", "username", user["username"])
    agent.type_text("id", "password", user["password"])
```

## 🎨 UI Integration

The Selenium Test Agent is fully integrated into the dynamic UI:

1. **Agent Selection**: Choose "Selenium Test Agent" from the agent list
2. **Dynamic Input**: Provide test instructions or file paths
3. **Execution**: Agent runs tests with permission system
4. **Results**: View detailed results with screenshots and reports
5. **GitHub Integration**: Push test results to GitHub repositories

### UI Usage Examples

```
# Run test suite
test_suite: example_test_suite.json

# Simple browser automation
browser: chrome url: https://www.google.com

# Custom test scenario (will be generated by AI)
Test login functionality for an e-commerce website with username and password fields
```

## 📈 Test Reporting

### HTML Report Features
- Executive summary with pass/fail statistics
- Detailed scenario breakdown
- Step-by-step execution details
- Screenshot integration
- Execution timing information
- Error details and stack traces

### Report Generation

```python
# Generate HTML report
report_path = agent.generate_test_report(test_results)

# Report includes:
# - Test suite summary
# - Individual scenario results
# - Step execution details
# - Screenshots and error information
# - Execution metrics
```

## 🔍 Locator Strategies

### Supported Locator Types

| Type | Description | Example |
|------|-------------|---------|
| `id` | Element ID | `"login-button"` |
| `name` | Element name attribute | `"username"` |
| `class` | CSS class name | `"btn-primary"` |
| `tag` | HTML tag name | `"button"` |
| `xpath` | XPath expression | `"//button[@type='submit']"` |
| `css` | CSS selector | `"button.btn-primary"` |
| `link_text` | Exact link text | `"Click Here"` |
| `partial_link_text` | Partial link text | `"Click"` |

## 🌐 Multi-Browser Support

### Supported Browsers
- **Chrome** - Default browser with extensive options
- **Firefox** - Gecko-based browser support
- **Edge** - Microsoft Edge Chromium support

### Browser Setup

```python
# Chrome with options
agent.setup_browser(browser="chrome", headless=True)

# Firefox
agent.setup_browser(browser="firefox", headless=False)

# Edge
agent.setup_browser(browser="edge", headless=True)
```

## 🛡️ Error Handling

### Retry Mechanisms
- Configurable retry attempts for failed actions
- Automatic retry delays
- Comprehensive error logging
- Screenshot capture on failures

### Exception Handling
- `TimeoutException` - Element not found within timeout
- `NoSuchElementException` - Element doesn't exist
- `ElementNotInteractableException` - Element not clickable
- `StaleElementReferenceException` - Element reference outdated
- `WebDriverException` - General WebDriver errors

## 📝 Logging

### Log Levels
- `INFO` - General information and successful operations
- `WARNING` - Non-critical issues and retries
- `ERROR` - Failed operations and exceptions
- `DEBUG` - Detailed debugging information

### Log Output
- Console output for real-time monitoring
- File output for persistent logging
- Structured format with timestamps
- Separate log files per test session

## 🔧 Advanced Features

### JavaScript Execution

```python
# Execute JavaScript
result = agent.execute_javascript("return document.title;")
agent.execute_javascript("window.scrollTo(0, document.body.scrollHeight);")
```

### Frame Switching

```python
# Switch to iframe
agent.switch_to_frame("content-frame")
agent.click_element("id", "frame-button")
agent.switch_to_default_content()
```

### Window Management

```python
# Switch between windows
agent.switch_to_window(window_index=1)
agent.switch_to_window(window_handle="window-handle")
```

### Alert Handling

```python
# Handle different alert types
agent.handle_alert("accept")
agent.handle_alert("dismiss")
agent.handle_alert("accept", text="Input text")
```

## 🚀 Production Deployment

### Best Practices
1. **Headless Mode**: Use headless browsers in CI/CD
2. **Explicit Waits**: Always use explicit waits over implicit
3. **Error Handling**: Implement comprehensive error handling
4. **Screenshot Strategy**: Capture screenshots on failures
5. **Test Data Management**: Use external data sources
6. **Configuration Management**: Environment-specific configs
7. **Parallel Execution**: Consider parallel test execution
8. **Resource Cleanup**: Always cleanup browser resources

### CI/CD Integration

```yaml
# Example GitHub Actions workflow
name: Selenium Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run Selenium tests
        run: python selenium_agent_example.py
```

## 📚 Examples

### Complete Examples Available
- `selenium_agent_example.py` - Comprehensive usage examples
- `test_data/example_test_suite.json` - Sample test scenarios
- `test_data/selenium_config.yaml` - Configuration examples
- `test_data/test_data.csv` - Sample test data

### Running Examples

```bash
# Run all examples
python selenium_agent_example.py

# Run specific example functions
python -c "from selenium_agent_example import example_basic_usage; example_basic_usage()"
```

## 🤝 AutoGen Integration

### Agent Communication

```python
# Initialize as AutoGen agent
agent = SeleniumTestAgent(name="SeleniumAgent", claude_client=claude_client)

# Handle messages
messages = [{"content": "setup browser chrome headless"}]
response = agent.generate_reply(messages)

# Supported message formats:
# - "setup browser [browser] [headless]"
# - "navigate to [url]"
# - "run test file: [path]"
```

### Message Handling
- Automatic parsing of test requests
- Browser setup commands
- Navigation instructions
- Test suite execution
- Result reporting

## 🔒 Security Considerations

### File Upload Security
- Validates file existence before upload
- Uses absolute file paths
- Handles file permission errors

### Browser Security
- Runs with security flags enabled
- Supports sandboxed execution
- Handles SSL certificate issues

### Data Security
- Secure handling of test credentials
- No sensitive data in logs
- Configurable data masking

## 📋 Troubleshooting

### Common Issues

1. **WebDriver Not Found**
   - Solution: webdriver-manager handles automatic downloads
   - Check internet connectivity

2. **Element Not Found**
   - Increase wait timeouts
   - Verify locator strategies
   - Check element visibility

3. **Browser Crashes**
   - Use headless mode
   - Increase memory limits
   - Check browser compatibility

4. **Permission Errors**
   - Run with appropriate permissions
   - Check file system access
   - Verify screenshot directory permissions

### Debug Mode

```python
# Enable debug logging
import logging
logging.getLogger().setLevel(logging.DEBUG)

# Capture additional screenshots
agent.config["screenshot_on_failure"] = True
```

## 🎯 Future Enhancements

### Planned Features
- Mobile browser support (Chrome Mobile, Safari)
- Docker container integration
- Kubernetes deployment support
- Advanced reporting with charts
- Integration with test management tools
- AI-powered test generation
- Visual regression testing
- Performance testing capabilities

### Contributing
- Follow existing code patterns
- Add comprehensive tests
- Update documentation
- Maintain backward compatibility

## 📄 License

This Selenium Test Agent is part of the dynamic UI project and follows the same licensing terms.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review example implementations
3. Check log files for detailed error information
4. Verify configuration settings
5. Test with minimal scenarios first

---

**Ready for Production** ✅ **Cross-Platform** ✅ **AutoGen Compatible** ✅ **Comprehensive Testing** ✅