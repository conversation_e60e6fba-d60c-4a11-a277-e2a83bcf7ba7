package com.verizon.vrepair.customer.exception;

/**
 * Exception thrown when attempting to create a customer that already exists.
 * Replaces legacy C++ error code CUSTOMER_ALREADY_EXISTS.
 */
public class CustomerAlreadyExistsException extends RuntimeException {
    
    private final String phoneNumber;
    
    public CustomerAlreadyExistsException(String message) {
        super(message);
        this.phoneNumber = null;
    }
    
    public CustomerAlreadyExistsException(String message, String phoneNumber) {
        super(message);
        this.phoneNumber = phoneNumber;
    }
    
    public CustomerAlreadyExistsException(String message, Throwable cause) {
        super(message, cause);
        this.phoneNumber = null;
    }
    
    public String getPhoneNumber() {
        return phoneNumber;
    }
}


