#!/usr/bin/env python3
"""
RadAgents - AI Agent Automation Platform
Main entry point for the application.

This script provides a unified entry point that can launch different interfaces:
- CLI interface (default)
- Web UI applications
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Main entry point for RadAgents."""
    print("🚀 Welcome to RadAgents - AI Agent Automation Platform")
    print("=" * 60)
    
    # Add current directory to Python path
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    print("\n🎯 Available interfaces:")
    print("1. CLI Interface (Interactive Command Line)")
    print("2. Web UI - Full Application")
    print("3. Web UI - Simple Interface")
    print("4. Web UI - Dynamic Interface")
    print("5. Exit")
    
    while True:
        try:
            choice = input("\nSelect an interface (1-5): ").strip()
            
            if choice == "1":
                print("\n🚀 Starting CLI Interface...")
                from src.ui.cli.main import run_workflow
                run_workflow()
                break
                
            elif choice == "2":
                print("\n🚀 Starting Full Web UI...")
                subprocess.run([sys.executable, "-m", "streamlit", "run", "src/ui/web/ui_app.py"])
                break
                
            elif choice == "3":
                print("\n🚀 Starting Simple Web UI...")
                subprocess.run([sys.executable, "-m", "streamlit", "run", "src/ui/web/simple_ui.py"])
                break
                
            elif choice == "4":
                print("\n🚀 Starting Dynamic Web UI...")
                subprocess.run([sys.executable, "-m", "streamlit", "run", "src/ui/web/dynamic_ui.py"])
                break
                
            elif choice == "5":
                print("\n👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please select 1-5.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            print("Please try again.")

if __name__ == "__main__":
    main()