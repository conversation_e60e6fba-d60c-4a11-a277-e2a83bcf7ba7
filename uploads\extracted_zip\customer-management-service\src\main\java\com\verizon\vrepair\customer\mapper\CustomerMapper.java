package com.verizon.vrepair.customer.mapper;

import com.verizon.vrepair.customer.dto.CreateCustomerRequest;
import com.verizon.vrepair.customer.dto.CustomerDto;
import com.verizon.vrepair.customer.dto.PagedCustomerResponse;
import com.verizon.vrepair.customer.dto.UpdateCustomerRequest;
import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between Customer entities and DTOs.
 * Handles the mapping between internal domain model and external API contract.
 */
@Component
public class CustomerMapper {
    
    /**
     * Convert CreateCustomerRequest DTO to Customer entity.
     */
    public Customer toEntity(CreateCustomerRequest request) {
        if (request == null) {
            return null;
        }
        
        Customer customer = new Customer();
        customer.setBillingTelephoneNumber(request.getBillingTelephoneNumber());
        customer.setServiceAddress(request.getServiceAddress());
        customer.setCustomerName(request.getCustomerName());
        
        // Convert string to enum
        if (request.getCustomerType() != null) {
            customer.setCustomerType(CustomerType.valueOf(request.getCustomerType()));
        }
        
        customer.setAccountNumber(request.getAccountNumber());
        customer.setServiceClassCode(request.getServiceClassCode());
        customer.setMaintenanceLevel(request.getMaintenanceLevel());
        customer.setContactPhone(request.getContactPhone());
        customer.setAlternateContact(request.getAlternateContact());
        customer.setEmailAddress(request.getEmailAddress());
        customer.setSpecialInstructions(request.getSpecialInstructions());
        
        return customer;
    }
    
    /**
     * Convert UpdateCustomerRequest DTO to Customer entity.
     */
    public Customer toEntity(UpdateCustomerRequest request) {
        if (request == null) {
            return null;
        }
        
        Customer customer = new Customer();
        customer.setCustomerName(request.getCustomerName());
        customer.setServiceAddress(request.getServiceAddress());
        customer.setContactPhone(request.getContactPhone());
        customer.setAlternateContact(request.getAlternateContact());
        customer.setEmailAddress(request.getEmailAddress());
        customer.setSpecialInstructions(request.getSpecialInstructions());
        
        return customer;
    }
    
    /**
     * Convert Customer entity to CustomerDto.
     */
    public CustomerDto toDto(Customer customer) {
        if (customer == null) {
            return null;
        }
        
        CustomerDto dto = new CustomerDto();
        dto.setCustomerCode(customer.getCustomerCode());
        dto.setBillingTelephoneNumber(customer.getBillingTelephoneNumber());
        dto.setServiceAddress(customer.getServiceAddress());
        dto.setCustomerName(customer.getCustomerName());
        
        // Convert enum to string
        if (customer.getCustomerType() != null) {
            dto.setCustomerType(customer.getCustomerType().name());
        }
        
        if (customer.getCustomerStatus() != null) {
            dto.setCustomerStatus(customer.getCustomerStatus().name());
        }
        
        dto.setAccountNumber(customer.getAccountNumber());
        dto.setServiceClassCode(customer.getServiceClassCode());
        dto.setMaintenanceLevel(customer.getMaintenanceLevel());
        dto.setContactPhone(customer.getContactPhone());
        dto.setAlternateContact(customer.getAlternateContact());
        dto.setEmailAddress(customer.getEmailAddress());
        dto.setSpecialInstructions(customer.getSpecialInstructions());
        dto.setCreatedDate(customer.getCreatedDate());
        dto.setModifiedDate(customer.getModifiedDate());
        dto.setCreatedBy(customer.getCreatedBy());
        dto.setModifiedBy(customer.getModifiedBy());
        
        return dto;
    }
    
    /**
     * Convert list of Customer entities to list of CustomerDto.
     */
    public List<CustomerDto> toDtoList(List<Customer> customers) {
        if (customers == null) {
            return null;
        }
        
        return customers.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Convert Page of customers to paged response.
     */
    public PagedCustomerResponse toPagedResponse(Page<Customer> customerPage) {
        if (customerPage == null) {
            return new PagedCustomerResponse();
        }
        
        List<CustomerDto> customerDtos = toDtoList(customerPage.getContent());
        
        return new PagedCustomerResponse(
                customerDtos,
                customerPage.getNumber(),
                customerPage.getSize(),
                customerPage.getTotalElements(),
                customerPage.getTotalPages(),
                customerPage.isFirst(),
                customerPage.isLast()
        );
    }
    
    /**
     * Convert list of customers to paged response.
     */
    public PagedCustomerResponse toPagedResponse(List<Customer> customers, int page, int size) {
        if (customers == null) {
            return new PagedCustomerResponse();
        }
        
        List<CustomerDto> customerDtos = toDtoList(customers);
        
        // Calculate pagination metadata
        long totalElements = customers.size();
        int totalPages = (int) Math.ceil((double) totalElements / size);
        boolean first = page == 0;
        boolean last = page >= totalPages - 1;
        
        return new PagedCustomerResponse(
                customerDtos,
                page,
                size,
                totalElements,
                totalPages,
                first,
                last
        );
    }
    
    /**
     * Convert string to CustomerType enum safely.
     */
    public CustomerType toCustomerType(String customerType) {
        if (customerType == null || customerType.trim().isEmpty()) {
            return null;
        }
        
        try {
            return CustomerType.valueOf(customerType.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid customer type: " + customerType);
        }
    }
    
    /**
     * Convert string to CustomerStatus enum safely.
     */
    public CustomerStatus toCustomerStatus(String customerStatus) {
        if (customerStatus == null || customerStatus.trim().isEmpty()) {
            return null;
        }
        
        try {
            return CustomerStatus.valueOf(customerStatus.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid customer status: " + customerStatus);
        }
    }
}
