#!/bin/bash

# VRepair Customer Management Service - Alternative Port Startup Script
# This script starts the service on port 8081 to avoid conflicts with port 8080

clear
echo "🚀 VRepair Customer Management Service - Alternative Port (8081)"
echo "=================================================================="
echo ""
echo "📋 Configuration:"
echo "   🌐 Server Port:    8081 (alternative port)"
echo "   🗄️  Database:       Oracle (production)"
echo "   🔧 Profile:        altport"
echo "   📂 Context Path:   /customer-service"
echo ""
echo "🌐 Service Endpoints (Alternative Port):"
echo "   🔗 Base URL:       http://localhost:8081/customer-service"
echo "   📝 API Endpoints:  http://localhost:8081/customer-service/api/v1/customers"
echo ""
echo "📖 API Documentation (Alternative Port):"
echo "   📖 Swagger UI:     http://localhost:8081/customer-service/swagger-ui/index.html"
echo "   📄 OpenAPI Spec:   http://localhost:8081/customer-service/v3/api-docs"
echo "   📝 OpenAPI YAML:   http://localhost:8081/customer-service/v3/api-docs.yaml"
echo "   ❤️  Health Check:  http://localhost:8081/customer-service/actuator/health"
echo ""
echo "🔐 Authentication Information:"
echo "   All API endpoints require OAuth 2.0 JWT authentication"
echo "   Include Bearer token in Authorization header"
echo "   Roles: USER, TECHNICIAN, ADMIN, SUPERVISOR"
echo ""
echo "📚 Documentation Files:"
echo "   📖 API Guide:      ./API_Documentation.md"
echo "   📋 Project Summary: ../Customer_Service_MVP_Development_Summary.md"
echo ""
echo "🏃 Starting the service on port 8081..."
echo "   Press Ctrl+C to stop the service"
echo "   The service will start in a few seconds..."
echo ""

# Start the Spring Boot application with the alternative port profile
mvn spring-boot:run -Dspring-boot.run.profiles=altport
