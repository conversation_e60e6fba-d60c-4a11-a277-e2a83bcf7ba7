# from .base_agent import BaseAgent

# class ImproverAgent(BaseAgent):
#     """Agent for improving Python code."""
    
#     def __init__(self, claude_client, name, api_key):
#         super().__init__(
#             name=name,
#             claude_client=claude_client,
#             system_message="You are a code improver. Take the provided Python script and review feedback to create a final improved version with high quality.",
#             api_key=api_key
#         )
#         self.api_key = api_key


from .base_agent import BaseAgent

class ImproverAgent(BaseAgent):
    """Agent for improving code."""
    
    def __init__(self, claude_client, name, api_key):
        super().__init__(
            name=name,
            claude_client=claude_client,
            system_message="""
You are  code improver. Enhance the provided script based on feedback, ensuring it is clean, efficient, and production-ready. 
Address all issues and apply best practices.
""",
            api_key=api_key
        )
        self.api_key = api_key