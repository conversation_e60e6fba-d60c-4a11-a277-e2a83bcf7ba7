#!/usr/bin/env python3
"""
Test script to verify config import works correctly.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print(f"Project root: {project_root}")
print(f"Python path: {sys.path[:3]}")

try:
    from config.config import API_KEY, JIRA_API_CONFIG
    print("SUCCESS: Config import successful!")
    print(f"API_KEY exists: {'Yes' if API_KEY else 'No'}")
    print(f"JIRA config exists: {'Yes' if JIRA_API_CONFIG else 'No'}")
except ImportError as e:
    print(f"ERROR: Config import failed: {e}")
    
    # Try alternative import
    try:
        import config.config as cfg
        print("SUCCESS: Alternative config import successful!")
        print(f"API_KEY exists: {'Yes' if hasattr(cfg, 'API_KEY') and cfg.API_KEY else 'No'}")
    except ImportError as e2:
        print(f"ERROR: Alternative config import also failed: {e2}")
        
        # List what's actually in the config directory
        config_dir = project_root / "config"
        if config_dir.exists():
            print(f"Config directory contents: {list(config_dir.iterdir())}")
        else:
            print("Config directory does not exist!")