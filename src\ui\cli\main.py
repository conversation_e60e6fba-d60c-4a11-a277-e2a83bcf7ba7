import os
import sys
import subprocess
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

os.environ["OPENAI_API_KEY"] = "sk-dummy-key"

from anthropic import Anthropic

# Import agents from the new structure
from src.agents import (
    CodeGeneratorAgent, 
    ReviewerAgent, 
    ImproverAgent, 
    APIIntegrationAgent, 
    DocumentationAgent, 
    PRDAgent, 
    QAAgent
)

# Import configuration
from config.config import API_KEY, google_api_key, OUTPUT_PATH, GITHUB_USERNAME, GITHUB_TOKEN, AZURE_OPENAI_API_KEY

# Import MOP templates
from src.workflows.templates.mop_cisco_audit import MOP_CONTENT
from src.workflows.templates.PRDmop import PRD_MOP_CONTENT

# Import workflow functions
from src.workflows.core.run_autogen_mop import run_autogen_mop

# Import integration functions
from src.integrations.jira.create_jira_issues_from_prd import create_jira_issues_from_prd
from src.integrations.github.github_utils import create_github_repo, push_to_github
from src.agents.core.qa_agent import create_jira_bug




def select_mop_content():
    print("Select MOP input file:")
    print("1. mop_cisco_audit")
    print("2. PRDmop")
    choice = input("Enter 1 or 2: ").strip()
    if choice == "2":
        return PRD_MOP_CONTENT, "PRDmop"
    return MOP_CONTENT, "mop_cisco_audit"


def select_agents():
    agent_options = [
        ("CodeGenerator", "coder"),
        ("Reviewer", "reviewer"),
        ("Improver", "improver"),
        ("APIIntegration", "integrator"),
        ("ReverseEngineering", "Document"),
        ("PRD", "prd_agent"),
        ("QA", "qa_agent")
    ]
    print("Select agents to run (comma separated numbers):")
    for idx, (name, _) in enumerate(agent_options, 1):
        print(f"{idx}. {name}")
    choices = input("Enter choices (e.g., 1,3,5): ").split(",")
    selected = set()
    for c in choices:
        c = c.strip()
        if c.isdigit() and 1 <= int(c) <= len(agent_options):
            selected.add(agent_options[int(c)-1][1])
    return selected



def run_workflow():
    print("🚀 Starting the API integration workflow...")

    mop_content, mop_name = select_mop_content()
    selected_agents = select_agents()

    # GitHub config (from config.py)
    repo_name = mop_name
    repo_url = create_github_repo(repo_name, GITHUB_TOKEN, GITHUB_USERNAME)

    # Step 1: Initialize the Claude client and agents
    claude_client = Anthropic(api_key=API_KEY)

    coder = CodeGeneratorAgent(claude_client=claude_client, name="coder", api_key=API_KEY)
    reviewer = ReviewerAgent(claude_client=claude_client, name="reviewer", api_key=AZURE_OPENAI_API_KEY)
    improver = ImproverAgent(claude_client=claude_client, name="improver", api_key=API_KEY)
    integrator = APIIntegrationAgent(claude_client=claude_client, name="integrator", google_api_key=google_api_key)
    Document = DocumentationAgent(claude_client=claude_client, name="DocumentAgent", api_key=API_KEY)
    prd_agent = PRDAgent(claude_client=claude_client, name="PRDAgent", api_key=API_KEY)
    qa_agent = QAAgent(claude_client=claude_client, name="QAAgent", api_key=API_KEY)

    print(f"✅ Agents initialized! Using MOP: {mop_name}")

    generated_code = None
    improved_code = None
    integration_result = None

    if "coder" in selected_agents:
        print("\n💻 Generating code...")
        generated_code = coder.generate_reply([{"content": mop_content}])
        print(f"Generated Code:\n{generated_code}")
        # Save and push generated code
        output_dir = project_root / "data" / "output"
        output_dir.mkdir(parents=True, exist_ok=True)
        code_file = output_dir / "generated_code.py"
        with open(code_file, "w", encoding="utf-8") as f:
            f.write(generated_code)
        # Ensure file is written before pushing
        if code_file.exists() and code_file.stat().st_size > 0:
            push_to_github(repo_url, "codegen", str(code_file), "Add generated code", GITHUB_TOKEN, GITHUB_USERNAME)
        else:
            print(f"❌ File {code_file} not found or empty, skipping push.")
    if "reviewer" in selected_agents and generated_code:
        print("\n🔍 Reviewing code...")
        review_feedback = reviewer.generate_reply([{"content": f"Review this code:\n\n{generated_code}"}])
        print(f"Review Feedback:\n{review_feedback}")
        # Save and push review feedback
        review_file = output_dir / "review_feedback.txt"
        with open(review_file, "w", encoding="utf-8") as f:
            f.write(review_feedback)
        if review_file.exists() and review_file.stat().st_size > 0:
            push_to_github(repo_url, "review", str(review_file), "Add review feedback", GITHUB_TOKEN, GITHUB_USERNAME)
        else:
            print(f"❌ File {review_file} not found or empty, skipping push.")
    if "improver" in selected_agents and generated_code:
        print("\n✨ Improving code...")
        improved_code = improver.generate_reply([{"content": f"Improve this code:\n\n{generated_code}"}])
        print(f"Improved Code:\n{improved_code}")
        # Save and push improved code
        improved_file = output_dir / "improved_code.py"
        with open(improved_file, "w", encoding="utf-8") as f:
            f.write(improved_code)
        if improved_file.exists() and improved_file.stat().st_size > 0:
            push_to_github(repo_url, "improve", str(improved_file), "Add improved code", GITHUB_TOKEN, GITHUB_USERNAME)
        else:
            print(f"❌ File {improved_file} not found or empty, skipping push.")
    if "integrator" in selected_agents and (improved_code or generated_code):
        print("\n🔗 Performing API Integration...")
        api_task = f"Integrate the following MOP content into a suitable API workflow:\n\n{mop_content}"
        integration_result = integrator.handle_task(api_task)
        print(f"Integration Result:\n{integration_result}")
        # Save and push integration result
        integration_file = output_dir / "integration_result.txt"
        with open(integration_file, "w", encoding="utf-8") as f:
            f.write(str(integration_result))
        push_to_github(repo_url, "integration", str(integration_file), "Add integration result", GITHUB_TOKEN, GITHUB_USERNAME)
    if "Document" in selected_agents and (generated_code or improved_code):
        print("\n✨ Documenting the code...")
        doc_input = improved_code if improved_code else generated_code
        Documented_code = Document.generate_reply([{"content": f"Document this code:\n\n{doc_input}"}])
        print(f"Documented Code:\n{Documented_code}")
        output_file = output_dir / "documented_script.py"
        with open(output_file, "w", encoding="utf-8") as file:
            file.write(Documented_code)
        print(f"\n📂 Documented script saved at: {output_file}")
        push_to_github(repo_url, "docs", str(output_file), "Add documented script", GITHUB_TOKEN, GITHUB_USERNAME)
    if "prd_agent" in selected_agents:
        print("\n📄 Generating PRD...")
        prd_content = PRD_MOP_CONTENT
        prd_output_file = output_dir / "prd.md"
        result = prd_agent.generate_prd(mop_content=prd_content, output_file=str(prd_output_file))
        print(result)
        push_to_github(repo_url, "prd", str(prd_output_file), "Add PRD", GITHUB_TOKEN, GITHUB_USERNAME)
    if "qa_agent" in selected_agents and (generated_code or improved_code):
        code_to_analyze = improved_code if improved_code else generated_code
        print("\n🔍 Performing static code analysis...")
        analyzed_code = qa_agent.analyze_code(code_to_analyze)
        print(f"Static Code Analysis:\n{analyzed_code}")
        # Save and push static code analysis
        static_analysis_file = output_dir / "static_code_analysis.txt"
        with open(static_analysis_file, "w", encoding="utf-8") as f:
            f.write(analyzed_code)
        if static_analysis_file.exists() and static_analysis_file.stat().st_size > 0:
            push_to_github(repo_url, "qa", str(static_analysis_file), "Add static code analysis", GITHUB_TOKEN, GITHUB_USERNAME)
        else:
            print(f"❌ File {static_analysis_file} not found or empty, skipping push.")

        print("\n📝 Generating test cases...")
        test_cases = qa_agent.generate_test_cases(mop_content)
        print(f"Generated Test Cases:\n{test_cases}")
        test_file_path = output_dir / "test_generated.py"
        with open(test_file_path, "w", encoding="utf-8") as f:
            f.write(test_cases)
        print(f"✅ Test cases written to {test_file_path}")
        if test_file_path.exists() and test_file_path.stat().st_size > 0:
            push_to_github(repo_url, "qa", str(test_file_path), "Add generated test cases", GITHUB_TOKEN, GITHUB_USERNAME)
        else:
            print(f"❌ File {test_file_path} not found or empty, skipping push.")

        print("\n🚀 Running generated test cases...")
        try:
            result = subprocess.run(["pytest", str(test_file_path), "-v", "--tb=short"],
                                    capture_output=True, text=True, check=False)
            test_output = result.stdout
        except Exception as e:
            test_output = str(e)
        print("🧪 Test Output:\n", test_output)
        test_results_file = output_dir / "test_results.txt"
        with open(test_results_file, "w", encoding="utf-8") as f:
            f.write(test_output)
        if test_results_file.exists() and test_results_file.stat().st_size > 0:
            push_to_github(repo_url, "qa", str(test_results_file), "Add test results", GITHUB_TOKEN, GITHUB_USERNAME)
        else:
            print(f"❌ File {test_results_file} not found or empty, skipping push.")
        if "FAILED" in test_output or "Error" in test_output:
            print("❌ Test failures detected, creating Jira ticket...")
            create_jira_bug(test_output)
        else:
            print("✅ All tests passed.")

        print("\n✅ Checking compliance...")
        compliance_results = qa_agent.check_compliance(integration_result if integration_result else code_to_analyze)
        print(f"Compliance Results:\n{compliance_results}")
        qa_output_file = output_dir / "qa_results.txt"
        with open(qa_output_file, "w", encoding="utf-8") as file:
            file.write(f"Static Code Analysis:\n{analyzed_code}\n\n")
            file.write(f"Test Cases:\n{test_cases}\n\n")
            file.write(f"Compliance Results:\n{compliance_results}\n\n")
        print(f"\n📂 QA results saved at: {qa_output_file}")
        if qa_output_file.exists() and qa_output_file.stat().st_size > 0:
            push_to_github(repo_url, "qa", str(qa_output_file), "Add QA results", GITHUB_TOKEN, GITHUB_USERNAME)
        else:
            print(f"❌ File {qa_output_file} not found or empty, skipping push.")
    if "prd_agent" in selected_agents:
        print("\n🚀 Creating Jira epics, user stories, and tasks from PRD...")
        create_jira_issues_from_prd()
        print("✅ Jira issues created.")


if __name__ == "__main__":
    run_workflow()


