package com.verizon.vrepair.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;

/**
 * Customer response DTO.
 * Represents customer data returned by API endpoints.
 */
@Schema(description = "Customer information")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerDto {
    
    @Schema(description = "Customer code", example = "RES123456")
    private String customerCode;
    
    @Schema(description = "Billing telephone number", example = "5551234567")
    private String billingTelephoneNumber;
    
    @Schema(description = "Service address", example = "123 Main St, Anytown, ST 12345")
    private String serviceAddress;
    
    @Schema(description = "Customer name", example = "<PERSON>")
    private String customerName;
    
    @Schema(description = "Customer type", example = "RESIDENTIAL")
    private String customerType;
    
    @Schema(description = "Customer status", example = "ACTIVE")
    private String customerStatus;
    
    @Schema(description = "Account number", example = "ACC123456")
    private String accountNumber;
    
    @Schema(description = "Service class code", example = "RES001")
    private String serviceClassCode;
    
    @Schema(description = "Maintenance level", example = "STANDARD")
    private String maintenanceLevel;
    
    @Schema(description = "Contact phone number", example = "**********")
    private String contactPhone;
    
    @Schema(description = "Alternate contact phone", example = "**********")
    private String alternateContact;
    
    @Schema(description = "Email address", example = "<EMAIL>")
    private String emailAddress;
    
    @Schema(description = "Special instructions", example = "Customer prefers morning appointments")
    private String specialInstructions;
    
    @Schema(description = "Creation timestamp", example = "2024-01-15T10:30:00")
    private LocalDateTime createdDate;
    
    @Schema(description = "Last modification timestamp", example = "2024-01-15T14:45:00")
    private LocalDateTime modifiedDate;
    
    @Schema(description = "Created by user", example = "admin")
    private String createdBy;
    
    @Schema(description = "Modified by user", example = "technician")
    private String modifiedBy;
    
    // Default constructor
    public CustomerDto() {}
    
    // Getters and setters
    public String getCustomerCode() { return customerCode; }
    public void setCustomerCode(String customerCode) { this.customerCode = customerCode; }
    
    public String getBillingTelephoneNumber() { return billingTelephoneNumber; }
    public void setBillingTelephoneNumber(String billingTelephoneNumber) { this.billingTelephoneNumber = billingTelephoneNumber; }
    
    public String getServiceAddress() { return serviceAddress; }
    public void setServiceAddress(String serviceAddress) { this.serviceAddress = serviceAddress; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public String getCustomerType() { return customerType; }
    public void setCustomerType(String customerType) { this.customerType = customerType; }
    
    public String getCustomerStatus() { return customerStatus; }
    public void setCustomerStatus(String customerStatus) { this.customerStatus = customerStatus; }
    
    public String getAccountNumber() { return accountNumber; }
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }
    
    public String getServiceClassCode() { return serviceClassCode; }
    public void setServiceClassCode(String serviceClassCode) { this.serviceClassCode = serviceClassCode; }
    
    public String getMaintenanceLevel() { return maintenanceLevel; }
    public void setMaintenanceLevel(String maintenanceLevel) { this.maintenanceLevel = maintenanceLevel; }
    
    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }
    
    public String getAlternateContact() { return alternateContact; }
    public void setAlternateContact(String alternateContact) { this.alternateContact = alternateContact; }
    
    public String getEmailAddress() { return emailAddress; }
    public void setEmailAddress(String emailAddress) { this.emailAddress = emailAddress; }
    
    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }
    
    public LocalDateTime getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate = createdDate; }
    
    public LocalDateTime getModifiedDate() { return modifiedDate; }
    public void setModifiedDate(LocalDateTime modifiedDate) { this.modifiedDate = modifiedDate; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public String getModifiedBy() { return modifiedBy; }
    public void setModifiedBy(String modifiedBy) { this.modifiedBy = modifiedBy; }
}


