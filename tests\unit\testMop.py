from anthropic import Anthropic
import autogen 
from autogen import ConversableAgent
import os


class ClaudeAgent(ConversableAgent):
    """Custom AutoGen agent powered by <PERSON>"""

    def __init__(self, name, claude_client, system_message):
        super().__init__(name=name, human_input_mode="NEVER")
        self.claude_client = claude_client
        self._system_message = system_message

    def generate_reply(   
        self, messages=None, sender=None, **kwargs):
        """Generate reply using <PERSON>"""
        if messages:
            last_message = messages[-1]['content']
            prompt = f"{self._system_message}\n\nTask: {last_message}"
            try:
                response = self.claude_client.messages.create(
                    model="claude-sonnet-4-20250514",
                    max_tokens=2000,
                    messages=[{"role": "user", "content": prompt}]
                )
                return response.content[0].text
            except Exception as e:
                return f"Error: {e}"
        return "No message to respond to."


def run_autogen_mop(api_key, mop_content, output_path):
    """Run AutoGen workflow to create Python script from MOP"""

    print("🚀 Creating AutoGen agents powered by Claude...")

    # Create Claude client
    claude_client = Anthropic(api_key=api_key)

    # Create AutoGen agents using Claude
    coder = ClaudeAgent(
        name="coder",
        claude_client=claude_client,
        system_message="You are a Python expert. Generate a clean Python script based on the provided MOP. Include detailed comments and proper structure."
    )

    reviewer = ClaudeAgent(
        name="reviewer",
        claude_client=claude_client,
        system_message="You are a code reviewer. Review the provided Python script for clarity, functionality, and adherence to best practices. Suggest improvements."
    )

    improver = ClaudeAgent(
        name="improver",
        claude_client=claude_client,
        system_message="You are a code improver. Take the provided Python script and review feedback to create a final improved version with high quality."
    )

    print("✅ AutoGen agents created!")

    # Step 1: Generate initial Python code
    print(f"\n💻 {coder.name.upper()} generating code...")
    code_response = coder.generate_reply([{"content": mop_content}])
    print(f"CODER: {code_response}")

    # Step 2: Review the generated code
    print(f"\n🔍 {reviewer.name.upper()} reviewing code...")
    review_task = f"Review this code and suggest improvements:\n\n{code_response}"
    review_response = reviewer.generate_reply([{"content": review_task}])
    print(f"REVIEWER: {review_response}")

    # Step 3: Improve the code based on feedback
    print(f"\n✨ {improver.name.upper()} improving code...")
    improve_task = f"Improve this code based on feedback:\n\nCODE:\n{code_response}\n\nFEEDBACK:\n{review_response}"
    final_response = improver.generate_reply([{"content": improve_task}])
    print(f"IMPROVER: {final_response}")

    # Step 4: Save the final code to file
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as file:
        file.write(final_response)
    print(f"\n📂 Final Python script saved to: {output_path}")

    return {
        "original_code": code_response,
        "review": review_response,
        "improved_code": final_response
    }


def main():
    # Your Claude API key
    api_key = "************************************************************************************************************"
    if api_key == "your-claude-api-key-here":
        print("❌ Please add your Claude API key!")
        return

    # Method of Procedure (MOP) content
    mop_content = """
    MOP: Cisco XR Device Audit


Objective:

The purpose of this MOP is to outline the steps for conducting an audit on a Cisco XR device to assess its configuration, security, and compliance with best practices and organizational standards.


Prerequisites:

1. Ensure you have the necessary access credentials to log in to the Cisco XR device.

2. Have a secure network connection to the device.

3. Obtain approval from the relevant stakeholders and notify them of the audit schedule.


Step 1: Establish a Secure Connection

1.1. Use a secure protocol like SSH to establish a connection to the Cisco XR device.

1.2. Log in using the provided access credentials.


Step 2: Gather Device Information

2.1. Retrieve the device's hostname, model, and software version using the following commands:

- show hostname

- show version

2.2. Document the retrieved information for reference.


Step 3: Review Running Configuration

3.1. Use the command "show running-config" to display the device's current running configuration.

3.2. Analyze the configuration for any discrepancies, misconfigurations, or deviations from best practices and organizational standards.

3.3. Take note of any issues or areas that require further investigation.


Step 4: Check Interface Configuration

4.1. Use the command "show interfaces" to display information about the device's interfaces.

4.2. Review the interface configurations, including IP addresses, VLANs, and status.

4.3. Verify that the interfaces are properly configured and in the expected state.


Step 5: Assess Security Configuration

5.1. Check the device's access control lists (ACLs) using the command "show access-lists".

5.2. Review the ACLs to ensure they are properly configured and align with security best practices.

5.3. Verify that unnecessary or insecure protocols and services are disabled.


Step 6: Examine Routing Configuration

6.1. Use the command "show ip route" to display the device's routing table.

6.2. Review the routing configuration to ensure proper routing protocols are in place and routes are correctly configured.

6.3. Check for any unusual or unauthorized routes.


Step 7: Analyze System Logs

7.1. Use the command "show logging" to view the device's system logs.

7.2. Examine the logs for any error messages, warnings, or suspicious activities.

7.3. Investigate and document any issues found in the logs.


Step 8: Generate Audit Report

8.1. Compile all the findings and observations from the previous steps into a comprehensive audit report.

8.2. Include recommendations for addressing any identified issues or areas of improvement.

8.3. Distribute the audit report to the relevant stakeholders for review and action.


Step 9: Close the Connection

9.1. Gracefully exit the SSH session using the "exit" command.

9.2. Ensure the connection is properly terminated.


Post-Audit Actions:

1. Schedule a meeting with the relevant stakeholders to discuss the audit findings and recommendations.

2. Develop an action plan to address any identified issues and implement necessary changes.

3. Perform follow-up audits to ensure the implemented changes are effective and the device remains compliant.


Note: The specific commands and steps may vary depending on the Cisco XR software version and device model. Always refer to the official Cisco documentation for the most accurate and up-to-date information.

    Steps:
    1. Log in to the device using SSH.
    2. Execute the command 'show running-config' to capture the configuration.
    3. Analyze the configuration for compliance.
    4. Save the results in a report.
    """

    # Output file path
    output_path = "./generated_scripts/audit_script.py"

    # Run the AutoGen workflow
    results = run_autogen_mop(api_key, mop_content, output_path)

    print("\n" + "=" * 60)
    print("📊 WORKFLOW SUMMARY")
    print("=" * 60)
    print("✅ MOP content processed")
    print(f"✅ Original Code:\n{results['original_code']}")
    print(f"✅ Review Feedback:\n{results['review']}")
    print(f"✅ Improved Code:\n{results['improved_code']}")
    print("✅ Python script saved successfully!")


if __name__ == "__main__":
    main()
