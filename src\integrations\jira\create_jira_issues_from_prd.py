# import re
# import requests
# import base64
# from config import JIRA_API_CONFIG, OUTPUT_PATH

# # --- CONFIGURATION ---
# JIRA_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/issue"  # Use the correct Jira REST API endpoint for issue creation
# JIRA_EMAIL = JIRA_API_CONFIG["email"]
# JIRA_API_TOKEN = JIRA_API_CONFIG["api_key"]
# JIRA_PROJECT_KEY = JIRA_API_CONFIG["project_key"]  # Get project key from config
# PRD_FILE = f"{OUTPUT_PATH}/prd.md"

# # --- AUTH ---
# def get_auth_header():
#     # Ensure credentials are correct and print for debug (do not print in production)
#     token = f"{JIRA_EMAIL}:{JIRA_API_TOKEN}"
#     b64 = base64.b64encode(token.encode()).decode()
#     return {"Authorization": f"Basic {b64}", "Content-Type": "application/json"}

# # --- PARSE PRD ---
# def parse_prd(file_path):
#     with open(file_path, 'r', encoding='utf-8') as f:
#         content = f.read()
#     epics = re.findall(r"### Epic (\d+): (.+?)\n(.*?)(?=###|$)", content, re.DOTALL)
#     user_stories = re.findall(r"\*\*US-(\d+)\*\*: (.+)", content)
#     return epics, user_stories

# # --- JIRA API ---
# def create_epic(summary, description):
#     url = f"{JIRA_URL}"
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": description,
#             "issuetype": {"name": "Epic"},
#             "customfield_10011": summary  # Epic Name field, may need to adjust field ID
#         }
#     }
#     resp = requests.post(url, json=data, headers=get_auth_header())
#     if resp.status_code == 400:
#         print("Bad Request! Check required fields for Epic creation. Response:", resp.text)
#         raise Exception("Jira Epic creation failed.")
#     if resp.status_code == 401:
#         print("Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#         raise Exception("Jira authentication failed.")
#     resp.raise_for_status()
#     return resp.json()["key"]

# def create_story(summary, description, epic_key):
#     url = f"{JIRA_URL}"
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": description,
#             "issuetype": {"name": "Story"},
#             "customfield_10014": epic_key  # This field may differ; check your Jira instance
#         }
#     }
#     resp = requests.post(url, json=data, headers=get_auth_header())
#     if resp.status_code == 401:
#         print("Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#         raise Exception("Jira authentication failed.")
#     resp.raise_for_status()
#     return resp.json()["key"]

# def create_jira_issues_from_prd():
#     epics, user_stories = parse_prd(PRD_FILE)
#     print(f"Found {len(epics)} epics and {len(user_stories)} user stories.")
#     epic_keys = {}
#     for num, name, _ in epics:
#         epic_key = create_epic(name.strip(), f"Epic {num}")
#         epic_keys[num] = epic_key
#         print(f"Created Epic: {name} ({epic_key})")
#     for us_num, us_text in user_stories:
#         # Find which epic this user story belongs to (simple mapping by number)
#         epic_num = str(((int(us_num)-1)//3)+1)  # Adjust as needed
#         epic_key = epic_keys.get(epic_num)
#         story_key = create_story(f"US-{us_num}", us_text, epic_key)
#         print(f"Created Story: US-{us_num} ({story_key}) under Epic {epic_key}")

# # --- MAIN ---
# if __name__ == "__main__":
#     create_jira_issues_from_prd()


# import re
# import requests
# import base64
# import json
# from config import JIRA_API_CONFIG, OUTPUT_PATH

# # --- CONFIGURATION ---
# JIRA_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/issue"
# JIRA_FIELDS_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/field"
# JIRA_EMAIL = JIRA_API_CONFIG["email"]
# JIRA_API_TOKEN = JIRA_API_CONFIG["api_key"]
# JIRA_PROJECT_KEY = JIRA_API_CONFIG["project_key"]
# PRD_FILE = f"{OUTPUT_PATH}/prd.md"

# # --- AUTH ---
# def get_auth_header():
#     token = f"{JIRA_EMAIL}:{JIRA_API_TOKEN}"
#     b64 = base64.b64encode(token.encode()).decode()
#     return {"Authorization": f"Basic {b64}", "Content-Type": "application/json"}

# # --- PARSE PRD ---
# def parse_prd(file_path):
#     with open(file_path, 'r', encoding='utf-8') as f:
#         content = f.read()

#     # Match epics: ### Epic 1: Title
#     epics = re.findall(r"### Epic (\d+): (.+?)\n", content)

#     # Match user stories: **US-001: Title**
#     user_stories = re.findall(
#         r"\*\*US-(\d+): (.+?)\*\*\n(.*?)(?=(\*\*US-\d+:|\Z))",
#         content, re.DOTALL
#     )

#     parsed_stories = []
#     for us_num, title, body, _ in user_stories:
#         description = body.strip()
#         full_text = f"*{title.strip()}*\n\n{description}"
#         parsed_stories.append((us_num, title, full_text))

#     return epics, parsed_stories

# # --- Get Custom Field ID for 'Epic Name' ---
# def get_epic_name_field_id():
#     resp = requests.get(JIRA_FIELDS_URL, headers=get_auth_header())
#     resp.raise_for_status()
#     fields = resp.json()
#     for field in fields:
#         if field.get("name") == "Epic Name":
#             return field["id"]
#     raise Exception("Could not find 'Epic Name' field in Jira.")

# # --- JIRA API ---
# def create_epic(summary, description, epic_name_field_id):
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": description,
#             "issuetype": {"name": "Epic"},
#             epic_name_field_id: summary
#         }
#     }
#     resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
#     if resp.status_code == 400:
#         print("Bad Request! Response:", resp.text)
#     elif resp.status_code == 401:
#         print("Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#     resp.raise_for_status()
#     return resp.json()["key"]

# def create_story(summary, description, epic_key):
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": description,
#             "issuetype": {"name": "Story"},
#             "customfield_10014": epic_key  # Adjust if your field ID is different
#         }
#     }
#     resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
#     if resp.status_code == 401:
#         print("Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#     resp.raise_for_status()
#     return resp.json()["key"]

# # --- MAIN WORKFLOW ---
# def create_jira_issues_from_prd():
#     epics, user_stories = parse_prd(PRD_FILE)
#     print(f"✅ Found {len(epics)} epics and {len(user_stories)} user stories.")

#     if not user_stories:
#         print("⚠️ No user stories found. Check your PRD formatting.")
#         return

#     epic_name_field_id = get_epic_name_field_id()
#     epic_keys = {}

#     for epic_num, epic_title in epics:
#         epic_key = create_epic(epic_title.strip(), f"Epic {epic_num}", epic_name_field_id)
#         epic_keys[epic_num] = epic_key
#         print(f"📌 Created Epic: {epic_title} ({epic_key})")

#     for us_num, title, description in user_stories:
#         epic_num = str(((int(us_num) - 1) // 3) + 1)
#         epic_key = epic_keys.get(epic_num)
#         if not epic_key:
#             print(f"⚠️ No matching epic for US-{us_num}, skipping.")
#             continue
#         story_key = create_story(f"US-{us_num}: {title}", description, epic_key)
#         print(f"✅ Created Story: US-{us_num} ({story_key}) under Epic {epic_key}")

# # --- MAIN ---
# if __name__ == "__main__":
#     create_jira_issues_from_prd()



# import re
# import requests
# import base64
# import json
# from config import JIRA_API_CONFIG, OUTPUT_PATH

# # --- CONFIGURATION ---
# JIRA_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/issue"
# JIRA_FIELDS_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/field"
# JIRA_EMAIL = JIRA_API_CONFIG["email"]
# JIRA_API_TOKEN = JIRA_API_CONFIG["api_key"]
# JIRA_PROJECT_KEY = JIRA_API_CONFIG["project_key"]
# PRD_FILE = f"{OUTPUT_PATH}/prd.md"

# # --- AUTH ---
# def get_auth_header():
#     token = f"{JIRA_EMAIL}:{JIRA_API_TOKEN}"
#     b64 = base64.b64encode(token.encode()).decode()
#     return {"Authorization": f"Basic {b64}", "Content-Type": "application/json"}

# # --- PARSE PRD ---
# def parse_prd(file_path):
#     with open(file_path, 'r', encoding='utf-8') as f:
#         content = f.read()

#     # Match epics: ### Epic 1: Title
#     epics = re.findall(r"### Epic (\d+): (.+?)\n", content)

#     # Match user stories: **US-001: Title**
#     user_stories = re.findall(
#         r"\*\*US-(\d+): (.+?)\*\*\n(.*?)(?=(\*\*US-\d+:|\Z))",
#         content, re.DOTALL
#     )

#     parsed_stories = []
#     for us_num, title, body, _ in user_stories:
#         description = body.strip()
#         full_text = f"*{title.strip()}*\n\n{description}"
#         parsed_stories.append((us_num, title, full_text))

#     return epics, parsed_stories

# # --- Get Custom Field ID for 'Epic Name' ---
# def get_epic_name_field_id():
#     print("🔍 Fetching Jira custom fields...")
#     resp = requests.get(JIRA_FIELDS_URL, headers=get_auth_header())
#     resp.raise_for_status()
#     fields = resp.json()

#     print("\n📋 Available Fields:")
#     for field in fields:
#         print(f"- {field['id']}: {field.get('name')}")

#     for field in fields:
#         if field.get("name") == "Epic Name":
#             print(f"\n✅ Found 'Epic Name' field ID: {field['id']}")
#             return field["id"]

#     raise Exception("\n❌ Could not find 'Epic Name' field in Jira. Please check your Jira instance configuration.")

# # --- JIRA API ---
# def create_epic(summary, description, epic_name_field_id):
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": description,
#             "issuetype": {"name": "Epic"},
#             epic_name_field_id: summary
#         }
#     }
#     resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
#     if resp.status_code == 400:
#         print("❌ Bad Request! Response:", resp.text)
#     elif resp.status_code == 401:
#         print("❌ Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#     resp.raise_for_status()
#     return resp.json()["key"]

# def create_story(summary, description, epic_key):
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": description,
#             "issuetype": {"name": "Story"},
#             "customfield_10014": epic_key  # ⚠️ Change this if your "Epic Link" field ID is different
#         }
#     }
#     resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
#     if resp.status_code == 401:
#         print("❌ Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#     resp.raise_for_status()
#     return resp.json()["key"]

# # --- MAIN WORKFLOW ---
# def create_jira_issues_from_prd():
#     epics, user_stories = parse_prd(PRD_FILE)
#     print(f"✅ Found {len(epics)} epics and {len(user_stories)} user stories.")

#     if not user_stories:
#         print("⚠️ No user stories found. Check your PRD formatting.")
#         return

#     epic_name_field_id = get_epic_name_field_id()
#     epic_keys = {}

#     for epic_num, epic_title in epics:
#         epic_key = create_epic(epic_title.strip(), f"Epic {epic_num}", epic_name_field_id)
#         epic_keys[epic_num] = epic_key
#         print(f"📌 Created Epic: {epic_title} ({epic_key})")

#     for us_num, title, description in user_stories:
#         epic_num = str(((int(us_num) - 1) // 3) + 1)
#         epic_key = epic_keys.get(epic_num)
#         if not epic_key:
#             print(f"⚠️ No matching epic for US-{us_num}, skipping.")
#             continue
#         story_key = create_story(f"US-{us_num}: {title}", description, epic_key)
#         print(f"✅ Created Story: US-{us_num} ({story_key}) under Epic {epic_key}")

# # --- MAIN ---
# if __name__ == "__main__":
#     create_jira_issues_from_prd()

# import re
# import requests
# import base64
# import json
# from config import JIRA_API_CONFIG, OUTPUT_PATH

# # --- CONFIGURATION ---
# JIRA_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/issue"
# JIRA_FIELDS_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/field"
# JIRA_EMAIL = JIRA_API_CONFIG["email"]
# JIRA_API_TOKEN = JIRA_API_CONFIG["api_key"]
# JIRA_PROJECT_KEY = JIRA_API_CONFIG["project_key"]  # Make sure it's "SCRUM"
# PRD_FILE = f"{OUTPUT_PATH}/prd.md"

# # --- AUTH ---
# def get_auth_header():
#     token = f"{JIRA_EMAIL}:{JIRA_API_TOKEN}"
#     b64 = base64.b64encode(token.encode()).decode()
#     return {"Authorization": f"Basic {b64}", "Content-Type": "application/json"}

# # --- PARSE PRD ---
# def parse_prd(file_path):
#     with open(file_path, 'r', encoding='utf-8') as f:
#         content = f.read()

#     epics = re.findall(r"### Epic (\d+): (.+?)\n", content)

#     user_stories = re.findall(
#         r"\*\*US[-_]?(\d{3})\*\*: (.*?)(?=(\*\*US[-_]?(\d{3})\*\*:|\Z))",
#         content,
#         re.DOTALL
#     )

#     parsed_stories = []
#     for us_num, description, _, _ in user_stories:
#         summary = f"US-{us_num}"
#         full_text = description.strip() if description else "No detailed description provided."
#         parsed_stories.append((us_num, summary, full_text))

#     return epics, parsed_stories

# # --- HELPER: Convert text to ADF (Atlassian Document Format) ---
# def to_adf(text):
#     return {
#         "type": "doc",
#         "version": 1,
#         "content": [
#             {
#                 "type": "paragraph",
#                 "content": [
#                     {
#                         "type": "text",
#                         "text": text
#                     }
#                 ]
#             }
#         ]
#     }

# # --- JIRA API ---
# def create_epic(summary, description):
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": to_adf(description),
#             "issuetype": {"name": "Epic"}
#         }
#     }
#     resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
#     if resp.status_code == 400:
#         print("❌ Bad Request! Response:", resp.text)
#     elif resp.status_code == 401:
#         print("❌ Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#     resp.raise_for_status()
#     return resp.json()["key"]

# def create_story(summary, description, epic_key):
#     data = {
#         "fields": {
#             "project": {"key": JIRA_PROJECT_KEY},
#             "summary": summary,
#             "description": to_adf(description),
#             "issuetype": {"name": "Story"},
#             "customfield_10014": epic_key  # Epic Link; update if your field ID differs
#         }
#     }
#     resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
#     if resp.status_code == 400:
#         print("❌ Bad Request! Response:", resp.text)
#     elif resp.status_code == 401:
#         print("❌ Unauthorized! Check your Jira email and API token.")
#         print("Response:", resp.text)
#     resp.raise_for_status()
#     return resp.json()["key"]

# # --- MAIN WORKFLOW ---
# def create_jira_issues_from_prd():
#     print("🚀 Creating Jira epics and user stories from PRD...")
#     epics, user_stories = parse_prd(PRD_FILE)
#     print(f"✅ Found {len(epics)} epics and {len(user_stories)} user stories.")

#     epic_keys = {}

#     for epic_num, epic_title in epics:
#         epic_key = create_epic(epic_title.strip(), f"Epic {epic_num}")
#         epic_keys[epic_num] = epic_key
#         print(f"📌 Created Epic: {epic_title} ({epic_key})")

#     if not user_stories:
#         print("⚠️ No user stories found. Only epics created.")
#         return

#     for us_num, title, description in user_stories:
#         epic_num = str(((int(us_num) - 1) // 3) + 1)  # Example: US-001–003 → Epic 1
#         epic_key = epic_keys.get(epic_num)
#         if not epic_key:
#             print(f"⚠️ No matching epic for US-{us_num}, skipping.")
#             continue
#         story_key = create_story(f"{title}", description, epic_key)
#         print(f"✅ Created Story: US-{us_num} ({story_key}) under Epic {epic_key}")

# # --- MAIN ---
# if __name__ == "__main__":
#     create_jira_issues_from_prd()





import re
import requests
import base64
from datetime import datetime
from config import JIRA_API_CONFIG, OUTPUT_PATH

# --- CONFIGURATION ---
JIRA_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/issue"
JIRA_FIELDS_URL = JIRA_API_CONFIG["base_url"].rstrip("/") + "/rest/api/3/field"
JIRA_EMAIL = JIRA_API_CONFIG["email"]
JIRA_API_TOKEN = JIRA_API_CONFIG["api_key"]
JIRA_PROJECT_KEY = JIRA_API_CONFIG["project_key"]
PRD_FILE = f"{OUTPUT_PATH}/prd.md"

# --- AUTH ---
def get_auth_header():
    token = f"{JIRA_EMAIL}:{JIRA_API_TOKEN}"
    b64 = base64.b64encode(token.encode()).decode()
    return {"Authorization": f"Basic {b64}", "Content-Type": "application/json"}

# --- ENHANCED ADF FORMATTER FOR ACCEPTANCE CRITERIA ---
def to_adf(text):
    """Convert text to Atlassian Document Format with proper formatting for acceptance criteria."""
    content = []

    # Split text into lines for better formatting
    lines = text.split('\n')
    current_paragraph = []

    for line in lines:
        line = line.strip()
        if not line:
            # Empty line - end current paragraph if it exists
            if current_paragraph:
                content.append({
                    "type": "paragraph",
                    "content": [{"type": "text", "text": " ".join(current_paragraph)}]
                })
                current_paragraph = []
        elif line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')):
            # Numbered list item (acceptance criteria)
            if current_paragraph:
                content.append({
                    "type": "paragraph",
                    "content": [{"type": "text", "text": " ".join(current_paragraph)}]
                })
                current_paragraph = []

            content.append({
                "type": "paragraph",
                "content": [
                    {"type": "text", "text": line, "marks": [{"type": "strong"}]}
                ]
            })
        elif line.startswith(('User Story:', 'Acceptance Criteria:')):
            # Section headers
            if current_paragraph:
                content.append({
                    "type": "paragraph",
                    "content": [{"type": "text", "text": " ".join(current_paragraph)}]
                })
                current_paragraph = []

            content.append({
                "type": "paragraph",
                "content": [
                    {"type": "text", "text": line, "marks": [{"type": "strong"}]}
                ]
            })
        else:
            # Regular text
            current_paragraph.append(line)

    # Add any remaining paragraph
    if current_paragraph:
        content.append({
            "type": "paragraph",
            "content": [{"type": "text", "text": " ".join(current_paragraph)}]
        })

    # If no content was created, add the original text as a single paragraph
    if not content:
        content = [{
            "type": "paragraph",
            "content": [{"type": "text", "text": text}]
        }]

    return {
        "type": "doc",
        "version": 1,
        "content": content
    }

# --- FETCH EPIC LINK FIELD ID (if available) ---
def get_epic_link_field_id():
    response = requests.get(JIRA_FIELDS_URL, headers=get_auth_header())
    response.raise_for_status()
    for field in response.json():
        if "epic link" in field["name"].lower():
            print(f"✅ Epic Link Field ID: {field['id']}")
            return field["id"]
    print("⚠️ 'Epic Link' field not found. Will try using 'parent' instead.")
    return None

# --- PARSE PRD WITH ACCEPTANCE CRITERIA ---
def parse_prd(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Match epics like: ### Epic 1: Title or ### **Epic 1: Title**
    epics = re.findall(r"### \*?\*?Epic (\d+): (.+?)\*?\*?\n", content)

    # Parse epic sections to extract user stories and their acceptance criteria
    parsed_stories = []

    # Split content by epic sections (handle both ### Epic and ### **Epic formats)
    epic_sections = re.split(r"### \*?\*?Epic \d+:", content)

    for i, section in enumerate(epic_sections[1:], 1):  # Skip first empty section
        epic_num = str(i)

        # Find user stories in this epic section (handle multiple formats)
        # Format 1: - **US-XXX**: Description
        user_story_pattern1 = re.compile(r"- \*\*(US-(\d+))\*\*: (.+?)(?=\n- \*\*US-|\n\*\*Acceptance Criteria|\Z)", re.DOTALL)
        stories_format1 = user_story_pattern1.findall(section)

        # Format 2: **US-XXX: Title** (your PRD format)
        user_story_pattern2 = re.compile(r"\*\*(US-(\d+): ([^*]+))\*\*", re.DOTALL)
        stories_format2 = user_story_pattern2.findall(section)

        # Combine both formats
        stories_in_epic = stories_format1 + [(full_match, us_num, title) for full_match, us_num, title in stories_format2]

        # For your PRD format, extract the complete user story content for each US-XXX
        for full_id, us_num, title_part in stories_in_epic:
            # Extract the complete user story content from the section
            us_pattern = re.compile(rf"\*\*{re.escape(full_id)}\*\*(.*?)(?=\*\*US-\d+:|###|\Z)", re.DOTALL)
            us_match = us_pattern.search(section)

            if us_match:
                full_story_content = us_match.group(1).strip()

                # Extract the "I want" part for the title
                want_match = re.search(r"- \*\*I want\*\* (.+?)(?=\n|$)", full_story_content, re.IGNORECASE)
                if want_match:
                    action = want_match.group(1).strip()
                    # Clean up the action text
                    action = action.replace("to ", "").replace("the ", "")
                    title = action[0].upper() + action[1:] if action else title_part
                    if len(title) > 80:
                        title = title[:77] + "..."
                else:
                    # Use the title part from US-XXX: Title
                    title = title_part.strip()

                # Build the complete user story description
                as_a_match = re.search(r"- \*\*As a\*\* (.+?)(?=\n|$)", full_story_content)
                want_match = re.search(r"- \*\*I want\*\* (.+?)(?=\n|$)", full_story_content)
                so_that_match = re.search(r"- \*\*So that\*\* (.+?)(?=\n|$)", full_story_content)

                if as_a_match and want_match and so_that_match:
                    role = as_a_match.group(1).strip()
                    want = want_match.group(1).strip()
                    benefit = so_that_match.group(1).strip()

                    user_story = f"As a {role}, I want {want} so that {benefit}"
                else:
                    # Fallback to the full content
                    user_story = full_story_content.replace("- **As a**", "As a").replace("- **I want**", "I want").replace("- **So that**", "so that")

                # Create summary with just the meaningful title
                summary = title

                # Build description with full user story
                description = f"**{full_id}**: {user_story}"

                parsed_stories.append((us_num, summary, description))
            else:
                # Fallback for stories that don't match the expected format
                summary = title_part.strip()
                description = f"**{full_id}**: {title_part}"
                parsed_stories.append((us_num, summary, description))

    # Fallback: If no stories found in epic sections, try original parsing with meaningful titles
    if not parsed_stories:
        # Style 1: Multiline story blocks with title
        pattern_multiline = re.compile(
            r"\*\*(US[-_]?(\d{3}): (.*?)?)\*\*\n(.*?)(?=(\*\*US[-_]?(\d{3})\*\*:)|###|\Z)",
            re.DOTALL
        )
        for full_id, us_num, title_part, body, *_ in pattern_multiline.findall(content):
            # Extract title from the story text
            story_text = body.strip() if body else title_part

            # Extract meaningful title
            title_match = re.search(r"I want to (.+?) so that", story_text, re.IGNORECASE)
            if title_match:
                action = title_match.group(1).strip().replace("to ", "").replace("the ", "")
                title = action[0].upper() + action[1:] if action else story_text[:50]
                if len(title) > 80:
                    title = title[:77] + "..."
            else:
                title = story_text[:50] + "..." if len(story_text) > 50 else story_text

            summary = title  # Just the meaningful title, no US-XXX prefix
            description = f"**{full_id}**: {story_text}"
            parsed_stories.append((us_num, summary, description))

        # Style 2: Single-line stories like **US-001**: As a...
        pattern_inline = re.compile(
            r"\*\*(US[-_]?(\d{3}))\*\*: (.*?)\n", re.DOTALL
        )
        for full_id, us_num, story_text in pattern_inline.findall(content):
            if not any(s[0] == us_num for s in parsed_stories):
                # Extract meaningful title
                title_match = re.search(r"I want to (.+?) so that", story_text, re.IGNORECASE)
                if title_match:
                    action = title_match.group(1).strip().replace("to ", "").replace("the ", "")
                    title = action[0].upper() + action[1:] if action else story_text[:50]
                    if len(title) > 80:
                        title = title[:77] + "..."
                else:
                    title = story_text[:50] + "..." if len(story_text) > 50 else story_text

                summary = title  # Just the meaningful title, no US-XXX prefix
                description = f"**{full_id}**: {story_text.strip()}"
                parsed_stories.append((us_num, summary, description))

    return epics, parsed_stories

# --- JIRA API ---
def create_epic(summary, description):
    data = {
        "fields": {
            "project": {"key": JIRA_PROJECT_KEY},
            "summary": summary,
            "description": to_adf(description),
            "issuetype": {"name": "Epic"}
        }
    }
    resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
    if resp.status_code != 201:
        print("❌ Epic Creation Failed:", resp.text)
    resp.raise_for_status()
    return resp.json()["key"]

def create_story(summary, description, epic_key, epic_link_field_id):
    fields = {
        "project": {"key": JIRA_PROJECT_KEY},
        "summary": summary,
        "description": to_adf(description),
        "issuetype": {"name": "Story"}
    }

    if epic_link_field_id:
        fields[epic_link_field_id] = epic_key
    else:
        fields["parent"] = {"key": epic_key}  # fallback for team-managed projects

    data = {"fields": fields}
    resp = requests.post(JIRA_URL, json=data, headers=get_auth_header())
    if resp.status_code != 201:
        print("❌ Story Creation Failed:", resp.text)
    resp.raise_for_status()
    return resp.json()["key"]

# --- SUMMARY EPIC CREATION ---
def create_summary_epic(epics, user_stories, prd_file_path):
    """Create a summary epic that provides an overview of the entire PRD."""
    try:
        # Read the PRD file to extract overview information
        with open(prd_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract product overview
        overview_match = re.search(r"## Product Overview\n(.*?)(?=\n##|\Z)", content, re.DOTALL)
        product_overview = overview_match.group(1).strip() if overview_match else "Product overview not found"

        # Extract business objectives
        objectives_match = re.search(r"## Business Objectives\n(.*?)(?=\n##|\Z)", content, re.DOTALL)
        business_objectives = objectives_match.group(1).strip() if objectives_match else "Business objectives not found"

        # Create summary description with ADF formatting
        summary_description = f"""📋 PRD SUMMARY DASHBOARD

🎯 Product Overview:
{product_overview[:500]}{'...' if len(product_overview) > 500 else ''}

🎯 Business Objectives:
{business_objectives[:500]}{'...' if len(business_objectives) > 500 else ''}

📊 Project Statistics:
• Total Epics: {len(epics)}
• Total User Stories: {len(user_stories)}

📌 Epic Breakdown:"""

        # Add epic breakdown
        for i, (epic_num, epic_title) in enumerate(epics, 1):
            # Count stories for this epic (rough estimation based on numbering)
            stories_in_epic = len([s for s in user_stories if int(s[0]) in range((i-1)*3+1, i*3+4)])
            summary_description += f"\n• Epic {epic_num}: {epic_title} ({stories_in_epic} stories)"

        summary_description += f"""

🔗 Quick Navigation:
This epic serves as a dashboard for the entire PRD implementation. Use this to track overall progress and navigate to specific epics and user stories.

📅 Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📄 Source: {prd_file_path}

💡 Tip: Pin this epic to your Jira board for easy access to project overview."""

        # Create the summary epic
        summary_title = "📊 PRD Summary Dashboard"
        summary_epic_key = create_epic(summary_title, summary_description)

        return summary_epic_key

    except Exception as e:
        print(f"⚠️ Failed to create summary epic: {str(e)}")
        return None

# --- MAIN WORKFLOW ---
def create_jira_issues_from_prd():
    print("🚀 Creating Jira epics and user stories from PRD...")
    epics, user_stories = parse_prd(PRD_FILE)
    print(f"✅ Found {len(epics)} epics and {len(user_stories)} user stories.")

    # Create summary epic first
    print("📊 Creating summary dashboard epic...")
    summary_epic_key = create_summary_epic(epics, user_stories, PRD_FILE)
    if summary_epic_key:
        print(f"✅ Created Summary Dashboard: 📊 PRD Summary Dashboard ({summary_epic_key})")
    else:
        print("⚠️ Summary dashboard creation failed, continuing with regular epics...")

    epic_link_field_id = get_epic_link_field_id()
    epic_keys = {}

    print("📌 Creating functional epics...")
    for epic_num, epic_title in epics:
        epic_key = create_epic(epic_title.strip(), f"Epic {epic_num}")
        epic_keys[epic_num] = epic_key
        print(f"✅ Created Epic: {epic_title} ({epic_key})")

    if not user_stories:
        print("⚠️ No user stories found. Only epics created.")
        return

    print("📝 Creating user stories...")
    for us_num, summary, description in user_stories:
        epic_num = str(((int(us_num) - 1) // 3) + 1)
        epic_key = epic_keys.get(epic_num)
        if not epic_key:
            print(f"⚠️ No matching epic for US-{us_num}, skipping.")
            continue
        story_key = create_story(summary, description, epic_key, epic_link_field_id)
        print(f"✅ Created Story: {summary} ({story_key}) under Epic {epic_key}")

    # Final summary
    print(f"\n🎉 Jira Integration Complete!")
    print(f"📊 Created: 1 summary dashboard + {len(epics)} epics + {len(user_stories)} user stories")
    if summary_epic_key:
        print(f"🔍 View project dashboard: {summary_epic_key}")
        print(f"💡 Pin the summary epic to your board for easy project overview!")

# --- TEST PARSING FUNCTION ---
def test_prd_parsing(file_path=None):
    """Test function to validate PRD parsing and acceptance criteria extraction."""
    if not file_path:
        file_path = PRD_FILE

    print(f"🔍 Testing PRD parsing for: {file_path}")

    try:
        epics, user_stories = parse_prd(file_path)

        print(f"✅ Found {len(epics)} epics:")
        for epic_num, epic_title in epics:
            print(f"  📌 Epic {epic_num}: {epic_title}")

        print(f"\n✅ Found {len(user_stories)} user stories:")
        for us_num, summary, description in user_stories:
            print(f"  📝 {summary}")
            if "Acceptance Criteria:" in description:
                criteria_count = description.count('\n') - description.count('User Story:')
                print(f"    ✅ Includes {criteria_count} acceptance criteria")
            else:
                print(f"    ⚠️ No acceptance criteria found")
            print(f"    Description preview: {description[:100]}...")
            print()

        return True

    except Exception as e:
        print(f"❌ Error parsing PRD: {str(e)}")
        return False

# --- MAIN ---
if __name__ == "__main__":
    # Uncomment the line below to test parsing before creating Jira issues
    # test_prd_parsing()
    create_jira_issues_from_prd()


