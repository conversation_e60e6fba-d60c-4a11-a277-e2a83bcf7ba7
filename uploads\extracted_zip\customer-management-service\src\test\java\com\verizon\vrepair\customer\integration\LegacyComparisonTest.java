package com.verizon.vrepair.customer.integration;

import com.verizon.vrepair.customer.dto.CustomerDto;
import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import com.verizon.vrepair.customer.service.CustomerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Legacy system comparison tests to validate functional parity.
 * Compares Java microservice behavior with legacy C++ system expectations.
 * 
 * These tests validate the "as-is" migration requirements by ensuring:
 * 1. Same business logic behavior
 * 2. Identical data transformations
 * 3. Equivalent search results
 * 4. Same validation rules
 */
@SpringBootTest
@ActiveProfiles("integration-test")
class LegacyComparisonTest {
    
    @Autowired
    private CustomerService customerService;
    
    /**
     * Test customer code generation matches legacy C++ algorithm.
     * Legacy pattern: [TYPE][SEQUENCE] where TYPE = RES/BUS/GOV/WHO
     */
    @Test
    void customerCodeGeneration_MatchesLegacyPattern() {
        // Given - Different customer types as in legacy system
        Customer residentialCustomer = createTestCustomer(CustomerType.RESIDENTIAL);
        Customer businessCustomer = createTestCustomer(CustomerType.BUSINESS);
        Customer governmentCustomer = createTestCustomer(CustomerType.GOVERNMENT);
        Customer wholesaleCustomer = createTestCustomer(CustomerType.WHOLESALE);
        
        // When - Create customers (triggers code generation)
        Customer createdRes = customerService.createCustomer(residentialCustomer);
        Customer createdBus = customerService.createCustomer(businessCustomer);
        Customer createdGov = customerService.createCustomer(governmentCustomer);
        Customer createdWho = customerService.createCustomer(wholesaleCustomer);
        
        // Then - Validate legacy code patterns
        assertThat(createdRes.getCustomerCode()).matches("RES\\d{6}"); // RES + 6 digits
        assertThat(createdBus.getCustomerCode()).matches("BUS\\d{6}"); // BUS + 6 digits
        assertThat(createdGov.getCustomerCode()).matches("GOV\\d{6}"); // GOV + 6 digits
        assertThat(createdWho.getCustomerCode()).matches("WHO\\d{6}"); // WHO + 6 digits
        
        // Validate uniqueness (legacy requirement)
        assertThat(createdRes.getCustomerCode()).isNotEqualTo(createdBus.getCustomerCode());
    }
    
    /**
     * Test phone number validation matches legacy C++ rules.
     * Legacy rules: Exactly 10 digits, no special characters
     */
    @Test
    void phoneNumberValidation_MatchesLegacyRules() {
        // Valid phone numbers (legacy format)
        String[] validPhones = {
            "**********",
            "8001234567", 
            "9995551234"
        };
        
        // Invalid phone numbers (should fail like legacy)
        String[] invalidPhones = {
            "************",  // Hyphens not allowed
            "(555)1234567",  // Parentheses not allowed
            "555123456",     // Too short
            "**********8",   // Too long
            "555123456a",    // Contains letter
            "",              // Empty
            null             // Null
        };
        
        // Test valid phones
        for (String phone : validPhones) {
            Customer customer = createTestCustomerWithPhone(phone);
            
            // Should not throw exception (legacy behavior)
            assertThat(() -> customerService.createCustomer(customer))
                    .doesNotThrowAnyException();
        }
        
        // Test invalid phones
        for (String phone : invalidPhones) {
            Customer customer = createTestCustomerWithPhone(phone);
            
            // Should throw validation exception (legacy behavior)
            assertThat(() -> customerService.createCustomer(customer))
                    .isInstanceOf(Exception.class);
        }
    }
    
    /**
     * Test customer search functionality matches legacy C++ behavior.
     * Legacy search: Case-insensitive, partial matching, specific ordering
     */
    @Test
    void customerSearch_MatchesLegacyBehavior() {
        // Given - Create test customers with specific names (legacy test data)
        createLegacyTestCustomers();
        
        // Test case-insensitive search (legacy behavior)
        List<Customer> results1 = customerService.searchCustomers("smith", null, null, null, null, 0, 10);
        List<Customer> results2 = customerService.searchCustomers("SMITH", null, null, null, null, 0, 10);
        List<Customer> results3 = customerService.searchCustomers("Smith", null, null, null, null, 0, 10);
        
        // All should return same results (case-insensitive)
        assertThat(results1).hasSameSizeAs(results2);
        assertThat(results1).hasSameSizeAs(results3);
        assertThat(results1.size()).isGreaterThan(0);
        
        // Test partial matching (legacy behavior)
        List<Customer> partialResults = customerService.searchCustomers("Joh", null, null, null, null, 0, 10);
        assertThat(partialResults.size()).isGreaterThanOrEqualTo(2); // Should find "John Smith" and "Johnson Corp"
        
        // Test ordering (legacy: alphabetical by customer name)
        List<Customer> orderedResults = customerService.searchCustomers(null, null, null, null, null, 0, 100);
        assertThat(orderedResults).isSortedAccordingTo((c1, c2) -> 
                c1.getCustomerName().compareToIgnoreCase(c2.getCustomerName()));
    }
    
    /**
     * Test address validation matches legacy C++ rules.
     * Legacy rules: Required, max 100 characters, basic format validation
     */
    @Test
    void addressValidation_MatchesLegacyRules() {
        // Valid addresses (legacy format)
        String[] validAddresses = {
            "123 Main St, Anytown, ST 12345",
            "456 Oak Avenue, Big City, CA 90210",
            "789 Pine Road, Small Town, TX 75001"
        };
        
        // Invalid addresses (should fail like legacy)
        String[] invalidAddresses = {
            "",  // Empty
            null, // Null
            "A".repeat(101), // Too long (>100 chars)
            "   ", // Only whitespace
        };
        
        // Test valid addresses
        for (String address : validAddresses) {
            Customer customer = createTestCustomerWithAddress(address);
            assertThat(() -> customerService.createCustomer(customer))
                    .doesNotThrowAnyException();
        }
        
        // Test invalid addresses
        for (String address : invalidAddresses) {
            Customer customer = createTestCustomerWithAddress(address);
            assertThat(() -> customerService.createCustomer(customer))
                    .isInstanceOf(Exception.class);
        }
    }
    
    /**
     * Test customer status transitions match legacy C++ state machine.
     * Legacy states: ACTIVE -> INACTIVE -> SUSPENDED -> TERMINATED
     */
    @Test
    void customerStatusTransitions_MatchLegacyStateMachine() {
        // Given - Active customer
        Customer customer = createTestCustomer(CustomerType.RESIDENTIAL);
        customer.setCustomerStatus(CustomerStatus.ACTIVE);
        Customer created = customerService.createCustomer(customer);
        
        // Test ACTIVE -> INACTIVE (allowed in legacy)
        Customer deactivated = customerService.deactivateCustomer(created.getCustomerCode());
        assertThat(deactivated.getCustomerStatus()).isEqualTo(CustomerStatus.INACTIVE);
        
        // Test INACTIVE -> ACTIVE (allowed in legacy)
        Customer reactivated = customerService.activateCustomer(created.getCustomerCode());
        assertThat(reactivated.getCustomerStatus()).isEqualTo(CustomerStatus.ACTIVE);
        
        // Test ACTIVE -> SUSPENDED (would be allowed in legacy with proper authorization)
        // This would require additional business logic implementation
        
        // Test invalid transitions (should fail like legacy)
        // TERMINATED -> ACTIVE should not be allowed
        customer.setCustomerStatus(CustomerStatus.TERMINATED);
        customerService.updateCustomer(customer.getCustomerCode(), customer);
        
        assertThat(() -> customerService.activateCustomer(customer.getCustomerCode()))
                .isInstanceOf(Exception.class);
    }
    
    /**
     * Test business customer validation matches legacy C++ rules.
     * Legacy rule: Business customers must have account number
     */
    @Test
    void businessCustomerValidation_MatchesLegacyRules() {
        // Business customer without account number (should fail)
        Customer businessCustomer = createTestCustomer(CustomerType.BUSINESS);
        businessCustomer.setAccountNumber(null);
        
        assertThat(() -> customerService.createCustomer(businessCustomer))
                .isInstanceOf(Exception.class);
        
        // Business customer with account number (should succeed)
        businessCustomer.setAccountNumber("BUSACC001");
        assertThat(() -> customerService.createCustomer(businessCustomer))
                .doesNotThrowAnyException();
        
        // Residential customer without account number (should succeed - legacy allows this)
        Customer residentialCustomer = createTestCustomer(CustomerType.RESIDENTIAL);
        residentialCustomer.setAccountNumber(null);
        
        assertThat(() -> customerService.createCustomer(residentialCustomer))
                .doesNotThrowAnyException();
    }
    
    /**
     * Test data format consistency with legacy C++ system.
     * Ensures dates, strings, and numbers are formatted identically.
     */
    @Test
    void dataFormatConsistency_MatchesLegacyFormat() {
        // Create customer and verify format consistency
        Customer customer = createTestCustomer(CustomerType.RESIDENTIAL);
        Customer created = customerService.createCustomer(customer);
        
        // Customer code format (legacy: TYPE + 6-digit sequence)
        assertThat(created.getCustomerCode()).matches("[A-Z]{3}\\d{6}");
        
        // Phone number format (legacy: exactly 10 digits, no formatting)
        assertThat(created.getBillingTelephoneNumber()).matches("\\d{10}");
        
        // Date formats should be consistent with legacy expectations
        assertThat(created.getCreatedDate()).isNotNull();
        assertThat(created.getModifiedDate()).isNotNull();
        
        // Status values should match legacy enumeration
        assertThat(created.getCustomerStatus().toString())
                .isIn("ACTIVE", "INACTIVE", "PENDING", "SUSPENDED", "TERMINATED");
        
        // Type values should match legacy enumeration
        assertThat(created.getCustomerType().toString())
                .isIn("RESIDENTIAL", "BUSINESS", "GOVERNMENT", "WHOLESALE");
    }
    
    // Helper methods
    
    private Customer createTestCustomer(CustomerType type) {
        Customer customer = new Customer();
        customer.setBillingTelephoneNumber("**********");
        customer.setServiceAddress("123 Test St, Test City, TC 12345");
        customer.setCustomerName("Test Customer");
        customer.setCustomerType(type);
        customer.setAccountNumber("TESTACC001");
        customer.setServiceClassCode("STD001");
        customer.setMaintenanceLevel("STANDARD");
        return customer;
    }
    
    private Customer createTestCustomerWithPhone(String phoneNumber) {
        Customer customer = createTestCustomer(CustomerType.RESIDENTIAL);
        customer.setBillingTelephoneNumber(phoneNumber);
        return customer;
    }
    
    private Customer createTestCustomerWithAddress(String address) {
        Customer customer = createTestCustomer(CustomerType.RESIDENTIAL);
        customer.setServiceAddress(address);
        return customer;
    }
    
    private void createLegacyTestCustomers() {
        // Create test customers matching legacy test data
        String[] customerNames = {
            "John Smith",
            "Jane Johnson", 
            "Johnson Corporation",
            "Smith & Associates",
            "ABC Company",
            "XYZ Industries"
        };
        
        for (int i = 0; i < customerNames.length; i++) {
            Customer customer = new Customer();
            customer.setBillingTelephoneNumber("555100" + String.format("%04d", i));
            customer.setServiceAddress("Address " + (i + 1));
            customer.setCustomerName(customerNames[i]);
            customer.setCustomerType(i % 2 == 0 ? CustomerType.RESIDENTIAL : CustomerType.BUSINESS);
            customer.setAccountNumber("LEGACY" + String.format("%03d", i));
            customer.setServiceClassCode("LEG001");
            customer.setMaintenanceLevel("STANDARD");
            
            customerService.createCustomer(customer);
        }
    }
}
