#!/usr/bin/env python3
"""
Test script to verify acceptance criteria extraction from PRD files.
This script tests the enhanced Jira integration that includes acceptance criteria
in user story descriptions.
"""

import os
import sys

def test_acceptance_criteria_parsing():
    """Test the acceptance criteria parsing functionality."""
    
    print("🧪 Testing Acceptance Criteria Parsing for Jira Integration")
    print("=" * 60)
    
    # Create a sample PRD content for testing
    sample_prd = """
# Product Requirements Document (PRD)

## Product Overview
This is a sample PRD for testing acceptance criteria extraction.

## Functional Requirements

### Epic 1: User Authentication
User authentication and authorization system

**User Stories:**
- **US-001**: As a user, I want to log in with email and password so that I can access my account
- **US-002**: As a user, I want to reset my password so that I can regain access if I forget it
- **US-003**: As a user, I want to log out so that I can secure my account

**Acceptance Criteria:**
- User can enter valid email and password to log in
- System validates credentials against database
- Invalid credentials show appropriate error message
- Password reset sends email with secure link
- User session expires after 30 minutes of inactivity
- <PERSON><PERSON><PERSON> clears all session data

### Epic 2: User Profile Management
User profile creation and management features

**User Stories:**
- **US-004**: As a user, I want to create my profile so that I can personalize my experience
- **US-005**: As a user, I want to edit my profile so that I can keep my information current

**Acceptance Criteria:**
- Profile form includes name, email, phone, and bio fields
- All fields except bio are required
- Email must be unique in the system
- Profile changes are saved immediately
- User receives confirmation of successful updates

### Epic 3: Dashboard Features
Main dashboard with key functionality

**User Stories:**
- **US-006**: As a user, I want to see my dashboard so that I can view my activity summary
- **US-007**: As a user, I want to customize my dashboard so that I can see relevant information

**Acceptance Criteria:**
- Dashboard loads within 3 seconds
- Shows last 10 activities by default
- Widgets can be rearranged by drag and drop
- Settings are saved per user
- Mobile responsive design
"""
    
    # Save sample PRD to test file
    test_file = "./output/test_prd.md"
    os.makedirs("./output", exist_ok=True)
    
    with open(test_file, "w", encoding="utf-8") as f:
        f.write(sample_prd)
    
    print(f"📝 Created test PRD file: {test_file}")
    
    # Test the parsing
    try:
        from generated_scripts.create_jira_issues_from_prd import test_prd_parsing
        
        print("\n🔍 Testing PRD parsing...")
        success = test_prd_parsing(test_file)
        
        if success:
            print("\n✅ Acceptance criteria parsing test PASSED!")
            print("\n📋 Summary of what will be created in Jira:")
            print("- 3 Epics with descriptive titles")
            print("- 7 User Stories with acceptance criteria in descriptions")
            print("- Each user story will include:")
            print("  • Original user story text")
            print("  • Numbered acceptance criteria list")
            print("  • Proper formatting for Jira")
            
        else:
            print("\n❌ Acceptance criteria parsing test FAILED!")
            
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        print("Make sure the Jira integration module is available")
        
    except Exception as e:
        print(f"\n❌ Test error: {e}")
    
    # Clean up test file
    try:
        os.remove(test_file)
        print(f"\n🗑️ Cleaned up test file: {test_file}")
    except:
        pass

def show_feature_info():
    """Show information about the new acceptance criteria feature."""
    
    print("\n📚 Acceptance Criteria Integration Feature")
    print("=" * 50)
    print("""
🎯 FEATURE OVERVIEW:
The Jira integration now automatically extracts acceptance criteria from PRD files
and includes them in user story descriptions when creating Jira issues.

📋 HOW IT WORKS:
1. PRD Agent generates structured PRD with epics and acceptance criteria
2. Jira integration parses the PRD file to extract:
   - Epic titles and descriptions
   - User stories with IDs (US-001, US-002, etc.)
   - Acceptance criteria for each epic
3. User stories are created in Jira with descriptions containing:
   - Original user story text
   - Numbered acceptance criteria list
   - Proper formatting for readability

📄 REQUIRED PRD FORMAT:
### Epic X: Epic Title
**User Stories:**
- **US-XXX**: User story description
- **US-XXX**: User story description

**Acceptance Criteria:**
- Criterion 1
- Criterion 2
- Criterion 3

🎫 JIRA RESULT:
Each user story in Jira will have a description like:

User Story: As a user, I want to log in so that I can access my account

Acceptance Criteria:
1. User can enter valid email and password to log in
2. System validates credentials against database
3. Invalid credentials show appropriate error message

✅ BENEFITS:
- Complete traceability from PRD to Jira
- Acceptance criteria visible to development team
- Consistent formatting across all user stories
- Automated process reduces manual work
""")

if __name__ == "__main__":
    test_acceptance_criteria_parsing()
    show_feature_info()
