spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 10000
      idle-timeout: 300000
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
        show_sql: false
    database-platform: org.hibernate.dialect.H2Dialect
  
  redis:
    host: localhost
    port: 6379
    database: 1
    timeout: 2000ms
  
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://localhost:8080/.well-known/jwks.json

# Test-specific configuration
app:
  encryption:
    key: dGVzdC1lbmNyeXB0aW9uLWtleS0xMjM0NTY3ODkwMTIzNDU2
  
  security:
    cors:
      allowed-origins: http://localhost:3000
  
  external-services:
    etms:
      base-url: http://localhost:8081
      timeout: 1000
      retry-attempts: 1
    
    baais:
      base-url: http://localhost:8082
      timeout: 1000
      retry-attempts: 1
    
    vi:
      base-url: http://localhost:8083
      timeout: 1000
      retry-attempts: 1

# Disable management endpoints in tests
management:
  endpoints:
    enabled-by-default: false

# Test logging
logging:
  level:
    com.verizon.vrepair.customer: DEBUG
    org.springframework.test: INFO
    org.testcontainers: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

