import streamlit as st
import os
import traceback

# Configure Streamlit page
st.set_page_config(
    page_title="MOP Agent Workflow UI - Debug Version",
    page_icon="🤖",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state variables."""
    if 'workflow_results' not in st.session_state:
        st.session_state.workflow_results = {}
    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'selected_agents' not in st.session_state:
        st.session_state.selected_agents = set()
    if 'mop_content' not in st.session_state:
        st.session_state.mop_content = ""

def test_imports():
    """Test if all required modules can be imported."""
    try:
        from config import API_KEY, OUTPUT_PATH
        from anthropic import Anthropic
        from local_agents import CodeGeneratorAgent
        return True, "All imports successful"
    except Exception as e:
        return False, f"Import error: {str(e)}"

def simple_code_generation(mop_content):
    """Simple code generation test."""
    try:
        # Import required modules
        from config import API_KEY
        from anthropic import Anthropic
        from local_agents import CodeGeneratorAgent

        # Initialize Claude client with minimal configuration
        try:
            claude_client = Anthropic(api_key=API_KEY)
        except TypeError as te:
            # Try alternative initialization if there's a version issue
            claude_client = Anthropic(
                api_key=API_KEY,
                max_retries=3,
                timeout=60.0
            )

        # Initialize code generator
        coder = CodeGeneratorAgent(claude_client=claude_client, name="coder", api_key=API_KEY)

        # Generate code
        generated_code = coder.generate_reply([{"content": mop_content}])

        return True, generated_code

    except Exception as e:
        return False, f"Error: {str(e)}\n\nFull traceback:\n{traceback.format_exc()}"

def main():
    """Main application function."""
    initialize_session_state()
    
    st.title("🤖 MOP Agent Workflow UI - Debug Version")
    st.markdown("---")
    
    # Test imports first
    st.header("🔧 System Check")
    import_success, import_message = test_imports()
    
    if import_success:
        st.success(f"✅ {import_message}")
    else:
        st.error(f"❌ {import_message}")
        st.stop()
    
    # MOP Content Input
    st.header("📝 MOP Content")
    
    # Simple test MOP
    test_mop = """
MOP: Simple Python Script Test

Objective: Create a basic Python function that adds two numbers.

Steps:
1. Define a function called add_numbers
2. The function should take two parameters
3. Return the sum of the two numbers
4. Add proper documentation
"""
    
    mop_content = st.text_area(
        "Enter MOP content:",
        value=test_mop,
        height=200
    )
    
    if mop_content:
        st.session_state.mop_content = mop_content
        st.success("✅ MOP content loaded")
    
    # Agent Selection (simplified)
    st.header("🤖 Agent Selection")
    
    use_code_generator = st.checkbox("💻 Code Generator", value=True)
    
    if use_code_generator:
        st.session_state.selected_agents = {'coder'}
    else:
        st.session_state.selected_agents = set()
    
    # Run Workflow
    st.header("🚀 Workflow Execution")
    
    if st.button("Run Simple Test", disabled=st.session_state.workflow_running):
        if not st.session_state.mop_content:
            st.error("❌ Please enter MOP content first!")
        elif not st.session_state.selected_agents:
            st.error("❌ Please select at least one agent!")
        else:
            st.session_state.workflow_running = True
            
            with st.spinner("🔄 Running workflow..."):
                success, result = simple_code_generation(st.session_state.mop_content)
            
            st.session_state.workflow_running = False
            
            if success:
                st.success("✅ Workflow completed successfully!")
                st.subheader("📋 Generated Code:")
                st.code(result, language='python')
                
                # Save to file
                try:
                    os.makedirs("./output", exist_ok=True)
                    with open("./output/generated_code.py", "w") as f:
                        f.write(result)
                    st.info("💾 Code saved to ./output/generated_code.py")
                except Exception as e:
                    st.warning(f"⚠️ Could not save file: {e}")
                    
            else:
                st.error("❌ Workflow failed!")
                st.code(result, language='text')
    
    # Results Display
    if st.session_state.workflow_results:
        st.header("📊 Results")
        for agent_name, result in st.session_state.workflow_results.items():
            with st.expander(f"📋 {agent_name} Results"):
                if result.get('status') == 'success':
                    st.success("✅ Success")
                    st.text_area("Output", result.get('output', ''), height=200)
                else:
                    st.error("❌ Error")
                    st.text_area("Error Details", result.get('error', ''), height=200)

if __name__ == "__main__":
    main()
