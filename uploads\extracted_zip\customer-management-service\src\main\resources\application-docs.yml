# Documentation-Only Configuration
# This profile runs ONLY the documentation server on port 8082
# The main application should be running separately on port 8080

server:
  port: 8082  # Documentation-only port
  servlet:
    context-path: /docs

spring:
  application:
    name: customer-management-docs
  
  # Minimal configuration for docs-only mode
  profiles:
    active: docs
  
  # Disable database connections for docs-only mode
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration

# Disable management endpoints for docs-only mode
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true

# Logging configuration
logging:
  level:
    root: WARN
    org.springframework: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - DOCS - %msg%n"

# OpenAPI/Swagger configuration - Documentation ONLY on port 8082
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui/index.html
    enabled: true
    try-it-out-enabled: true
    filter: true
    display-request-duration: true
    display-operation-id: true
    default-models-expand-depth: 2
    default-model-expand-depth: 2
    doc-expansion: none
    tags-sorter: alpha
    operations-sorter: alpha
    # Point to the main service running on port 8080
    urls:
      - url: http://localhost:8080/customer-service/v3/api-docs
        name: Customer Management Service API
  show-actuator: false
  info:
    title: Customer Management Service API Documentation
    description: VRepair Customer Management Microservice Documentation Server
    version: 1.0.0
    contact:
      name: VRepair Development Team
      email: <EMAIL>
