import streamlit as st
import os
import sys
from datetime import datetime
import traceback
import git
from git import Repo
import shutil
import re
import json
import zipfile
from pathlib import Path

def normalize_path(path_str):
    """Normalize path for cross-platform compatibility."""
    try:
        if isinstance(path_str, str):
            # Convert to Path object and normalize
            return Path(path_str).resolve()
        elif isinstance(path_str, Path):
            return path_str.resolve()
        else:
            return Path(str(path_str)).resolve()
    except Exception as e:
        st.error(f"🔧 DEBUG: Path normalization failed for {path_str}: {e}")
        # Fallback to original path
        return Path(str(path_str))

def ensure_cross_platform_path(path_str):
    """Ensure path works on both Windows and Unix systems."""
    normalized = normalize_path(path_str)
    # Convert to string with forward slashes for consistency
    return str(normalized).replace('\\', '/')

# DEBUG: Test if this file is being loaded (removed from web display)

# Add project root to Python path for imports - CROSS-PLATFORM COMPATIBLE
# dynamic_ui.py is in src/ui/web, so we need to go up 3 levels to reach project root
project_root = Path(__file__).parent.parent.parent.parent
project_root_absolute = project_root.absolute()

# Cross-platform fallback: Try to find project root by looking for .git directory or other markers
if not (project_root_absolute / "config").exists():
    # Try to find the actual project root by going up more levels
    current_path = Path(__file__).parent
    for i in range(10):  # Go up to 10 levels max
        current_path = current_path.parent
        # Use cross-platform path checking
        config_exists = (current_path / "config").exists()
        
        if config_exists:
            project_root_absolute = current_path
            break

if str(project_root_absolute) not in sys.path:
    sys.path.insert(0, str(project_root_absolute))

# Configure Streamlit page - moved to main function
# st.set_page_config(
#     page_title="Radiant.ai : SDLC Agents",
#     page_icon="🤖",
#     layout="wide",
#     initial_sidebar_state="expanded"
# )

# Add the RADIANT logo image in the top-left corner
st.markdown(
    """
    <style>
    .radiant-corner-img {
        position: fixed;
        top: 30px;
        left: 30px;
        width: 180px;
        height: auto;
        z-index: 9999;
        opacity: 0.92;
        pointer-events: none;
        border-radius: 18px;
        box-shadow: 0 8px 32px rgba(102,126,234,0.18);
        transition: opacity 0.3s;
    }
    @media (max-width: 900px) {
        .radiant-corner-img { display: none; }
    }
    </style>
    <img src="assets/radiant_logo.png" class="radiant-corner-img" alt="Radiant Logo" />
    """,
    unsafe_allow_html=True
)

# Enhanced CSS for gorgeous, modern styling
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

    /* Global Styles */
    .stApp {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-family: 'Inter', sans-serif;
    }

    /* Main Container */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        backdrop-filter: blur(10px);
        margin: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Header Styling */
    .main-header {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        animation: fadeInDown 1s ease-out;
    }

    /* Sidebar Styling */
    .css-1d391kg {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(226, 232, 240, 0.5);
    }

    /* Agent Cards */
    .agent-card {
        background: rgba(255, 255, 255, 0.98);
        border: 2px solid #e2e8f0;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .agent-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .agent-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        border-color: #667eea;
    }

    .agent-card:hover::before {
        transform: scaleX(1);
    }

    /* Status Indicators */
    .status-success {
        color: #10b981;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: fadeInLeft 0.5s ease-out;
    }

    .status-error {
        color: #ef4444;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: fadeInLeft 0.5s ease-out;
    }

    .status-running {
        color: #f59e0b;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: pulse 2s infinite;
    }

    /* Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }

    .stButton > button:active {
        transform: translateY(0);
    }

    /* Checkbox Styling */
    .stCheckbox > label {
        font-weight: 500;
        color: #374151;
        font-size: 1.1rem;
    }

    /* Metrics Cards */
    .metric-card {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
        border-color: #667eea;
    }

    /* Progress Bar */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 10px;
        height: 8px;
    }

    /* Expander Styling */
    .streamlit-expanderHeader {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 12px;
        padding: 1rem;
        font-weight: 600;
        color: #374151;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .streamlit-expanderHeader:hover {
        border-color: #667eea;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Text Areas */
    .stTextArea > div > div > textarea {
        border-radius: 12px;
        border: 2px solid #e2e8f0;
        font-family: 'JetBrains Mono', monospace;
        transition: all 0.3s ease;
    }

    .stTextArea > div > div > textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* File Uploader */
    .stFileUploader > div {
        border: 2px dashed #667eea;
        border-radius: 15px;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.95);
        transition: all 0.3s ease;
    }

    .stFileUploader > div:hover {
        border-color: #5a67d8;
        background: rgba(255, 255, 255, 1);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    /* Animations */
    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* Scrollbar Styling */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
    }

    /* Info/Warning/Error Boxes */
    .stAlert {
        border-radius: 12px;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    /* Code Blocks */
    .stCode {
        border-radius: 12px;
        border: 2px solid #e2e8f0;
        font-family: 'JetBrains Mono', monospace;
    }

    /* Download Button */
    .stDownloadButton > button {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.5rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }

    .stDownloadButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.6);
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if 'workflow_results' not in st.session_state:
        st.session_state.workflow_results = {}
    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'selected_agents' not in st.session_state:
        st.session_state.selected_agents = set()
    if 'mop_content' not in st.session_state:
        st.session_state.mop_content = ""
    if 'mop_source' not in st.session_state:
        st.session_state.mop_source = ""
    # GitHub permission tracking
    if 'github_permissions' not in st.session_state:
        st.session_state.github_permissions = {}
    if 'pending_github_pushes' not in st.session_state:
        st.session_state.pending_github_pushes = {}
    # Agent execution permission tracking
    if 'agent_permissions' not in st.session_state:
        st.session_state.agent_permissions = {}
    if 'pending_agent_executions' not in st.session_state:
        st.session_state.pending_agent_executions = {}

def get_agent_descriptions():
    """Return descriptions for each agent."""
    return {
        "coder": {
            "name": "Code Generator",
            "description": "Generates clean Python scripts based on MOP content with best practices and detailed comments.",
            "icon": "💻"
        },
        "reviewer": {
            "name": "Code Validator", 
            "description": "Validates generated code for clarity, functionality, and adherence to best practices.",
            "icon": "🔍"
        },
        "improver": {
            "name": "Code Improver",
            "description": "Enhances and optimizes existing code with performance improvements and better structure.",
            "icon": "✨"
        },
        "integrator": {
            "name": "API Integration",
            "description": "Handles API integration tasks, analysis, design, testing, and optimization.",
            "icon": "🔗"
        },
        "documenter": {
            "name": "ReverseEngineer",
            "description": "Creates comprehensive documentation for generated code and processes.",
            "icon": "📚"
        },
        "prd_agent": {
            "name": "PRD Generator",
            "description": "Generates Product Requirements Documents with user stories and technical specifications.",
            "icon": "📄"
        },
        "qa_agent": {
            "name": "QA & Testing",
            "description": "Performs PRD-based validation, security analysis, and compliance checking with structured reporting.",
            "icon": "🧪"
        }
    }

def load_predefined_mops():
    """Load predefined MOP files."""
    mops = {}
    
    # Add built-in test MOPs
    mops["Simple Calculator"] = """
MOP: Simple Calculator Application

Objective: Create a basic calculator that can perform arithmetic operations.

Steps:
1. Create functions for add, subtract, multiply, and divide
2. Create a main function that takes user input
3. Handle division by zero errors
4. Display results clearly
5. Allow multiple calculations

Expected Output:
A complete Python calculator script with error handling.
"""
    
    mops["File Processor"] = """
MOP: File Reading and Processing

Objective: Create a script that reads and processes text files.

Steps:
1. Create a function to read a text file
2. Count the number of lines and words
3. Find the most common words
4. Save results to an output file
5. Handle file not found errors

Expected Output:
A Python script for file processing with comprehensive error handling.
"""
    
    mops["Web Scraper"] = """
MOP: Simple Web Scraper

Objective: Create a basic web scraper to extract data from websites.

Steps:
1. Import required libraries (requests, BeautifulSoup)
2. Create a function to fetch web page content
3. Parse HTML and extract specific data
4. Save extracted data to CSV file
5. Add error handling for network issues

Expected Output:
A Python web scraping script with proper error handling.
"""
    
    # Try to load existing MOP files
    try:
        from mop_cisco_audit import MOP_CONTENT
        mops["Cisco XR Device Audit"] = MOP_CONTENT
    except ImportError:
        pass
    
    try:
        from PRDmop import PRD_MOP_CONTENT
        mops["PRD Generation"] = PRD_MOP_CONTENT
    except ImportError:
        pass
    
    return mops

# Multi-Model API functions
def call_multi_model_api(prompt, task_type="general", use_multi_model=False):
    """Call multiple AI models for validation to reduce hallucination."""
    if not use_multi_model:
        return call_claude_api(prompt, task_type)
    
    results = []
    models_used = []
    
    # Try Claude Sonnet 3.5 (most reliable for code analysis)
    try:
        import sys
        import os
        # Add project root to path if not already there
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        from config.config import API_KEY
        from anthropic import Anthropic
        
        client = Anthropic(api_key=API_KEY)
        response = client.messages.create(
            model="claude-3-5-sonnet-20241022",  # Latest Claude Sonnet
            max_tokens=8000,  # Sonnet supports higher token limits
            messages=[{"role": "user", "content": prompt}]
        )
        
        response_text = response.content[0].text
        import re
        response_text = re.sub(r'<[^>]*>', '', response_text)
        results.append(("Claude Sonnet 3.5", response_text))
        models_used.append("Claude Sonnet 3.5")
        
    except Exception as e:
        results.append(("Claude Sonnet 3.5", f"Error: {str(e)}"))
    
    # Try Gemini 2.0 Flash (if available - fallback to basic Claude)
    try:
        import google.generativeai as genai
        # Import with proper path setup
        import sys
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        from config.config import GEMINI_API_KEY  # Add this to config
        
        genai.configure(api_key=GEMINI_API_KEY)
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
        response = model.generate_content(prompt)
        results.append(("Gemini 2.0 Flash", response.text))
        models_used.append("Gemini 2.0 Flash")
        
    except Exception as e:
        # Fallback to Claude Haiku for secondary validation
        try:
            import sys
            import os
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)
            from config.config import API_KEY
            from anthropic import Anthropic
            client = Anthropic(api_key=API_KEY)
            response = client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=4000,  # Haiku has lower token limits
                messages=[{"role": "user", "content": prompt}]
            )
            response_text = response.content[0].text
            response_text = re.sub(r'<[^>]*>', '', response_text)
            results.append(("Claude Haiku (Fallback)", response_text))
            models_used.append("Claude Haiku")
        except Exception as fallback_error:
            results.append(("Gemini/Fallback", f"Both Gemini and fallback failed: {str(e)}"))
    
    # Combine results from multiple models
    if len(results) > 1:
        combined_result = f"""# 🤖 MULTI-MODEL VALIDATION REPORT
        
## Models Used: {', '.join(models_used)}

---

"""
        for i, (model_name, result) in enumerate(results, 1):
            if not result.startswith("Error:"):
                combined_result += f"""## 📊 Analysis from {model_name}

{result}

---

"""
        
        # Add consensus summary
        combined_result += f"""## 🎯 MULTI-MODEL CONSENSUS

This validation was performed using {len([r for r in results if not r[1].startswith('Error:')])} different AI models to reduce hallucination and provide more reliable insights.

**Models Successfully Used:** {', '.join(models_used)}

**Recommendation:** Pay attention to issues that appear consistently across multiple model analyses, as these are more likely to be genuine concerns.
"""
        
        return True, combined_result
    else:
        # Single model result
        return True, results[0][1] if results else False, "All models failed"

def call_claude_api(prompt, task_type="general"):
    """Call Claude API with the given prompt."""
    try:
        import sys
        import os
        # Add project root to path if not already there
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
            
        from config.config import API_KEY
        from anthropic import Anthropic
        import html
        
        client = Anthropic(api_key=API_KEY)
        
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=4000,  # Haiku token limit
            messages=[{"role": "user", "content": prompt}]
        )
        
        # Get response text and remove all HTML-like content to prevent createElement errors
        response_text = response.content[0].text
        # Remove all HTML tags to prevent any createElement issues
        import re
        response_text = re.sub(r'<[^>]*>', '', response_text)
        
        return True, response_text
        
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        full_traceback = traceback.format_exc()
        return False, f"{error_msg}\n\nFull traceback:\n{full_traceback}"

def generate_code(mop_content, language="Python"):
    """Generate code using Claude API for the specified language, supporting multi-file output."""
    prompt = f"""
You are an expert {language} developer. Generate a complete {language} project based on the following input.

Input:
{mop_content}

If the project requires multiple files (e.g., React: .jsx and .css, Python: .py and requirements.txt), return a JSON object where each key is a filename and each value is the file content. Example:
{{
  "App.jsx": "...",
  "App.css": "...",
  "index.js": "..."
}}

If only one file is needed, return a JSON object with a single file.

Return only the JSON object, no explanation.
"""
    return call_claude_api(prompt, "code_generation")

def review_code_chunked(files_with_metadata, prd_content=None, validation_files=None, use_multi_model=False):
    """
    Multi-pass validation system for large codebases using intelligent chunking.
    
    Args:
        files_with_metadata: List of file dictionaries with metadata
        prd_content: PRD content for requirements validation
        validation_files: Dict of validation files
        use_multi_model: Whether to use multiple AI models
    """
    chunker = CodebaseChunker(max_tokens_per_chunk=160000)  # Claude Sonnet limit
    chunks = chunker.chunk_by_priority(files_with_metadata)
    
    st.info(f"🔄 Processing codebase in {len(chunks)} intelligent chunks...")
    
    chunk_results = []
    
    for i, chunk in enumerate(chunks):
        st.info(f"📊 Analyzing chunk {i + 1}/{len(chunks)} (Priority {chunk['priority_range'][0]}-{chunk['priority_range'][1]}, {len(chunk['files'])} files)")
        
        # Prepare chunk content
        chunk_content = chunker.prepare_chunk_content(chunk, i, len(chunks))
        
        # Debug: Check if chunk content is actually populated
        if len(chunk_content) < 100:
            st.warning(f"⚠️ Chunk {i + 1} content seems too short ({len(chunk_content)} chars). This might indicate an issue.")
            st.text(f"Chunk content preview: {chunk_content[:200]}...")
        else:
            st.success(f"✅ Chunk {i + 1} ready with {len(chunk_content)} characters of code content")
        
        # Create chunk-specific validation prompt
        chunk_prompt = create_chunk_validation_prompt(
            chunk_content, 
            chunk, 
            i + 1, 
            len(chunks),
            prd_content, 
            validation_files
        )
        
        # Validate this chunk
        try:
            success, result = call_multi_model_api(chunk_prompt, "code_review_chunk", use_multi_model)
            if success:
                chunk_results.append({
                    'chunk_index': i + 1,
                    'priority_range': chunk['priority_range'],
                    'file_types': list(chunk['file_types']),
                    'file_count': len(chunk['files']),
                    'result': result
                })
                st.success(f"✅ Chunk {i + 1} validated successfully ({len(result)} chars)")
            else:
                st.warning(f"⚠️ Chunk {i + 1} validation failed: {result}")
                chunk_results.append({
                    'chunk_index': i + 1,
                    'priority_range': chunk['priority_range'],
                    'file_types': list(chunk['file_types']),
                    'file_count': len(chunk['files']),
                    'result': f"Validation failed: {result}",
                    'error': True
                })
        except Exception as e:
            st.error(f"❌ Error processing chunk {i + 1}: {e}")
            chunk_results.append({
                'chunk_index': i + 1,
                'error': True,
                'result': f"Error: {e}"
            })
    
    # Aggregate results into final report
    final_report = aggregate_chunk_results(chunk_results, files_with_metadata, prd_content)
    
    return True, final_report

def create_chunk_validation_prompt(chunk_content, chunk_metadata, chunk_num, total_chunks, prd_content=None, validation_files=None):
    """Create a specialized validation prompt for a chunk."""
    
    prd_section = ""
    if prd_content:
        prd_section = f"""
### 📋 PRD REQUIREMENTS CONTEXT

The code should be evaluated against the following Product Requirements Document:

```
{prd_content}
```
"""

    validation_files_section = ""
    if validation_files:
        validation_files_section = """
### 📁 VALIDATION FILES CONTEXT

Additional validation criteria from uploaded files:

"""
        for filename, content in validation_files.items():
            file_type = "Test Cases" if filename.endswith(('.test', '.spec', '.py')) else "Standards Document"
            validation_files_section += f"""
#### {file_type}: {filename}
```
{content[:1500]}{'...' if len(content) > 1500 else ''}
```

"""

    prompt = f"""
You are a **Senior Software Architect and Code Quality Expert** conducting a focused analysis of a codebase chunk.

{prd_section}

{validation_files_section}

### 🎯 CHUNK ANALYSIS CONTEXT

**Chunk {chunk_num} of {total_chunks}**
- Priority Level: {chunk_metadata['priority_range'][0]}-{chunk_metadata['priority_range'][1]} (10=highest)
- File Types: {', '.join(sorted(chunk_metadata['file_types']))}
- Files in Chunk: {len(chunk_metadata['files'])}

### 🔍 FOCUSED ANALYSIS OBJECTIVES

Since this is chunk {chunk_num} of {total_chunks}, focus your analysis on:

1. **Code Quality Assessment** for files in this priority range
2. **Architecture and Design Patterns** evident in these files
3. **Security Vulnerabilities** specific to this code subset
4. **Performance Considerations** for the included functionality
5. **Requirements Traceability** (if PRD provided)

### 📊 CHUNK-SPECIFIC OUTPUT FORMAT

**CHUNK {chunk_num} ANALYSIS SUMMARY**

**Priority Focus**: {chunk_metadata['priority_range'][0]}-{chunk_metadata['priority_range'][1]} priority files
**Key Findings**: [3-5 most critical issues in this chunk]
**Architecture Notes**: [Design patterns and structural observations]
**Security Assessment**: [Security-specific findings for this chunk]
**Performance Impact**: [Performance considerations for included code]

**DETAILED FINDINGS**:
[Provide specific, actionable findings with file references]

### 📁 CODEBASE CHUNK CONTENT
Please review the following Java codebase chunk:

{chunk_content}

### 📊 CHUNK STATISTICS
- Chunk {chunk_num} of {total_chunks}
- Files in this chunk: {len(chunk_metadata['files'])}
- Content length: {len(chunk_content)} characters

**IMPORTANT**: This is actual Java code that needs comprehensive validation. Please analyze the provided code thoroughly.
"""
    
    return prompt

def aggregate_chunk_results(chunk_results, files_with_metadata, prd_content=None):
    """Aggregate results from multiple chunks into a comprehensive report."""
    
    # Calculate summary statistics
    total_files = len(files_with_metadata)
    total_chunks = len(chunk_results)
    successful_chunks = len([r for r in chunk_results if not r.get('error', False)])
    
    # Organize files by priority for summary
    priority_distribution = {}
    for file_data in files_with_metadata:
        priority = file_data['importance']
        if priority not in priority_distribution:
            priority_distribution[priority] = 0
        priority_distribution[priority] += 1
    
    # Build comprehensive report
    report = f"""# 🤖 COMPREHENSIVE MULTI-CHUNK VALIDATION REPORT

## 📊 ANALYSIS OVERVIEW

**Codebase Scale**: {total_files} files processed across {total_chunks} intelligent chunks
**Processing Success**: {successful_chunks}/{total_chunks} chunks analyzed successfully
**File Priority Distribution**: {dict(sorted(priority_distribution.items(), reverse=True))}

## 🎯 CHUNK-BY-CHUNK ANALYSIS

"""
    
    # Add each chunk's results
    for chunk_result in chunk_results:
        if not chunk_result.get('error', False):
            report += f"""
### Chunk {chunk_result['chunk_index']} - Priority {chunk_result['priority_range'][0]}-{chunk_result['priority_range'][1]}
**File Types**: {', '.join(chunk_result['file_types'])}
**Files Analyzed**: {chunk_result.get('file_count', 'unknown')}

{chunk_result['result']}

---

"""
        else:
            report += f"""
### Chunk {chunk_result['chunk_index']} - ERROR
**Status**: Failed to process
**Error**: {chunk_result.get('result', 'Unknown error')}

---

"""
    
    # Add summary and recommendations
    report += f"""
## 🎯 COMPREHENSIVE RECOMMENDATIONS

**Based on analysis of {total_files} files across {successful_chunks} chunks:**

1. **High Priority Issues**: Focus on findings from Priority 10-9 files (core business logic)
2. **Architecture Review**: Consider patterns identified across multiple chunks
3. **Security Assessment**: Address security findings from all analyzed chunks
4. **Performance Optimization**: Implement performance recommendations from chunk analyses

## 📈 NEXT STEPS

1. **Address Critical Issues**: Start with Priority 10 files (controllers, services)
2. **Review Architecture**: Consider structural improvements identified
3. **Security Hardening**: Implement security recommendations
4. **Quality Gates**: Establish quality metrics based on findings

---

*This report was generated using intelligent codebase chunking to handle large-scale validation while maintaining comprehensive analysis quality.*
"""
    
    return report

def review_code(code, prd_content=None, custom_validation_request=None, validation_files=None, use_multi_model=False):
    """
    Enhanced code review with industry standards, custom validation files, and multi-model support.
    
    Args:
        code: Code to review (str or tuple for multi-file)
        prd_content: PRD content for requirements validation
        custom_validation_request: Custom validation instructions
        validation_files: Dict of validation files (md, test cases, etc.)
        use_multi_model: Whether to use multiple AI models for validation
    """
    
    # Support: code (str) or (code, guidelines_markdown, file_list, files_metadata) for backward compatibility
    if isinstance(code, tuple) and len(code) >= 3:
        if len(code) == 4:
            # New format with structured metadata - use chunked validation
            codebase, guidelines_md, file_list, files_metadata = code
            
            # Check if we should use chunked validation (large codebase or many files)
            total_tokens = sum(file_data.get('tokens_estimated', 0) for file_data in files_metadata)
            should_chunk = total_tokens > 100000 or len(files_metadata) > 20
            
            if should_chunk:
                try:
                    return review_code_chunked(files_metadata, prd_content, validation_files, use_multi_model)
                except Exception as e:
                    st.error(f"❌ Chunked validation failed: {str(e)}")
                    st.info("🔄 Falling back to single-pass validation...")
                    # Fall through to single-pass validation
            else:
                pass
        
        # Legacy format or small codebase - use original method
        codebase, guidelines_md, file_list = code[:3]
        
        # Build comprehensive validation context
        prd_section = ""
        if prd_content:
            prd_section = f"""
### 📋 PRD REQUIREMENTS CONTEXT

The code should be evaluated against the following Product Requirements Document:

```
{prd_content}
```

**Key Focus Areas from PRD:**
- Verify implementation aligns with specified user stories and acceptance criteria
- Ensure all functional requirements are properly addressed
- Check technical requirements compliance
- Validate non-functional requirements (performance, security, scalability)
- Assess business objectives fulfillment

"""

        # Add validation files context
        validation_files_section = ""
        if validation_files:
            validation_files_section = """
### 📁 VALIDATION FILES CONTEXT

Additional validation criteria from uploaded files:

"""
            for filename, content in validation_files.items():
                file_type = "Test Cases" if filename.endswith(('.test', '.spec', '.py')) else "Standards Document"
                validation_files_section += f"""
#### {file_type}: {filename}
```
{content[:2000]}{'...' if len(content) > 2000 else ''}
```

"""

        # Industry standards section (always included)
        industry_standards_section = """
### 🏭 INDUSTRY STANDARDS VALIDATION

Apply the following industry-standard validation criteria:

**🔒 SECURITY STANDARDS:**
- OWASP Top 10 compliance
- Input validation and sanitization
- Authentication and authorization mechanisms
- Secure coding practices (SANS, NIST guidelines)
- Data protection and encryption standards

**📊 CODE QUALITY STANDARDS:**
- Clean Code principles (Robert Martin)
- SOLID design principles
- Language-specific best practices (PEP 8 for Python, Google Style Guides)
- Code complexity analysis (Cyclomatic complexity < 10)
- Error handling and logging standards

**🏗️ ARCHITECTURAL STANDARDS:**
- Design patterns implementation
- Separation of concerns
- Dependency injection principles
- Testability and maintainability
- Performance optimization guidelines

**🧪 TESTING STANDARDS:**
- Unit test coverage (>80%)
- Integration test completeness
- Test naming conventions
- Test independence and reliability

**📝 DOCUMENTATION STANDARDS:**
- API documentation completeness
- Code comments quality
- README and setup instructions
- Version control best practices

"""
        
        prompt = f"""
You are a **Senior Software Architect, Security Expert, and Code Quality Validator Agent**.

Your role is to conduct a comprehensive code review against **PRD requirements**, **industry best practices**, and **user's custom validation requests**.

{prd_section}

{industry_standards_section}

{validation_files_section}

### 🎯 REVIEW OBJECTIVES

**PRIMARY GOALS:**
- Validate implementation against PRD specifications
- Ensure code quality, security, and maintainability
- Identify critical issues and provide actionable solutions
- Assess architectural decisions and design patterns

### 📋 CONTEXT

- **PRD**: Reference specification provided above
- **Codebase**: Multiple files provided for comprehensive review
- **Custom Guidelines**: {guidelines_md}
- **Validation Focus**: User-specified analysis requirements

### 🔍 COMPREHENSIVE ANALYSIS FRAMEWORK

**1. REQUIREMENTS TRACEABILITY**
   - Map each PRD requirement to implementation
   - Verify functional completeness
   - Identify missing or partially implemented features

**2. CODE QUALITY ASSESSMENT**
   - **Readability**: Clear naming, structure, documentation
   - **Maintainability**: Modularity, separation of concerns, DRY principles
   - **Performance**: Algorithm efficiency, resource usage, scalability
   - **Error Handling**: Exception management, graceful degradation

**3. SECURITY ANALYSIS**
   - **Authentication & Authorization**: Access controls, session management
   - **Input Validation**: SQL injection, XSS, data sanitization
   - **Data Protection**: Encryption, sensitive data handling
   - **Vulnerability Assessment**: OWASP Top 10, security patterns

**4. ARCHITECTURAL REVIEW**
   - **Design Patterns**: Appropriate pattern usage, anti-patterns
   - **Dependencies**: Coupling, cohesion, dependency injection
   - **Scalability**: Performance bottlenecks, resource management
   - **Testing**: Unit tests, integration tests, coverage

**5. COMPLIANCE & STANDARDS**
   - **Coding Standards**: Language-specific conventions
   - **Documentation**: API docs, inline comments, README
   - **Configuration**: Environment management, secrets handling
   - **Deployment**: CI/CD readiness, containerization

### 📊 STRUCTURED OUTPUT FORMAT

**1. EXECUTIVE SUMMARY**
   - Overall code quality score (1-10)
   - Critical issues count
   - Compliance status
   - Recommendation priority

**2. REQUIREMENTS MAPPING**
   | Requirement ID | Description | Implementation Status | Coverage % | Notes |
   |---|---|---|---|---|
   | REQ-001 | [Description] | ✅ Implemented / ⚠️ Partial / ❌ Missing | 85% | [Details] |

**3. CRITICAL FINDINGS**
   - **🚨 P0 (Critical)**: Security vulnerabilities, data loss risks
   - **⚠️ P1 (High)**: Performance issues, major bugs
   - **📋 P2 (Medium)**: Code quality, maintainability
   - **💡 P3 (Low)**: Optimizations, suggestions

**4. CODE IMPROVEMENTS**
   ```
   🔧 Issue: [Description]
   📍 Location: [file:line]
   ⚠️ Impact: [Security/Performance/Maintainability]
   🎯 Priority: [P0/P1/P2/P3]
   
   ❌ Current Code:
   ```language
   [current code snippet]
   ```
   
   ✅ Improved Code:
   ```language
   [improved code snippet]
   ```
   
   💡 Rationale: [Explanation of improvement]
   ```

**5. SECURITY ASSESSMENT**
   - **Authentication**: [Status and recommendations]
   - **Authorization**: [RBAC implementation review]
   - **Data Validation**: [Input sanitization analysis]
   - **Encryption**: [Data protection assessment]
   - **Vulnerabilities**: [Security risks identified]

### 🎯 QUALITY GATES

**PASS CRITERIA:**
- No P0 (Critical) issues
- < 3 P1 (High) issues
- Code quality score ≥ 7/10
- Security assessment: PASS
- Requirements coverage ≥ 90%

### 📋 REVIEW RULES

- **Evidence-Based**: All findings must reference specific code locations
- **Actionable**: Provide concrete, implementable solutions
- **Prioritized**: Rank issues by business impact and technical risk
- **Comprehensive**: Cover all aspects of code quality and security
- **Language-Aware**: Apply language-specific best practices
- **Context-Sensitive**: Consider project requirements and constraints

**Files to Review:**
{file_list}
{validation_files_section}

**Custom Validation Focus:**
{guidelines_md}

### 📁 CODEBASE CONTENT

Please review the following codebase files:

{codebase[:50000] + ('...' + chr(10) + chr(10) + '[CODEBASE TRUNCATED - Total length: ' + str(len(codebase)) + ' characters. Showing first 50,000 characters for analysis.]' if len(codebase) > 50000 else codebase)}
"""
        
        return call_multi_model_api(prompt, "code_review_guidelines", use_multi_model)
    else:
        # Single file review with industry standards
        prd_context = ""
        if prd_content:
            prd_context = f"""

### 📋 PRD REQUIREMENTS CONTEXT

The code should be evaluated against the following Product Requirements Document:

```
{prd_content}
```

**Key Focus Areas from PRD:**
- Verify implementation aligns with specified user stories and acceptance criteria
- Ensure all functional requirements are properly addressed
- Check technical requirements compliance
- Validate non-functional requirements (performance, security, scalability)
- Assess business objectives fulfillment

"""

        # Add validation files context for single file review
        validation_files_section = ""
        if validation_files:
            validation_files_section = """

### 📁 VALIDATION FILES CONTEXT

Additional validation criteria from uploaded files:

"""
            for filename, content in validation_files.items():
                file_type = "Test Cases" if filename.endswith(('.test', '.spec', '.py')) else "Standards Document"
                validation_files_section += f"""
#### {file_type}: {filename}
```
{content[:2000]}{'...' if len(content) > 2000 else ''}
```

"""

        # Industry standards for single file review
        industry_standards_section = """

### 🏭 INDUSTRY STANDARDS VALIDATION

Apply the following industry-standard validation criteria:

**🔒 SECURITY STANDARDS:**
- OWASP Top 10 compliance
- Input validation and sanitization
- Authentication and authorization mechanisms
- Secure coding practices (SANS, NIST guidelines)
- Data protection and encryption standards

**📊 CODE QUALITY STANDARDS:**
- Clean Code principles (Robert Martin)
- SOLID design principles
- Language-specific best practices (PEP 8 for Python, Google Style Guides)
- Code complexity analysis (Cyclomatic complexity < 10)
- Error handling and logging standards

**🏗️ ARCHITECTURAL STANDARDS:**
- Design patterns implementation
- Separation of concerns
- Dependency injection principles
- Testability and maintainability
- Performance optimization guidelines

"""
        
        custom_context = f"\n\n**Custom Validation Request:**\n{custom_validation_request}" if custom_validation_request else ""
        
        prompt = f"""
You are a **Senior Software Architect, Security Expert, and Code Quality Validator Agent**.

Your mission is to conduct a **comprehensive code review** that ensures **production readiness**, **security compliance**, and **architectural excellence**.

{prd_context}

{industry_standards_section}

{validation_files_section}

---

## 🎯 REVIEW OBJECTIVES

**PRIMARY GOALS:**
- Validate against PRD requirements and business logic
- Ensure enterprise-grade security and compliance
- Assess code quality, maintainability, and performance
- Provide actionable improvement recommendations
- Identify architectural strengths and weaknesses

---

## 📋 CONTEXT & SCOPE

- **PRD Requirements**: Product specification compliance (provided above)
- **Code Artifact**: Single file/component under review
- **Custom Focus**: Specific validation requirements{custom_context}
- **Review Depth**: Comprehensive analysis across all quality dimensions

---

## 🔍 MULTI-DIMENSIONAL ANALYSIS FRAMEWORK

### 1. **REQUIREMENTS VALIDATION**
   - Functional requirement implementation
   - Business logic correctness
   - Edge case handling
   - User story fulfillment

### 2. **CODE QUALITY ASSESSMENT**
   - **Readability**: Clear naming, structure, self-documenting code
   - **Maintainability**: Modularity, SOLID principles, clean architecture
   - **Reliability**: Error handling, input validation, defensive programming
   - **Efficiency**: Algorithm optimization, resource management

### 3. **SECURITY ANALYSIS**
   - **OWASP Top 10**: Injection, broken auth, sensitive data exposure
   - **Input Validation**: SQL injection, XSS, command injection prevention
   - **Access Control**: Authentication, authorization, privilege escalation
   - **Data Protection**: Encryption, secure storage, transmission security
   - **Configuration**: Secure defaults, secrets management

### 4. **ARCHITECTURAL REVIEW**
   - **Design Patterns**: Appropriate pattern usage, anti-pattern detection
   - **Separation of Concerns**: Single responsibility, loose coupling
   - **Scalability**: Performance bottlenecks, resource scaling
   - **Testability**: Unit test coverage, mock-friendly design
   - **Dependencies**: Third-party libraries, version management

### 5. **PERFORMANCE EVALUATION**
   - **Time Complexity**: Algorithm efficiency analysis
   - **Space Complexity**: Memory usage optimization
   - **I/O Operations**: Database queries, file operations, network calls
   - **Caching Strategy**: Data caching, computation optimization

---

## 📊 COMPREHENSIVE OUTPUT STRUCTURE

### **🎯 EXECUTIVE SUMMARY**
```
📊 Overall Quality Score: [X/10]
🚨 Critical Issues: [count]
⚠️ High Priority Issues: [count]
📋 Medium Priority Issues: [count]
✅ Requirements Coverage: [X%]
🛡️ Security Status: [PASS/FAIL]
🚀 Production Readiness: [READY/NEEDS_WORK]
```

### **📋 REQUIREMENTS TRACEABILITY**
| Requirement | Implementation Status | Coverage | Evidence | Recommendation |
|---|---|---|---|---|
| [REQ-ID] | ✅ Complete / ⚠️ Partial / ❌ Missing | [%] | [Code reference] | [Action needed] |

### **🔍 DETAILED FINDINGS**

#### **🚨 CRITICAL ISSUES (P0)**
```
🔴 Issue: [Security vulnerability/Data loss risk]
📍 Location: [file:line]
💥 Impact: [Business/Security impact]
🛠️ Fix: [Immediate action required]

❌ Vulnerable Code:
```language
[problematic code]
```

✅ Secure Implementation:
```language
[fixed code with security measures]
```

💡 Security Rationale: [Why this fix prevents the vulnerability]
```

#### **⚠️ HIGH PRIORITY ISSUES (P1)**
```
🟡 Issue: [Performance/Functionality problem]
📍 Location: [file:line]
📊 Impact: [Performance/User experience impact]
🔧 Solution: [Optimization approach]

❌ Current Implementation:
```language
[current code]
```

✅ Optimized Implementation:
```language
[improved code]
```

📈 Performance Gain: [Expected improvement]
```

#### **📋 MEDIUM PRIORITY ISSUES (P2)**
```
🔵 Issue: [Code quality/Maintainability concern]
📍 Location: [file:line]
🎯 Benefit: [Long-term maintainability improvement]
🛠️ Refactor: [Code improvement approach]
```

### **🛡️ SECURITY ASSESSMENT**
```
🔐 Authentication: [Analysis and recommendations]
🔑 Authorization: [Access control review]
🛡️ Input Validation: [Data sanitization assessment]
🔒 Data Protection: [Encryption and privacy review]
⚠️ Vulnerabilities: [Security risks and mitigations]
📋 Compliance: [Standards adherence check]
```

### **🚀 PERFORMANCE ANALYSIS**
```
⚡ Bottlenecks: [Performance constraints identified]
💾 Memory Usage: [Resource consumption analysis]
🔄 Scalability: [Horizontal/vertical scaling readiness]
📊 Optimization Opportunities: [Performance improvements]
```

### **🏗️ ARCHITECTURAL ASSESSMENT**
```
🎨 Design Patterns: [Pattern usage evaluation]
🔗 Dependencies: [Coupling and cohesion analysis]
🧪 Testability: [Unit testing readiness]
📦 Modularity: [Component separation review]
```

### **✅ QUALITY CHECKLIST**
- [ ] **Functionality**: All requirements implemented correctly
- [ ] **Reliability**: Robust error handling and edge cases
- [ ] **Security**: No vulnerabilities, secure by design
- [ ] **Performance**: Efficient algorithms and resource usage
- [ ] **Maintainability**: Clean, readable, well-structured code
- [ ] **Testability**: Unit test coverage and mock-friendly design
- [ ] **Documentation**: Clear comments and API documentation
- [ ] **Standards**: Coding conventions and best practices

### **🎯 RECOMMENDATIONS SUMMARY**
```
🚨 IMMEDIATE ACTIONS (P0):
1. [Critical security fix]
2. [Data integrity issue]

⚠️ HIGH PRIORITY (P1):
1. [Performance optimization]
2. [Functionality enhancement]

📋 MEDIUM PRIORITY (P2):
1. [Code refactoring]
2. [Documentation improvement]

💡 FUTURE ENHANCEMENTS (P3):
1. [Optimization opportunity]
2. [Feature suggestion]
```

---

## 🎯 QUALITY GATES & ACCEPTANCE CRITERIA

**✅ PRODUCTION READY CRITERIA:**
- Zero P0 (Critical) issues
- ≤ 2 P1 (High) issues
- Code quality score ≥ 8/10
- Security assessment: PASS
- Requirements coverage ≥ 95%
- Performance benchmarks met

**📋 REVIEW PRINCIPLES:**
- **Evidence-Based**: Every finding backed by code references
- **Actionable**: Concrete, implementable solutions provided
- **Risk-Prioritized**: Issues ranked by business and technical impact
- **Holistic**: Comprehensive coverage of all quality dimensions
- **Context-Aware**: Considers project constraints and requirements
- **Best-Practice Aligned**: Industry standards and proven patterns

---

**Code Under Review:**
```
{code}
```
"""
        return call_multi_model_api(prompt, "code_review", use_multi_model)

def improve_code(code):
    """Improve code using Claude API."""
    prompt = f"""
You are an expert software developer. Please improve the following code by:
- Optimizing performance
- Adding better error handling
- Improving code structure
- Adding more comprehensive documentation
- Following expert software best practices

Original code:
{code}

Return the improved version of the code with explanations for major changes.
"""
    return call_claude_api(prompt, "code_improvement")

def generate_api_integration(mop_content):
    """Generate API integration code using Claude API."""
    prompt = f"""
You are an API integration expert. Based on the following MOP, create a Python script that demonstrates API integration concepts:

MOP Content:
{mop_content}

Generate code that includes:
- API client setup
- Request/response handling
- Error handling for API calls
- Data processing
- Configuration management

Return a complete Python script with API integration examples.
"""
    return call_claude_api(prompt, "api_integration")

def generate_documentation(code):
    """Generate documentation using Claude API."""
    prompt = f"""
You are a technical documentation expert. Create comprehensive documentation for the following Python code:

Code:
{code}

Generate documentation that includes:
- Overview and purpose
- Function descriptions
- Parameter explanations
- Usage examples
- Installation requirements
- Error handling notes

Format the documentation in Markdown.
"""
    return call_claude_api(prompt, "documentation")

def generate_prd(mop_content):
    """Generate PRD using Claude API with proper epic structure."""
    prompt = f"""
You are a technical writer specializing in PRD creation. Generate a comprehensive Product Requirements Document (PRD) based on the provided MOP content.

MOP Content:
{mop_content}

Create a PRD document with the following structure and format EXACTLY as shown:

# Product Requirements Document (PRD)

## Product Overview
[Provide a comprehensive overview of the product/feature]

## Business Objectives
[List the key business objectives]

## Target Users
[Define the target user personas]

## Functional Requirements

### Epic 1: [Epic Name]
[Epic description and scope]

**User Stories:**
- **US-001**: As a [user type], I want [functionality] so that [benefit]
- **US-002**: As a [user type], I want [functionality] so that [benefit]
- **US-003**: As a [user type], I want [functionality] so that [benefit]

**Acceptance Criteria:**
- [Specific acceptance criteria for Epic 1]
- [Additional criteria]

### Epic 2: [Epic Name]
[Epic description and scope]

**User Stories:**
- **US-004**: As a [user type], I want [functionality] so that [benefit]
- **US-005**: As a [user type], I want [functionality] so that [benefit]
- **US-006**: As a [user type], I want [functionality] so that [benefit]

**Acceptance Criteria:**
- [Specific acceptance criteria for Epic 2]
- [Additional criteria]

### Epic 3: [Epic Name]
[Epic description and scope]

**User Stories:**
- **US-007**: As a [user type], I want [functionality] so that [benefit]
- **US-008**: As a [user type], I want [functionality] so that [benefit]

**Acceptance Criteria:**
- [Specific acceptance criteria for Epic 3]
- [Additional criteria]

## Technical Requirements
[Detailed technical specifications]

## Non-Functional Requirements
[Performance, security, scalability requirements]

## Success Metrics
[Key performance indicators and success criteria]

## Timeline and Milestones
[Project timeline with key milestones]

## Dependencies and Assumptions
[External dependencies and key assumptions]

IMPORTANT:
- Use EXACTLY the format "### Epic X: [Epic Name]" for epic headers
- Use EXACTLY the format "**US-XXX**: [Description]" for user stories
- Include at least 3 epics with 2-3 user stories each
- Make sure epic numbers are sequential (Epic 1, Epic 2, Epic 3, etc.)
- Make sure user story numbers are sequential (US-001, US-002, etc.)
"""
    return call_claude_api(prompt, "prd_generation")

def create_jira_issues_from_prd():
    """Create Jira issues from PRD file with acceptance criteria."""
    try:
        import sys
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        from generated_scripts.create_jira_issues_from_prd import create_jira_issues_from_prd, test_prd_parsing

        # First test the parsing to see if acceptance criteria are found
        parsing_success = test_prd_parsing()

        if parsing_success:
            # Create the actual Jira issues
            create_jira_issues_from_prd()
            return True, "Jira epics and user stories created successfully with acceptance criteria included in descriptions"
        else:
            return False, "Failed to parse PRD file - check PRD format"

    except Exception as e:
        return False, f"Failed to create Jira issues: {str(e)}"

def create_jira_bug_ticket(test_output):
    """Create Jira bug ticket from test failures."""
    try:
        import sys
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        from src.agents.core.qa_agent import create_jira_bug
        create_jira_bug(test_output)
        return True, "Jira bug ticket created successfully"
    except Exception as e:
        return False, f"Failed to create Jira bug ticket: {str(e)}"

def check_jira_configuration():
    """Check if Jira is properly configured."""
    try:
        import sys
        import os
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        from config.config import JIRA_API_CONFIG
        required_keys = ['base_url', 'api_key', 'email', 'project_key']

        for key in required_keys:
            if not JIRA_API_CONFIG.get(key):
                return False, f"Missing Jira configuration: {key}"

        return True, "Jira configuration is complete"
    except ImportError:
        return False, "Jira configuration not found in config.py"
    except Exception as e:
        return False, f"Jira configuration error: {str(e)}"

def generate_qa_tests(mop_content, code=None):
    """Generate QA tests using Claude API."""
    prompt = f"""
You are a QA engineer. Based on the following MOP{' and code' if code else ''}, create comprehensive test cases:

MOP Content:
{mop_content}

{f'Code to test:{chr(10)}{code}{chr(10)}' if code else ''}

Generate:
- Unit test cases
- Integration test scenarios
- Edge case testing
- Performance test considerations
- Security test cases

Provide test cases in Python using pytest framework.
"""
    return call_claude_api(prompt, "qa_testing")

# Load from environment variables or config file
import os
GITHUB_TOKEN = os.getenv('GITHUB_TOKEN', '')
GITHUB_USERNAME = os.getenv('GITHUB_USERNAME', 'default_user')
GITHUB_API_URL = "https://github.com/"
GITHUB_PUSH_URL_TEMPLATE = "https://{username}:{token}@github.com/{username}/{repo}.git"

def get_repo_name_from_mop(mop_content):
    # Handle both string and dict types for mop_content
    if isinstance(mop_content, dict):
        # Use the first filename as base, or the first file's content if no filename
        if mop_content:
            first_key = next(iter(mop_content.keys()))
            base = first_key or "mop_repo"
        else:
            base = "mop_repo"
    elif isinstance(mop_content, str):
        # Use first non-empty line as base
        for line in mop_content.split("\n"):
            line = line.strip()
            if line:
                base = line
                break
        else:
            base = "mop_repo"
    else:
        base = "mop_repo"
    # Only allow alphanumeric, dash, underscore
    repo_name = re.sub(r'[^A-Za-z0-9_-]', '', base)
    repo_name = repo_name[:30]  # Limit length
    if not repo_name:
        repo_name = "mop_repo"
    return repo_name

def ensure_github_repo(repo_name):
    import requests
    headers = {"Authorization": f"token {GITHUB_TOKEN}", "Accept": "application/vnd.github.v3+json"}
    # Check if repo exists
    r = requests.get(f"https://api.github.com/repos/{GITHUB_USERNAME}/{repo_name}", headers=headers)
    if r.status_code == 404:
        # Create repo
        data = {"name": repo_name, "private": False, "auto_init": True}
        r = requests.post(f"https://api.github.com/user/repos", headers=headers, json=data)
        if r.status_code not in (201, 202):
            raise Exception(f"Failed to create GitHub repo: {r.text}")
    elif r.status_code != 200:
        raise Exception(f"Failed to check GitHub repo: {r.text}")

def push_to_github(local_path, repo_name, branch_name, commit_message):
    # Remove .git if exists (for clean init)
    git_dir = os.path.join(local_path, ".git")
    if os.path.exists(git_dir):
        shutil.rmtree(git_dir)
    repo = Repo.init(local_path)
    repo.index.add([f for f in os.listdir(local_path) if os.path.isfile(os.path.join(local_path, f))])
    repo.index.commit(commit_message)
    push_url = GITHUB_PUSH_URL_TEMPLATE.format(username=GITHUB_USERNAME, token=GITHUB_TOKEN, repo=repo_name)
    if branch_name not in repo.heads:
        repo.git.checkout('-b', branch_name)
    else:
        repo.git.checkout(branch_name)
    if 'origin' not in [remote.name for remote in repo.remotes]:
        repo.create_remote('origin', push_url)
    else:
        repo.delete_remote('origin')
        repo.create_remote('origin', push_url)
    repo.git.push('--set-upstream', 'origin', branch_name, force=True)

def push_to_github_with_permission(agent_name, file_path, repo_name, branch_name, commit_message):
    """Push to GitHub with user permission."""
    try:
        # Ensure GitHub repo exists
        ensure_github_repo(repo_name)
        
        # Create temp directory for this push
        temp_git_dir = f"./output/{repo_name}_git_{branch_name}"
        if os.path.exists(temp_git_dir):
            shutil.rmtree(temp_git_dir)
        os.makedirs(temp_git_dir, exist_ok=True)
        
        # Copy file to temp directory
        if os.path.exists(file_path):
            shutil.copy(file_path, os.path.join(temp_git_dir, os.path.basename(file_path)))
        
        # Push to GitHub
        push_to_github(temp_git_dir, repo_name, branch_name, commit_message)
        
        # Update session state to mark as pushed
        if agent_name not in st.session_state.github_permissions:
            st.session_state.github_permissions[agent_name] = {}
        st.session_state.github_permissions[agent_name]['pushed'] = True
        st.session_state.github_permissions[agent_name]['repo_url'] = f"https://github.com/{GITHUB_USERNAME}/{repo_name}"
        st.session_state.github_permissions[agent_name]['branch'] = branch_name
        
        return True, f"Successfully pushed to GitHub: {repo_name}/{branch_name}"
    except Exception as e:
        return False, f"Failed to push to GitHub: {str(e)}"

def execute_agent_with_permission(agent_name, agent_function, *args, **kwargs):
    """Execute an agent with user permission."""
    try:
        # Execute the agent function
        success, result = agent_function(*args, **kwargs)
        
        # Update session state to mark as executed
        if agent_name not in st.session_state.agent_permissions:
            st.session_state.agent_permissions[agent_name] = {}
        st.session_state.agent_permissions[agent_name]['executed'] = True
        st.session_state.agent_permissions[agent_name]['success'] = success
        
        return success, result
    except Exception as e:
        # Update session state to mark as failed
        if agent_name not in st.session_state.agent_permissions:
            st.session_state.agent_permissions[agent_name] = {}
        st.session_state.agent_permissions[agent_name]['executed'] = True
        st.session_state.agent_permissions[agent_name]['success'] = False
        st.session_state.agent_permissions[agent_name]['error'] = str(e)
        
        return False, f"Failed to execute {agent_name}: {str(e)}"

def main():
    """Main application function."""
    
    # Configure Streamlit page
    st.set_page_config(
        page_title="Radiant.ai : SDLC Agents",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    initialize_session_state()
    
    # Enhanced Header with beautiful design
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 class="main-header">🤖 MOP Agent Workflow UI</h1>
        <p style="font-size: 1.2rem; color: #6b7280; font-weight: 400; margin-top: -1rem;">
            Transform your Method of Procedures into intelligent workflows with AI-powered agents
        </p>
        <div style="width: 100px; height: 4px; background: linear-gradient(90deg, #667eea, #764ba2); margin: 1rem auto; border-radius: 2px;"></div>
    </div>
    """, unsafe_allow_html=True)
    
    # Enhanced sidebar with beautiful styling
    with st.sidebar:
        # Add image above MOP selection with transparent background
        st.markdown('<div style="background:transparent;padding:0;margin-bottom:1rem;">', unsafe_allow_html=True)
        # st.image("C:/Users/<USER>/Downloads/InputFiles/download.jpg", use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
        # Beautiful sidebar header
        st.markdown("""
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="font-size: 3rem; margin-bottom: 0.5rem;">📁</div>
            <h2 style="color: #374151; font-weight: 700; margin: 0;">MOP Selection</h2>
            <p style="color: #6b7280; font-size: 0.9rem; margin-top: 0.5rem;">Choose your Method of Procedure</p>
            <div style="width: 50px; height: 3px; background: linear-gradient(90deg, #667eea, #764ba2); margin: 1rem auto; border-radius: 2px;"></div>
        </div>
        """, unsafe_allow_html=True)

        # --- Enhanced MOP source selection ---
        st.markdown("""
        <div style="margin-bottom: 1rem;">
            <h4 style="color: #374151; font-weight: 600;">📋 Source Options</h4>
        </div>
        """, unsafe_allow_html=True)

        mop_source = st.radio(
            "Select MOP Source",
            ["📤 Upload File", "🗂️ Upload Multiple Files", "🗂️ Upload Folder (ZIP)", "📚 Predefined MOPs", "✏️ Text Input"],
            label_visibility="collapsed",
            key="mop_source_radio",
            help="Select how you want to provide your MOP content"
        )
        
        if mop_source == "📤 Upload File":
            st.markdown("""
            <div style="margin: 1rem 0;">
                <h5 style="color: #374151; font-weight: 600;">📤 Upload Your MOP File</h5>
                <p style="color: #6b7280; font-size: 0.9rem;">Support for any file type</p>
            </div>
            """, unsafe_allow_html=True)
            uploaded_file = st.file_uploader(
                "Upload File",
                type=None,  # Allow all file types
                label_visibility="collapsed",
                help="Upload any file containing your MOP content"
            )
            if uploaded_file is not None:
                try:
                    content = uploaded_file.read().decode('utf-8')
                except Exception:
                    content = uploaded_file.read()  # fallback for binary
                st.session_state.mop_content = content
                st.session_state.mop_source = f"Uploaded: {uploaded_file.name}"
                st.success(f"✅ Loaded {uploaded_file.name}")

        elif mop_source == "🗂️ Upload Multiple Files":
            st.markdown("""
            <div style=\"margin: 1rem 0;\">
                <h5 style=\"color: #374151; font-weight: 600;\">🗂️ Upload Multiple Files</h5>
                <p style=\"color: #6b7280; font-size: 0.9rem;\">Select and upload multiple files at once (any type). All files will be processed as a folder.</p>
            </div>
            """, unsafe_allow_html=True)
            uploaded_files = st.file_uploader(
                "Upload Multiple Files",
                type=None,
                label_visibility="collapsed",
                accept_multiple_files=True,
                help="Upload multiple files to process as a folder."
            )
            if uploaded_files:
                folder_content = {}
                for f in uploaded_files:
                    try:
                        content = f.read().decode('utf-8')
                        import re
                        content = re.sub(r'<[^>]*>', '', content)
                        folder_content[f.name] = content
                    except Exception:
                        folder_content[f.name] = "[Binary file - content not readable]"
                st.session_state.mop_content = folder_content
                st.session_state.mop_source = f"Uploaded multiple files: {', '.join([f.name for f in uploaded_files])}"
                st.success(f"✅ Loaded {len(uploaded_files)} files")

        elif mop_source == "🗂️ Upload Folder (ZIP)":
            st.markdown("""
            <div style=\"margin: 1rem 0;\">
                <h5 style=\"color: #374151; font-weight: 600;\">🗂️ Upload Folder as ZIP</h5>
                <p style=\"color: #6b7280; font-size: 0.9rem;\">Upload a ZIP file containing multiple files as a folder. All files will be processed.</p>
            </div>
            """, unsafe_allow_html=True)
            uploaded_zip = st.file_uploader(
                "Upload ZIP File",
                type=["zip"],
                label_visibility="collapsed",
                help="Upload a ZIP file containing multiple files as a folder. All files will be processed."
            )
            if uploaded_zip is not None:
                try:
                    # Handle uploaded ZIP file by extracting its contents - WINDOWS COMPATIBLE
                    # Use absolute path to avoid Windows path issues
                    base_dir = os.path.abspath("uploads")
                    extraction_path = os.path.join(base_dir, "extracted_zip")
                    
                    # Clean up previous extraction
                    if os.path.exists(extraction_path):
                        shutil.rmtree(extraction_path)
                    
                    os.makedirs(extraction_path, exist_ok=True)
                    st.info(f"🔧 DEBUG: Created extraction path: {extraction_path}")

                    # Save the uploaded ZIP file temporarily
                    temp_zip_path = os.path.join(extraction_path, uploaded_zip.name)
                    with open(temp_zip_path, "wb") as temp_zip:
                        temp_zip.write(uploaded_zip.getbuffer())
                    st.info(f"🔧 DEBUG: Saved ZIP to: {temp_zip_path}")

                    # Extract the ZIP file safely
                    with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                        # List contents for debugging
                        zip_contents = zip_ref.namelist()
                        st.info(f"🔧 DEBUG: ZIP contains {len(zip_contents)} files")
                        st.info(f"🔧 DEBUG: First few files: {zip_contents[:5]}")
                        
                        # Safe extraction with path validation
                        extracted_count = 0
                        skipped_count = 0
                        for member in zip_ref.infolist():
                            try:
                                # Validate file path to prevent directory traversal
                                if os.path.isabs(member.filename) or ".." in member.filename:
                                    st.warning(f"⚠️ Skipping potentially unsafe file: {member.filename}")
                                    skipped_count += 1
                                    continue

                                # Check for invalid characters in filename (Windows compatibility)
                                invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
                                if any(char in member.filename for char in invalid_chars):
                                    st.warning(f"⚠️ Skipping file with invalid characters: {member.filename}")
                                    skipped_count += 1
                                    continue

                                # Check for newlines or control characters in filename
                                if '\n' in member.filename or '\r' in member.filename or any(ord(c) < 32 for c in member.filename):
                                    st.warning(f"⚠️ Skipping file with invalid control characters: {repr(member.filename)}")
                                    skipped_count += 1
                                    continue

                                # Check path length (Windows has 260 char limit)
                                full_path = os.path.join(extraction_path, member.filename)
                                if len(full_path) > 250:  # Leave some buffer
                                    st.warning(f"⚠️ Skipping file with path too long ({len(full_path)} chars): {member.filename[:50]}...")
                                    skipped_count += 1
                                    continue

                                # Extract the file
                                zip_ref.extract(member, extraction_path)
                                extracted_count += 1

                            except Exception as extract_error:
                                st.warning(f"⚠️ Failed to extract file '{member.filename}': {str(extract_error)}")
                                skipped_count += 1
                                continue

                        st.info(f"🔧 DEBUG: Extracted {extracted_count} files, skipped {skipped_count} files")

                    # Remove the temporary ZIP file
                    os.remove(temp_zip_path)
                    
                    # Verify extraction worked
                    extracted_files = []
                    for root, _, files in os.walk(extraction_path):
                        for file in files:
                            extracted_files.append(os.path.join(root, file))

                    if len(extracted_files) == 0:
                        st.error("❌ No files were successfully extracted from the ZIP file. The ZIP may be corrupted or contain only invalid files.")
                        st.info("💡 Try uploading a different ZIP file or check if the ZIP file is valid.")
                    else:
                        st.success(f"ZIP file extracted successfully to {extraction_path}")
                        st.info(f"🔧 DEBUG: Extracted {len(extracted_files)} files")

                        st.session_state.mop_content = extraction_path
                        st.session_state.mop_source = f"Uploaded folder: {uploaded_zip.name}"
                except Exception as e:
                    st.error(f"Failed to process ZIP file: {str(e)}")
                    st.error(f"🔧 DEBUG: Full error traceback: {traceback.format_exc()}")

        elif mop_source == "📚 Predefined MOPs":
            st.markdown("""
            <div style="margin: 1rem 0;">
                <h5 style="color: #374151; font-weight: 600;">📚 Choose Template</h5>
                <p style="color: #6b7280; font-size: 0.9rem;">Select from pre-built MOP templates</p>
            </div>
            """, unsafe_allow_html=True)

            predefined_mops = load_predefined_mops()
            if predefined_mops:
                selected_mop = st.selectbox(
                    "",
                    list(predefined_mops.keys()),
                    help="Choose a predefined MOP template"
                )
                if selected_mop:
                    st.session_state.mop_content = predefined_mops[selected_mop]
                    st.session_state.mop_source = f"Predefined: {selected_mop}"
                    st.success(f"✅ Loaded {selected_mop}")
            else:
                st.warning("No predefined MOPs available")

        elif mop_source == "✏️ Text Input":
            st.markdown("""
            <div style="margin: 1rem 0;">
                <h5 style="color: #374151; font-weight: 600;">✏️ Direct Input</h5>
                <p style="color: #6b7280; font-size: 0.9rem;">Paste or type your MOP content</p>
            </div>
            """, unsafe_allow_html=True)

            mop_text = st.text_area(
                "",
                height=200,
                placeholder="Paste your MOP content here...",
                help="Enter your Method of Procedure content directly"
            )
            if mop_text:
                st.session_state.mop_content = mop_text
                st.session_state.mop_source = "Text Input"
                st.success("✅ MOP content entered")
        
        # Display current MOP info
        if st.session_state.mop_content:
            st.info(f"📋 Current MOP: {st.session_state.mop_source}")
            with st.expander("Preview MOP Content"):
                st.text(st.session_state.mop_content[:500] + "..." if len(st.session_state.mop_content) > 500 else st.session_state.mop_content)

        # Enhanced Jira Integration Status
        st.markdown("""
        <div style="margin: 2rem 0 1rem 0;">
            <div style="text-align: center;">
                <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">🎫</div>
                <h3 style="color: #374151; font-weight: 700; margin: 0;">Jira Integration</h3>
                <div style="width: 40px; height: 2px; background: linear-gradient(90deg, #667eea, #764ba2); margin: 0.5rem auto; border-radius: 1px;"></div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        jira_configured, jira_message = check_jira_configuration()

        if jira_configured:
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
                        padding: 1rem; border-radius: 12px; margin: 1rem 0;
                        border-left: 4px solid #10b981;">
                <p style="margin: 0; color: #065f46; font-weight: 600;">✅ {jira_message}</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("""
            <div style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                        padding: 1rem; border-radius: 12px; margin: 0.5rem 0;">
                <p style="margin: 0; color: #1e40af; font-weight: 500;">   PRD Agent: Creates summary dashboard + epics + user stories</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("""
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                        padding: 1rem; border-radius: 12px; margin: 0.5rem 0;">
                <p style="margin: 0; color: #92400e; font-weight: 500;">🐛 QA Agent: Creates bug tickets for test failures</p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
                        padding: 1rem; border-radius: 12px; margin: 1rem 0;
                        border-left: 4px solid #ef4444;">
                <p style="margin: 0; color: #991b1b; font-weight: 600;">❌ {jira_message}</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("""
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                        padding: 1rem; border-radius: 12px; margin: 0.5rem 0;">
                <p style="margin: 0; color: #92400e; font-weight: 500;">⚠️ Jira features will be disabled</p>
            </div>
            """, unsafe_allow_html=True)

    # Main content area
    col1, col2 = st.columns([1, 1])

    with col1:
        # --- Agent Selection Header ---
        st.header("🤖 Agent Selection")

        # --- Allow agent selection and dynamic input even if no MOP is selected ---
        # Show a warning if no MOP is selected, but do NOT return/disable the UI
        if not st.session_state.mop_content:
            st.warning("⚠️ No MOP selected. You can still use Dynamic User Input to send any code or prompt (Python, Java, JavaScript, React, etc.) to the selected agents.")

        agent_descriptions = get_agent_descriptions()

        # Enhanced agent selection with beautiful cards
        st.markdown("""
        <div style="margin-bottom: 1.5rem;">
            <h3 style="color: #374151; font-weight: 600; margin-bottom: 1rem;">🎯 Select AI Agents</h3>
            <p style="color: #6b7280; margin-bottom: 1.5rem;">Choose the intelligent agents to process your MOP content</p>
        </div>
        """, unsafe_allow_html=True)

        selected_agents = set()

        # Create agent cards in a grid layout
        for i, (agent_key, agent_info) in enumerate(agent_descriptions.items()):
            # Create beautiful agent card
            is_selected = agent_key in st.session_state.selected_agents
            card_style = """
            <div class="agent-card" style="margin-bottom: 1rem; cursor: pointer;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div style="font-size: 2rem;">{icon}</div>
                    <div style="flex: 1;">
                        <h4 style="margin: 0; color: #374151; font-weight: 600;">{name}</h4>
                        <p style="margin: 0.5rem 0 0 0; color: #6b7280; font-size: 0.9rem;">{description}</p>
                    </div>
                </div>
            </div>
            """.format(
                icon=agent_info['icon'],
                name=agent_info['name'],
                description=agent_info['description']
            )

            st.markdown(card_style, unsafe_allow_html=True)

            # Checkbox for selection
            if st.checkbox(
                f"Enable {agent_info['name']}",
                key=f"agent_{agent_key}",
                value=is_selected,
                help=f"Click to enable/disable {agent_info['name']}"
            ):
                selected_agents.add(agent_key)

        st.session_state.selected_agents = selected_agents

        # --- Enhanced Dynamic User Input Section ---
        st.markdown("""
        <div style="margin: 1.5rem 0 1rem 0;">
            <h4 style="color: #374151; font-weight: 600;">📝 Enhanced Dynamic User Input</h4>
            <p style="color: #6b7280; font-size: 0.95rem;">Enter custom prompts and upload validation files (.md, test cases) for comprehensive code review.</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Language selection for code generation
        code_language = st.selectbox(
            "Select Code Language (for Code Generator agent)",
            ["Python", "Java", "JavaScript", "React", "Angular","Other"],
            key="dynamic_code_language",
            help="Choose the language you want the Code Generator agent to use."
        )
        
        # Multi-model validation option
        use_multi_model = st.checkbox(
            "🤖 Use Multi-Model Validation (Claude Sonnet + Gemini)",
            key="use_multi_model_validation",
            help="Enable multiple AI models for more reliable validation with reduced hallucination"
        )
        
        # Validation files upload
        st.markdown("""
        <div style="margin: 1rem 0 0.5rem 0;">
            <h5 style="color: #374151; font-weight: 600;">📁 Upload Validation Files</h5>
            <p style="color: #6b7280; font-size: 0.9rem;">Upload .md files (standards, guidelines) or test case files for custom validation criteria.</p>
        </div>
        """, unsafe_allow_html=True)
        
        validation_files_uploaded = st.file_uploader(
            "Validation Files (.md, .txt, .py, .test, .spec)",
            type=['md', 'txt', 'py', 'test', 'spec', 'json'],
            accept_multiple_files=True,
            key="validation_files_uploader",
            help="Upload standards documents, test cases, or guidelines for validation"
        )
        
        # Process uploaded validation files
        validation_files = {}
        if validation_files_uploaded:
            for uploaded_file in validation_files_uploaded:
                try:
                    content = uploaded_file.read().decode('utf-8')
                    validation_files[uploaded_file.name] = content
                    st.success(f"✅ Loaded validation file: {uploaded_file.name}")
                except Exception as e:
                    st.error(f"❌ Failed to load {uploaded_file.name}: {str(e)}")
        
        # Store validation files in session state
        if 'validation_files' not in st.session_state:
            st.session_state.validation_files = {}
        st.session_state.validation_files = validation_files
        st.session_state.use_multi_model = use_multi_model
        
        # Custom prompt input
        dynamic_user_input = st.text_area(
            "Custom Validation Instructions",
            key="dynamic_user_input",
            height=120,
            placeholder="Enter custom validation requirements, company standards, or specific focus areas...\n\nExamples:\n- Focus on API security and rate limiting\n- Validate against our company's Python coding standards\n- Check for proper error handling in microservices\n- Ensure compliance with GDPR data handling"
        )
        # Show the current dynamic user input below the input area
        if dynamic_user_input:
            st.info(f"Current Dynamic Input: {dynamic_user_input}")

        # Determine code input for review/improve: dynamic input > uploaded file > MOP
        code_input = dynamic_user_input.strip() if dynamic_user_input.strip() else (st.session_state.mop_content.strip() if st.session_state.mop_source and st.session_state.mop_source.startswith("Uploaded:") else "")

        if st.button("SUBMIT", disabled=not (selected_agents and (dynamic_user_input or (st.session_state.mop_source and st.session_state.mop_source.startswith('Uploaded:'))))):
            st.session_state.workflow_running = True
            st.session_state.workflow_results = {}
            st.session_state.pending_agent_executions = {}
            st.session_state.agent_permissions = {}
            
            try:
                # Store agent execution information for permission requests
                for agent_key in selected_agents:
                    agent_descriptions = get_agent_descriptions()
                    agent_name = agent_descriptions[agent_key]['name']
                    
                    if agent_key == "coder":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_code,
                            'args': [dynamic_user_input, code_language],
                            'description': 'Generate code in the selected language'
                        }
                    elif agent_key == "reviewer":
                        # Enhanced validation with industry standards, validation files, and multi-model support
                        validation_files = st.session_state.get('validation_files', {})
                        use_multi_model = st.session_state.get('use_multi_model', False)
                        
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': review_code,
                            'args': [code_input, prd_content, dynamic_user_input, validation_files, use_multi_model],
                            'description': f'Enhanced code review with industry standards{" + multi-model validation" if use_multi_model else ""}{" + validation files" if validation_files else ""}'
                        }
                    elif agent_key == "improver":
                        # Enhanced validation with industry standards, validation files, and multi-model support
                        validation_files = st.session_state.get('validation_files', {})
                        use_multi_model = st.session_state.get('use_multi_model', False)
                        
                        # Check if we have a PRD file in the uploaded content
                        prd_content = None
                        if isinstance(st.session_state.mop_content, dict):
                            for filename, content in st.session_state.mop_content.items():
                                if filename.lower().endswith('.md') and 'prd' in filename.lower():
                                    prd_content = content
                                    break
                        
                        if isinstance(st.session_state.mop_content, dict) and dynamic_user_input.strip():
                            # Multi-file review with all enhancements
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': review_code,
                                'args': [(st.session_state.mop_content, dynamic_user_input, st.session_state.mop_content), prd_content, dynamic_user_input, validation_files, use_multi_model],
                                'description': f'Enhanced multi-file review with industry standards{" + multi-model validation" if use_multi_model else ""}{" + validation files" if validation_files else ""}'
                            }
                        elif code_input:
                            # Single file review with enhancements
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': review_code,
                                'args': [code_input, prd_content, dynamic_user_input, validation_files, use_multi_model],
                                'description': f'Enhanced code review with industry standards{" + multi-model validation" if use_multi_model else ""}{" + validation files" if validation_files else ""}'
                            }
                        else:
                            # Fallback with enhancements
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': review_code,
                                'args': [code_input, prd_content, dynamic_user_input, validation_files, use_multi_model],
                                'description': f'Enhanced code review with industry standards{" + multi-model validation" if use_multi_model else ""} (⚠️ No code input detected - will use uploaded file or MOP content)'
                            }
                    elif agent_key == "improver":
                        # Always show permission for Code Improver, even if no code input
                        if code_input:
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': improve_code,
                                'args': [code_input],
                                'description': 'Improve and optimize the provided code'
                            }
                        else:
                            # Show permission even when no code input, but with a warning
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': improve_code,
                                'args': [code_input],
                                'description': 'Improve and optimize the provided code (⚠️ No code input detected - will use uploaded file or MOP content)'
                            }
                    elif agent_key == "integrator":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_api_integration,
                            'args': [dynamic_user_input],
                            'description': 'Generate API integration code and examples'
                        }
                    elif agent_key == "documenter":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_documentation,
                            'args': [dynamic_user_input],
                            'description': 'Generate comprehensive documentation'
                        }
                    elif agent_key == "prd_agent":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_prd,
                            'args': [dynamic_user_input],
                            'description': 'Generate Product Requirements Document with epics and user stories'
                        }
                    elif agent_key == "qa_agent":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_qa_tests,
                            'args': [dynamic_user_input],
                            'description': 'Generate QA tests and perform code analysis'
                        }
                
                st.session_state.workflow_running = False
                st.rerun()
            except Exception as e:
                st.session_state.workflow_running = False
                st.error(f"❌ Failed to process dynamic input: {str(e)}")

        # Run workflow button
        if st.button("🚀 Run Workflow", disabled=st.session_state.workflow_running or not selected_agents):
            run_workflow()

    with col2:
        # Enhanced results header
        st.markdown("""
        <div style="margin-bottom: 1.5rem;">
            <h3 style="color: #374151; font-weight: 600; margin-bottom: 0.5rem;">📊 Results & Progress</h3>
            <p style="color: #6b7280; margin-bottom: 1rem;">Monitor your workflow execution and download results</p>
        </div>
        """, unsafe_allow_html=True)

        # Enhanced workflow controls
        col_clear, col_status = st.columns([1, 2])

        with col_clear:
            if st.button("🗑️ Clear Results", disabled=st.session_state.workflow_running):
                st.session_state.workflow_results = {}
                st.session_state.pending_github_pushes = {}
                st.session_state.github_permissions = {}
                st.session_state.pending_agent_executions = {}
                st.session_state.agent_permissions = {}
                st.rerun()

        if st.session_state.workflow_running:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                        padding: 1rem; border-radius: 12px; margin: 1rem 0;
                        border-left: 4px solid #f59e0b;">
                <p class="status-running">🔄 Workflow is running...</p>
            </div>
            """, unsafe_allow_html=True)
            st.progress(0.5)

        # Display results with beautiful styling
        if st.session_state.workflow_results:
            # Enhanced summary statistics with beautiful cards
            total_agents = len(st.session_state.workflow_results)
            successful_agents = sum(1 for result in st.session_state.workflow_results.values() if result.get('status') == 'success')
            failed_agents = total_agents - successful_agents

            # Beautiful metrics cards
            st.markdown("""
            <div style="margin: 1.5rem 0;">
                <h4 style="color: #374151; font-weight: 600; margin-bottom: 1rem;">📈 Execution Summary</h4>
            </div>
            """, unsafe_allow_html=True)

            col_stats1, col_stats2, col_stats3 = st.columns(3)

            with col_stats1:
                st.markdown(f"""
                <div class="metric-card">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                    <div style="font-size: 2rem; font-weight: 700; color: #374151;">{total_agents}</div>
                    <div style="color: #6b7280; font-weight: 500;">Total Agents</div>
                </div>
                """, unsafe_allow_html=True)

            with col_stats2:
                st.markdown(f"""
                <div class="metric-card">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">✅</div>
                    <div style="font-size: 2rem; font-weight: 700; color: #10b981;">{successful_agents}</div>
                    <div style="color: #6b7280; font-weight: 500;">Successful</div>
                </div>
                """, unsafe_allow_html=True)

            with col_stats3:
                st.markdown(f"""
                <div class="metric-card">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">❌</div>
                    <div style="font-size: 2rem; font-weight: 700; color: #ef4444;">{failed_agents}</div>
                    <div style="color: #6b7280; font-weight: 500;">Failed</div>
                </div>
                """, unsafe_allow_html=True)

            st.markdown("<br>", unsafe_allow_html=True)

            # Results display
            for agent_name, result in st.session_state.workflow_results.items():
                with st.expander(f"📋 {agent_name} Results", expanded=True):
                    if result.get('status') == 'success':
                        st.markdown('<p class="status-success">✅ Completed Successfully</p>', unsafe_allow_html=True)

                        # Output display
                        output_text = result.get('output', '')

                        def sanitize_llm_output(text):
                            # Check if text is a string, if not return it unchanged
                            if not isinstance(text, str):
                                return text
                            # Remove incomplete code block markers and leading/trailing backticks
                            text = text.strip()
                            if text.startswith('```') and not text.endswith('```'):
                                text = text.lstrip('`').rstrip('`')
                            # Remove lone backticks at start/end
                            if text.startswith('`') and text.endswith('`') and text.count('`') == 2:
                                text = text[1:-1]
                            return text

                        output_text = sanitize_llm_output(output_text)

                        if agent_name == 'Code Generator':
                            output = result.get('output', '')
                            if isinstance(output, dict):
                                for filename, content in output.items():
                                    st.subheader(f"{filename}")
                                    display_content = to_display_string(content)
                                    st.code(display_content, language=detect_language(filename))
                                    st.download_button(
                                        f"📥 Download {filename}",
                                        display_content,
                                        file_name=filename,
                                        key=f"download_{agent_name}_{filename}",
                                        help=f"Download {filename}"
                                    )
                            else:
                                st.subheader("Generated Code:")
                                st.code(output, language='python')
                        else:
                            # Try rendering as markdown/code, fallback to plain text if error
                            rendered = False
                            if agent_name == 'Code Validator':
                                try:
                                    if output_text.strip().startswith('|') and '\n|' in output_text:
                                        st.markdown(output_text, unsafe_allow_html=False)
                                        rendered = True
                                    elif output_text.strip().startswith('```') or output_text.strip().startswith('#'):
                                        st.code(output_text)
                                        rendered = True
                                except Exception:
                                    rendered = False
                            if not rendered:
                                st.text_area(
                                    f"{agent_name} Output",
                                    to_display_string(output_text),
                                    height=200,
                                    key=f"result_{agent_name}",
                                    help="Click to select all text, then Ctrl+C to copy"
                                )

                        # Download button for outputs
                        if output_text:
                            file_extension = "py" if agent_name == 'Code Generator' else "txt"
                            file_name = f"{agent_name.lower().replace(' ', '_')}_output.{file_extension}"

                            st.download_button(
                                f"📥 Download {agent_name} Output",
                                to_display_string(output_text),
                                file_name=file_name,
                                key=f"download_{agent_name}",
                                help=f"Download the {agent_name} output"
                            )

                        # Show file info
                        if result.get('file_path') and os.path.exists(result['file_path']):
                            file_size = len(output_text.encode('utf-8'))
                            st.caption(f"📄 File saved: {result['file_path']} ({file_size} bytes)")
                            
                            # --- Step-by-step GitHub Permission Dialog ---
                            # Improved: Show all pending agents at once (no queue/index)
                            pending_agents = [agent for agent in st.session_state.pending_github_pushes.keys() if agent not in st.session_state.github_permissions]
                            for pending_agent in pending_agents:
                                push_info = st.session_state.pending_github_pushes[pending_agent]
                                accept_key = f"accept_{pending_agent}_{push_info['branch_name']}"
                                reject_key = f"reject_{pending_agent}_{push_info['branch_name']}"
                                st.markdown(f"""
                                <div style="margin: 1rem 0; padding: 1rem; background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); 
                                            border-radius: 12px; border-left: 4px solid #3b82f6;">
                                    <h5 style="margin: 0 0 0.5rem 0; color: #1e40af; font-weight: 600;">🚀 GitHub Push Permission</h5>
                                    <p style="margin: 0; color: #1e40af; font-size: 0.9rem;">
                                        Push these files to GitHub repository: <strong>{push_info['repo_name']}/{push_info['branch_name']}</strong>
                                    </p>
                                </div>
                                """, unsafe_allow_html=True)
                                if 'file_paths' in push_info:
                                    for fp in push_info['file_paths']:
                                        st.caption(f"📄 {os.path.basename(fp)}")
                                else:
                                    st.caption(f"📄 {os.path.basename(push_info['file_path'])}")
                                col_accept, col_reject = st.columns(2)
                                accept_clicked = col_accept.button("✅ Accept Push", key=accept_key, help=f"Push {pending_agent} results to GitHub")
                                reject_clicked = col_reject.button("❌ Reject Push", key=reject_key, help=f"Skip pushing {pending_agent} results to GitHub")
                                if accept_clicked:
                                    # Multi-file push logic
                                    def multi_file_push(file_paths, repo_name, branch_name, commit_message):
                                        ensure_github_repo(repo_name)
                                        temp_git_dir = f"./output/{repo_name}_git_{branch_name}"
                                        if os.path.exists(temp_git_dir):
                                            shutil.rmtree(temp_git_dir)
                                        os.makedirs(temp_git_dir, exist_ok=True)
                                        # Always include prd.md if it exists
                                        prd_path = os.path.join("./output", "prd.md")
                                        files_to_push = list(file_paths)
                                        if os.path.exists(prd_path) and prd_path not in files_to_push:
                                            files_to_push.append(prd_path)
                                        for fp in files_to_push:
                                            shutil.copy(fp, os.path.join(temp_git_dir, os.path.basename(fp)))
                                        push_to_github(temp_git_dir, repo_name, branch_name, commit_message)
                                    try:
                                        if 'file_paths' in push_info:
                                            multi_file_push(push_info['file_paths'], push_info['repo_name'], push_info['branch_name'], push_info['commit_message'])
                                        else:
                                            # Always include prd.md if it exists
                                            prd_path = os.path.join("./output", "prd.md")
                                            file_paths = [push_info['file_path']]
                                            if os.path.exists(prd_path) and prd_path not in file_paths:
                                                file_paths.append(prd_path)
                                            multi_file_push(file_paths, push_info['repo_name'], push_info['branch_name'], push_info['commit_message'])
                                        st.success(f"✅ Successfully pushed to GitHub: {push_info['repo_name']}/{push_info['branch_name']}")
                                        st.session_state.github_permissions[pending_agent] = {'pushed': True, 'rejected': False, 'repo_url': f"https://github.com/{GITHUB_USERNAME}/{push_info['repo_name']}", 'branch': push_info['branch_name']}
                                    except Exception as e:
                                        st.error(f"❌ Failed to push: {e}")
                                        st.session_state.github_permissions[pending_agent] = {'pushed': False, 'rejected': True}
                                    st.rerun()
                                if reject_clicked:
                                    st.session_state.github_permissions[pending_agent] = {'pushed': False, 'rejected': True}
                                    st.rerun()
                            # Show status of previous decisions
                            for agent, permission_status in st.session_state.github_permissions.items():
                                if permission_status.get('pushed'):
                                    st.success(f"✅ Pushed to GitHub: {permission_status.get('repo_url', 'Unknown')}")
                                elif permission_status.get('rejected'):
                                    st.info(f"⏭️ GitHub push was rejected for {agent}")

                        # Show Jira integration status for PRD and QA agents
                        if agent_name == 'PRD Generator' and 'jira_status' in result:
                            if result['jira_status']:
                                st.success(f"🎫 Jira Issues Created: {result['jira_message']}")
                                st.info("✅ User stories created with meaningful titles extracted from PRD")
                                st.info("✅ Acceptance criteria from PRD included in user story descriptions")

                                # Show example of what was created
                                with st.expander("📋 View Jira Integration Details"):
                                    st.markdown("""
                                    **What was created in Jira:**
                                    - 📊 **Summary Dashboard**: Overview epic with project statistics and navigation
                                    - 📌 **Functional Epics**: Created with descriptive titles from PRD sections
                                    - 📝 **User Stories**: Created with format `US-XXX: [Meaningful Title]`
                                    - 📋 **Descriptions**: Include original user story + numbered acceptance criteria
                                    - 🔗 **Links**: User stories linked to their respective epics

                                    **Summary Dashboard Epic includes:**
                                    - 🎯 Product overview from PRD
                                    - 📊 Project statistics (epic/story counts)
                                    - 📌 Epic breakdown with story counts
                                    - 🔗 Quick navigation links
                                    - 📅 Creation timestamp

                                    **Example User Story Title:**
                                    `Establish a secure SSH connection to Cisco XR device`

                                    **Example Description:**
                                    ```
                                    **US-001**: As a Network Administrator, I want to establish a secure SSH connection...

                                    **Acceptance Criteria:**
                                    1. The audit process can only be initiated through a secure SSH connection
                                    2. The provided access credentials are successfully used to authenticate
                                    3. The secure connection is maintained throughout the entire audit process
                                    ```

                                    **💡 Tip:** Pin the "📊 PRD Summary Dashboard" epic to your Jira board for easy project overview!
                                    """)
                            else:
                                st.warning(f"⚠️ Jira Integration: {result['jira_message']}")

                        elif agent_name == 'QA & Testing' and 'jira_status' in result:
                            if result.get('has_failures'):
                                if result['jira_status']:
                                    st.error(f"🐛 Issues Found - Jira Bug Created: {result['jira_message']}")
                                else:
                                    st.error(f"🐛 Issues Found - Jira Bug Failed: {result['jira_message']}")
                            else:
                                st.success("✅ No issues detected - All tests passed")

                    elif result.get('status') == 'error':
                        st.markdown('<p class="status-error">❌ Error Occurred</p>', unsafe_allow_html=True)
                        st.error(result.get('error', 'Unknown error'))

                        # Show error details in expandable section
                        if result.get('error_details'):
                            with st.expander("🔍 Error Details"):
                                st.code(result['error_details'], language='text')

        else:
            st.info("👆 Select agents and run the workflow to see results here")

        # Display pending agent executions that require permission
        if st.session_state.pending_agent_executions:
            pending_count = len(st.session_state.pending_agent_executions)
            st.markdown(f"""
            <div style="margin: 2rem 0 1rem 0;">
                <h3 style="color: #374151; font-weight: 600; margin-bottom: 1rem;">🤖 Agent Execution Permissions</h3>
                <p style="color: #6b7280; margin-bottom: 1rem;">Review and approve agent executions before they run ({pending_count} agent{'s' if pending_count != 1 else ''} pending)</p>
            </div>
            """, unsafe_allow_html=True)

            for agent_name, execution_info in st.session_state.pending_agent_executions.items():
                # Check if permission has already been granted or denied
                if agent_name not in st.session_state.agent_permissions:
                    # Get additional info for display
                    agent_key = execution_info['agent_key']
                    input_info = ""
                    
                    # Define agent-specific styling
                    agent_icons = {
                        "coder": "💻",
                        "reviewer": "🔍", 
                        "improver": "✨",
                        "integrator": "🔗",
                        "documenter": "📚",
                        "prd_agent": "📄",
                        "qa_agent": "🧪"
                    }
                    
                    agent_colors = {
                        "coder": "#3b82f6",  # Blue
                        "reviewer": "#10b981",  # Green
                        "improver": "#f59e0b",  # Yellow
                        "integrator": "#8b5cf6",  # Purple
                        "documenter": "#06b6d4",  # Cyan
                        "prd_agent": "#ef4444",  # Red
                        "qa_agent": "#f97316"  # Orange
                    }
                    
                    icon = agent_icons.get(agent_key, "🤖")
                    color = agent_colors.get(agent_key, "#6b7280")
                    
                    if agent_key in ["reviewer", "improver"]:
                        # Show what code will be reviewed/improved
                        code_input = execution_info['args'][0] if execution_info['args'] else ""
                        if code_input:
                            input_preview = code_input[:100] + "..." if len(code_input) > 100 else code_input
                            input_info = f"<br><strong>Input Preview:</strong> <code style='background: #f3f4f6; padding: 2px 4px; border-radius: 4px;'>{input_preview}</code>"
                        else:
                            input_info = "<br><strong>⚠️ No code input detected</strong> - Will use uploaded file or MOP content"
                    elif agent_key == "coder":
                        # Show what will be used for code generation
                        dynamic_input = st.session_state.get('dynamic_user_input', '')
                        if dynamic_input:
                            input_preview = dynamic_input[:100] + "..." if len(dynamic_input) > 100 else dynamic_input
                            input_info = f"<br><strong>Input Preview:</strong> <code style='background: #f3f4f6; padding: 2px 4px; border-radius: 4px;'>{input_preview}</code>"
                        else:
                            input_info = "<br><strong>⚠️ No dynamic input detected</strong> - Will use uploaded file or MOP content"
                    
                    st.markdown(f"""
                    <div style="margin: 1rem 0; padding: 1.5rem; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); 
                                border-radius: 12px; border-left: 4px solid {color};">
                        <h5 style="margin: 0 0 0.5rem 0; color: #92400e; font-weight: 600;">{icon} {agent_name} Execution Permission</h5>
                        <p style="margin: 0; color: #92400e; font-size: 0.9rem;">
                            <strong>Description:</strong> {execution_info['description']}
                        </p>
                        <p style="margin: 0.5rem 0 0 0; color: #92400e; font-size: 0.9rem;">
                            <strong>Function:</strong> {execution_info['function'].__name__}
                        </p>
                        {input_info}
                    </div>
                    """, unsafe_allow_html=True)
                    
                    col_accept, col_reject = st.columns(2)
                    with col_accept:
                        if st.button(f"✅ Execute {agent_name}", key=f"execute_{agent_name}", 
                                   help=f"Execute {agent_name} with the provided parameters"):
                            success, result = execute_agent_with_permission(
                                agent_name,
                                execution_info['function'],
                                *execution_info['args']
                            )
                            
                            if success:
                                # Process the result based on agent type
                                agent_key = execution_info['agent_key']
                                
                                if agent_key == "coder":
                                    # Save generated code with correct extension(s)
                                    os.makedirs("./output", exist_ok=True)
                                    file_paths, files_dict = handle_generated_files(result, output_dir="./output")
                                    # Store GitHub push information for later permission (all files)
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "codegen"
                                    commit_message = "Add generated code from agent"
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_paths': file_paths,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': files_dict,
                                        'file_paths': file_paths,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "reviewer":
                                    # Save review result as a file
                                    os.makedirs("./output", exist_ok=True)
                                    review_file = "./output/review.txt"
                                    with open(review_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "review"
                                    commit_message = "Add code review feedback from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': review_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': review_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "improver":
                                    # Save improved code as a file
                                    os.makedirs("./output", exist_ok=True)
                                    improve_file = "./output/improved_code.py"
                                    with open(improve_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "improve"
                                    commit_message = "Add improved code from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': improve_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': improve_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "integrator":
                                    # Save API integration result as a file
                                    os.makedirs("./output", exist_ok=True)
                                    api_file = "./output/api_integration.txt"
                                    with open(api_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "api_integration"
                                    commit_message = "Add API integration result from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': api_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': api_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "documenter":
                                    # Save documentation as a file
                                    os.makedirs("./output", exist_ok=True)
                                    doc_file = "./output/documentation.md"
                                    with open(doc_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "documentation"
                                    commit_message = "Add documentation from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': doc_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': doc_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "prd_agent":
                                    # Save PRD to file
                                    os.makedirs("./output", exist_ok=True)
                                    prd_file_path = "./output/prd.md"
                                    with open(prd_file_path, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "prd"
                                    commit_message = "Add PRD from agent"
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': prd_file_path,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    # Try to create Jira issues from PRD
                                    jira_success, jira_message = create_jira_issues_from_prd()
                                    
                                    # Append Jira status to output
                                    final_output = result
                                    if jira_success:
                                        final_output += f"\n\n✅ **Jira Integration**: {jira_message}"
                                    else:
                                        final_output += f"\n\n⚠️ **Jira Integration**: {jira_message}"
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': final_output,
                                        'file_path': prd_file_path,
                                        'jira_status': jira_success,
                                        'jira_message': jira_message
                                    }
                                elif agent_key == "qa_agent":
                                    # Save QA results to file
                                    os.makedirs("./output", exist_ok=True)
                                    qa_file_path = "./output/qa_results.txt"
                                    with open(qa_file_path, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "qa"
                                    commit_message = "Add QA results from agent"
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': qa_file_path,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    # Check if there are test failures or issues that need Jira tickets
                                    has_failures = any(keyword in result.lower() for keyword in ['failed', 'error', 'bug', 'issue', 'failure'])
                                    final_output = result
                                    jira_success = False
                                    jira_message = "No issues detected - no Jira ticket needed"
                                    if has_failures:
                                        # Create Jira bug ticket for test failures
                                        jira_success, jira_message = create_jira_bug_ticket(result)
                                        if jira_success:
                                            final_output += f"\n\n🐛 **Jira Bug Ticket**: {jira_message}"
                                        else:
                                            final_output += f"\n\n⚠️ **Jira Bug Ticket Failed**: {jira_message}"
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': final_output,
                                        'file_path': qa_file_path,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name,
                                        'jira_status': jira_success,
                                        'jira_message': jira_message,
                                        'has_failures': has_failures
                                    }
                                
                                # Remove from pending executions
                                del st.session_state.pending_agent_executions[agent_name]
                                st.success(f"✅ {agent_name} executed successfully!")
                            else:
                                st.error(f"❌ {agent_name} execution failed: {result}")
                            
                            st.rerun()
                    
                    with col_reject:
                        if st.button(f"❌ Skip {agent_name}", key=f"skip_{agent_name}",
                                   help=f"Skip executing {agent_name}"):
                            st.session_state.agent_permissions[agent_name] = {'executed': False, 'skipped': True}
                            # Remove from pending executions
                            del st.session_state.pending_agent_executions[agent_name]
                            st.info(f"⏭️ Skipped executing {agent_name}")
                            st.rerun()
                else:
                    # Show status of previous decision
                    permission_status = st.session_state.agent_permissions[agent_name]
                    if permission_status.get('executed') and permission_status.get('success'):
                        st.success(f"✅ {agent_name} executed successfully")
                    elif permission_status.get('executed') and not permission_status.get('success'):
                        st.error(f"❌ {agent_name} execution failed: {permission_status.get('error', 'Unknown error')}")
                    elif permission_status.get('skipped'):
                        st.info(f"⏭️ {agent_name} execution was skipped")

def run_workflow():
    """Execute the selected workflow."""
    st.session_state.workflow_running = True
    st.session_state.workflow_results = {}

    try:
        # Execute workflow based on selected agents
        execute_agent_workflow(st.session_state.selected_agents, st.session_state.mop_content)

    except Exception as e:
        st.error(f"❌ Workflow execution failed: {str(e)}")
        st.exception(e)
    finally:
        st.session_state.workflow_running = False
        st.rerun()

def get_file_type(file_path, code_extensions):
    """Determine file type based on extension and path."""
    file_lower = file_path.lower()
    
    # Check each extension category
    for file_type, extensions in code_extensions.items():
        if any(file_lower.endswith(ext) for ext in extensions):
            return file_type
    
    # Special cases for files without extensions
    if any(name in file_lower for name in ['dockerfile', 'makefile', 'jenkinsfile']):
        return 'devops'
    
    # Default fallback
    if '.' in file_path:
        return file_path.split('.')[-1]
    return 'unknown'

class FileImportanceScorer:
    """Assigns importance scores to files for prioritized validation."""
    
    @staticmethod
    def score_file(file_path, file_type, content_preview=""):
        """Score file importance from 1-10 based on type and content."""
        path_lower = file_path.lower()
        
        # Core business logic files (highest priority)
        if any(pattern in path_lower for pattern in ['controller', 'service', 'handler', 'router', 'api']):
            return 10
        if any(pattern in path_lower for pattern in ['model', 'entity', 'schema', 'dto']):
            return 9
        if any(pattern in path_lower for pattern in ['repository', 'dao', 'database']):
            return 9
            
        # Configuration and architecture
        if file_type in ['config', 'properties', 'build']:
            return 8
        if any(pattern in path_lower for pattern in ['config', 'setting', 'application.', 'pom.xml', 'package.json']):
            return 8
            
        # Tests (important for quality assessment)
        if 'test' in path_lower or file_type == 'test':
            return 7
            
        # Utilities and helpers
        if any(pattern in path_lower for pattern in ['util', 'helper', 'common']):
            return 6
            
        # Build and deployment
        if file_type in ['build', 'devops'] or any(pattern in path_lower for pattern in ['docker', 'jenkins', 'ci']):
            return 5
            
        # Documentation
        if file_type == 'docs' or path_lower.endswith('.md'):
            return 3
            
        # Default for code files
        if file_type in ['python', 'java', 'javascript', 'csharp', 'cpp', 'go', 'rust']:
            return 6
            
        return 4

class ContentQuantizer:
    """Applies intelligent compression techniques to fit content within token limits."""
    
    @staticmethod
    def estimate_tokens(content):
        """Estimate token count (rough: 4 chars ≈ 1 token)."""
        return len(content) // 4
    
    @staticmethod
    def extract_key_content(content, file_path, max_chars=None):
        """Extract key content from a file, removing boilerplate."""
        lines = content.split('\n')
        key_lines = []
        
        # Always include: class definitions, function signatures, important comments
        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue
                
            # Keep class/interface definitions
            if any(keyword in stripped for keyword in ['class ', 'interface ', 'enum ', 'struct ']):
                key_lines.append(line)
            # Keep function/method signatures
            elif any(keyword in stripped for keyword in ['def ', 'function ', 'public ', 'private ', 'protected ']):
                key_lines.append(line)
            # Keep important annotations/decorators
            elif stripped.startswith('@') or stripped.startswith('#'):
                key_lines.append(line)
            # Keep imports (but limit them)
            elif any(keyword in stripped for keyword in ['import ', 'from ', 'include ', 'using ']):
                if len([l for l in key_lines if 'import' in l]) < 20:  # Limit imports
                    key_lines.append(line)
            # Keep configuration values
            elif '=' in stripped and not stripped.startswith('//'):
                key_lines.append(line)
                
        extracted = '\n'.join(key_lines)
        
        # If still too long, truncate with summary
        if max_chars and len(extracted) > max_chars:
            truncated = extracted[:max_chars]
            return truncated + f"\n\n// [FILE TRUNCATED - Original: {len(content)} chars, Showing: {len(truncated)} chars]"
            
        return extracted if extracted else content[:max_chars] if max_chars else content

class CodebaseChunker:
    """Intelligent chunking strategies for large codebases."""
    
    def __init__(self, max_tokens_per_chunk=160000):  # 80% of Claude Sonnet's 200k limit
        self.max_tokens_per_chunk = max_tokens_per_chunk
        self.max_chars_per_chunk = max_tokens_per_chunk * 4  # Rough conversion
        
    def chunk_by_priority(self, files_with_metadata):
        """Chunk files by importance priority."""
        # Sort files by importance score (descending)
        sorted_files = sorted(files_with_metadata, key=lambda x: x['importance'], reverse=True)
        
        chunks = []
        current_chunk = {
            'files': [],
            'total_chars': 0,
            'priority_range': [10, 0],
            'file_types': set()
        }
        
        for file_data in sorted_files:
            file_content = file_data['quantized_content'] or file_data['content']
            file_chars = len(file_content)
            
            # If adding this file would exceed limit, finalize current chunk
            if current_chunk['total_chars'] + file_chars > self.max_chars_per_chunk and current_chunk['files']:
                chunks.append(current_chunk)
                current_chunk = {
                    'files': [],
                    'total_chars': 0,
                    'priority_range': [file_data['importance'], file_data['importance']],
                    'file_types': set()
                }
            
            # Add file to current chunk
            current_chunk['files'].append(file_data)
            current_chunk['total_chars'] += file_chars
            current_chunk['priority_range'][1] = min(current_chunk['priority_range'][1], file_data['importance'])
            current_chunk['file_types'].add(file_data['type'])
        
        # Add final chunk
        if current_chunk['files']:
            chunks.append(current_chunk)
            
        return chunks
    
    def prepare_chunk_content(self, chunk, chunk_index, total_chunks):
        """Prepare content string for a chunk."""
        header = f"""
=== CODEBASE CHUNK {chunk_index + 1} of {total_chunks} ===
Priority Range: {chunk_index + 1}-{chunk['priority_range'][0]} to {chunk['priority_range'][1]}
File Types: {', '.join(sorted(chunk['file_types']))}
Files in this chunk: {len(chunk['files'])}

"""
        
        content_parts = [header]
        
        for file_data in chunk['files']:
            file_header = f"\n=== FILE: {file_data['path']} (Priority: {file_data['importance']}) ===\n"
            file_content = file_data['quantized_content'] or file_data['content']
            content_parts.append(file_header + file_content)
        
        return ''.join(content_parts)

def read_codebase_from_zip(extraction_path):
    """Read any codebase from extracted ZIP for validation - supports all programming languages."""
    # Cross-platform path handling
    # Normalize path for cross-platform compatibility
    extraction_path_normalized = normalize_path(extraction_path)
    
    st.info(f"🔧 DEBUG: Checking extraction path: {extraction_path_normalized}")
    st.info(f"🔧 DEBUG: Path exists: {extraction_path_normalized.exists()}")
    
    if not extraction_path_normalized.exists():
        st.error(f"❌ Extraction path does not exist: {extraction_path_normalized}")
        return None, None, [], []

    code_files = []
    all_content = ""
    file_list = []
    
    # Define comprehensive file extensions for different languages and frameworks
    code_extensions = {
        # Programming Languages
        'python': ['.py', '.pyx', '.pyi'],
        'java': ['.java', '.scala', '.kt'],
        'javascript': ['.js', '.jsx', '.ts', '.tsx', '.vue'],
        'csharp': ['.cs', '.vb'],
        'cpp': ['.cpp', '.c', '.cc', '.cxx', '.h', '.hpp'],
        'go': ['.go'],
        'rust': ['.rs'],
        'php': ['.php', '.php3', '.php4', '.php5'],
        'ruby': ['.rb', '.rake'],
        'swift': ['.swift'],
        'kotlin': ['.kt', '.kts'],
        'dart': ['.dart'],
        'r': ['.r', '.R'],
        'matlab': ['.m'],
        'shell': ['.sh', '.bash', '.zsh', '.fish'],
        'powershell': ['.ps1', '.psm1'],
        'sql': ['.sql'],
        
        # Web Technologies
        'html': ['.html', '.htm', '.xhtml'],
        'css': ['.css', '.scss', '.sass', '.less'],
        
        # Configuration & Data
        'config': ['.json', '.yaml', '.yml', '.toml', '.ini', '.conf', '.config'],
        'xml': ['.xml', '.xsd', '.xsl', '.xslt'],
        'properties': ['.properties', '.env'],
        
        # Documentation
        'docs': ['.md', '.rst', '.txt', '.adoc'],
        
        # Build & Package Management
        'build': ['.gradle', '.maven', 'pom.xml', 'build.gradle', 'package.json', 'requirements.txt', 
                 'Cargo.toml', 'go.mod', 'composer.json', 'Gemfile', 'setup.py', 'pyproject.toml'],
        
        # Docker & DevOps
        'devops': ['Dockerfile', '.dockerignore', 'docker-compose.yml', '.github', '.gitlab-ci.yml', 
                  'Jenkinsfile', '.travis.yml', 'azure-pipelines.yml']
    }
    
    # Flatten all extensions
    all_extensions = []
    for ext_list in code_extensions.values():
        all_extensions.extend(ext_list)
    
    # Walk through the extracted directory - CROSS-PLATFORM COMPATIBLE
    # Use normalized Path for cross-platform compatibility
    extraction_path_obj = extraction_path_normalized
    
    # Count subdirectories for debugging
    subdirs = list(extraction_path_obj.rglob("*"))
    st.info(f"🔧 DEBUG: Found {len(subdirs)} items in directory")
    
    # List all files for debugging
    all_files_debug = []
    for root, dirs, files in os.walk(str(extraction_path_normalized)):
        for file in files:
            all_files_debug.append(os.path.join(root, file))
    st.info(f"🔧 DEBUG: All files found: {all_files_debug[:10]}...")  # Show first 10 files
    
    for root, dirs, files in os.walk(str(extraction_path_normalized)):
        for file in files:
            # Use Path for cross-platform path handling
            file_path = Path(root) / file
            relative_path = file_path.relative_to(extraction_path_obj)
            
            # Check if file should be included
            should_include = False
            file_ext = '.' + file.split('.')[-1].lower() if '.' in file else file.lower()
            
            # Include if extension matches or if it's a special build file
            if any(file.endswith(ext) or file.lower() == ext.lower() for ext in all_extensions):
                should_include = True
            elif file.lower() in ['dockerfile', 'makefile', 'readme', 'license', 'changelog']:
                should_include = True
            
            if should_include:
                try:
                    # Use Path for cross-platform file reading
                    st.info(f"🔧 DEBUG: Reading file: {file_path}")
                    with open(str(file_path), 'r', encoding='utf-8') as f:
                        content = f.read()
                    st.info(f"🔧 DEBUG: Successfully read {len(content)} characters from {file}")
                        
                    # Add file header for context
                    file_header = f"\n\n=== FILE: {relative_path} ===\n"
                    all_content += file_header + content
                    
                    # Get file type and importance
                    file_type = get_file_type(str(relative_path), code_extensions)
                    importance = FileImportanceScorer.score_file(str(relative_path), file_type, content[:500])
                    
                    # Apply content quantization for large files
                    quantized_content = None
                    if len(content) > 10000:  # Quantize files larger than 10k chars
                        quantized_content = ContentQuantizer.extract_key_content(content, str(relative_path), max_chars=8000)
                    
                    code_files.append({
                        'path': str(relative_path),
                        'content': content,
                        'quantized_content': quantized_content,
                        'type': file_type,
                        'size': len(content),
                        'importance': importance,
                        'tokens_estimated': ContentQuantizer.estimate_tokens(quantized_content or content)
                    })
                    file_list.append(str(relative_path))
                    
                except Exception as e:
                    # Try binary files or different encodings - CROSS-PLATFORM COMPATIBLE
                    st.warning(f"🔧 DEBUG: UTF-8 failed for {file}, trying latin-1: {e}")
                    try:
                        with open(str(file_path), 'r', encoding='latin-1') as f:
                            content = f.read()
                        st.info(f"🔧 DEBUG: Successfully read with latin-1: {len(content)} characters")
                        file_header = f"\n\n=== FILE: {relative_path} ===\n"
                        all_content += file_header + content
                        
                        # Get file type and importance (fallback encoding)
                        file_type = get_file_type(str(relative_path), code_extensions)
                        importance = FileImportanceScorer.score_file(str(relative_path), file_type, content[:500])
                        
                        # Apply content quantization for large files
                        quantized_content = None
                        if len(content) > 10000:
                            quantized_content = ContentQuantizer.extract_key_content(content, str(relative_path), max_chars=8000)
                        
                        code_files.append({
                            'path': str(relative_path),
                            'content': content,
                            'quantized_content': quantized_content,
                            'type': file_type,
                            'size': len(content),
                            'importance': importance,
                            'tokens_estimated': ContentQuantizer.estimate_tokens(quantized_content or content)
                        })
                        file_list.append(str(relative_path))
                    except Exception as e:
                        st.warning(f"⚠️ Skipping binary/unreadable file: {file_path} - Error: {e}")
                        continue
            else:
                st.info(f"🔧 DEBUG: Skipping file (not included): {file} - Extension: {file_ext}")
    
    # Detect primary programming language and framework
    language_stats = {}
    framework_indicators = {}
    
    for file_info in code_files:
        file_type = file_info['type'].lower()
        
        # Count file types
        if file_type in language_stats:
            language_stats[file_type] += 1
        else:
            language_stats[file_type] = 1
        
        # Detect frameworks based on file content and names
        content = file_info['content'].lower()
        path = file_info['path'].lower()
        
        # Framework detection
        if 'spring' in content or 'springframework' in content or 'pom.xml' in path:
            framework_indicators['Spring Boot'] = framework_indicators.get('Spring Boot', 0) + 1
        elif 'django' in content or 'manage.py' in path or 'settings.py' in path:
            framework_indicators['Django'] = framework_indicators.get('Django', 0) + 1
        elif 'flask' in content or 'app.py' in path:
            framework_indicators['Flask'] = framework_indicators.get('Flask', 0) + 1
        elif 'react' in content or 'package.json' in path and 'react' in content:
            framework_indicators['React'] = framework_indicators.get('React', 0) + 1
        elif 'angular' in content or 'angular.json' in path:
            framework_indicators['Angular'] = framework_indicators.get('Angular', 0) + 1
        elif 'vue' in content or file_type == 'vue':
            framework_indicators['Vue.js'] = framework_indicators.get('Vue.js', 0) + 1
        elif 'express' in content or 'node' in content:
            framework_indicators['Node.js/Express'] = framework_indicators.get('Node.js/Express', 0) + 1
        elif '.net' in content or 'csproj' in path:
            framework_indicators['.NET'] = framework_indicators.get('.NET', 0) + 1
    
    # Determine primary language
    primary_language = max(language_stats.keys(), key=lambda x: language_stats[x]) if language_stats else 'unknown'
    primary_framework = max(framework_indicators.keys(), key=lambda x: framework_indicators[x]) if framework_indicators else None
    
    # Create comprehensive analysis guidelines
    guidelines = f"""
Multi-Language Codebase Analysis Request:
- Total files analyzed: {len(code_files)}
- Primary language detected: {primary_language.upper()}
- Primary framework detected: {primary_framework or 'None detected'}
- File type distribution: {dict(sorted(language_stats.items(), key=lambda x: x[1], reverse=True))}
- Framework indicators: {framework_indicators}

Please perform a comprehensive review of this {primary_language.upper()}{f' {primary_framework}' if primary_framework else ''} codebase focusing on:

LANGUAGE-SPECIFIC ANALYSIS:
1. Code quality and {primary_language.upper()} best practices
2. Language-specific security vulnerabilities and patterns
3. Framework-specific implementation patterns{f' ({primary_framework})' if primary_framework else ''}
4. Architecture and design patterns appropriate for {primary_language.upper()}
5. Testing strategies and coverage for {primary_language.upper()} projects
6. Performance considerations specific to {primary_language.upper()}
7. Dependency management and package structure

GENERAL ANALYSIS:
8. API design and interface implementation
9. Database design and data access patterns
10. Configuration management and environment handling
11. Error handling and logging strategies
12. Documentation quality and completeness
13. Build system and deployment readiness
14. Security best practices and vulnerability assessment

FRAMEWORK-SPECIFIC FOCUS{f' ({primary_framework})' if primary_framework else ''}:
{get_framework_specific_guidelines(primary_framework) if primary_framework else '- Generic framework analysis will be applied'}
"""
    
    # Debug final results
    st.info(f"🔧 DEBUG: Final results - Files processed: {len(code_files)}, Content length: {len(all_content)}")
    
    # Ensure we return valid data even if no files found
    if not code_files:
        st.warning("⚠️ No code files found in ZIP. Creating minimal response.")
        minimal_content = "# No code files found in the uploaded ZIP\n\nThe ZIP file was extracted but no recognizable code files were found."
        minimal_guidelines = "No code files detected for analysis."
        return minimal_content, minimal_guidelines, [], []
    
    # Return both legacy format and new structured data
    return all_content, guidelines, file_list, code_files

def get_framework_specific_guidelines(framework):
    """Get framework-specific validation guidelines."""
    guidelines_map = {
        'Spring Boot': """
- Spring Boot configuration and auto-configuration usage
- Spring Security implementation and authentication flows
- JPA/Hibernate entity design and repository patterns
- REST controller design and HTTP status code usage
- Dependency injection and bean management
- Actuator endpoints and monitoring setup
- Testing with Spring Boot Test framework""",
        
        'Django': """
- Django models and ORM usage patterns
- URL routing and view implementation
- Template system and static file handling
- Django REST Framework API design
- Authentication and authorization mechanisms
- Database migrations and schema management
- Django admin interface customization""",
        
        'Flask': """
- Flask application factory pattern
- Blueprint organization and routing
- SQLAlchemy ORM integration
- Request handling and response formatting
- Flask-Login authentication implementation
- Error handling and custom error pages
- Configuration management with Flask-Config""",
        
        'React': """
- Component architecture and composition patterns
- State management (Redux, Context API, hooks)
- Performance optimization (memo, useMemo, useCallback)
- Routing implementation and navigation
- API integration and data fetching patterns
- Testing with Jest and React Testing Library
- Build optimization and bundle analysis""",
        
        'Angular': """
- Component architecture and module organization
- Service injection and dependency management
- RxJS observables and reactive programming
- Angular Router configuration and guards
- Forms handling (reactive and template-driven)
- HTTP client implementation and interceptors
- Testing with Jasmine and Karma""",
        
        'Node.js/Express': """
- Express middleware implementation and ordering
- RESTful API design and routing patterns
- Database integration and connection pooling
- Authentication strategies (JWT, sessions, OAuth)
- Error handling and logging middleware
- Security best practices (CORS, rate limiting)
- Testing with Jest, Mocha, or similar frameworks""",
        
        '.NET': """
- .NET Core architecture and dependency injection
- Entity Framework Core patterns and migrations
- ASP.NET Core MVC/API controller design
- Authentication and authorization with Identity
- Configuration management and options pattern
- Logging with built-in providers
- Testing with xUnit and integration tests"""
    }
    
    return guidelines_map.get(framework, "- Framework-specific analysis will be applied based on detected patterns")

def execute_agent_workflow(selected_agents, mop_content):
    """Execute the workflow with selected agents."""
    generated_code = None
    
    # Check if mop_content is a path to extracted ZIP - WINDOWS COMPATIBLE
    is_extracted_zip = isinstance(mop_content, str) and ("extracted_zip" in mop_content or mop_content.startswith("uploads"))
    
    if is_extracted_zip:
        # Read any codebase from extracted ZIP
        result = read_codebase_from_zip(mop_content)
        if len(result) == 4:  # Always store the result, even if no code files found
            codebase_content, guidelines, file_list, files_metadata = result
            if file_list and len(file_list) > 0:  # Check if actual code files were found
                st.info(f"📁 Detected codebase with {len(file_list)} files. Preparing for validation...")
            else:
                st.warning(f"⚠️ ZIP extracted but no valid code files found. Content: {codebase_content[:100]}...")
            # Store the structured codebase for validation (new 4-tuple format)
            st.session_state.codebase_data = (codebase_content, guidelines, file_list, files_metadata)
            
            # Look for PRD files in the extracted content - CROSS-PLATFORM COMPATIBLE
            prd_content = None
            for root, dirs, files in os.walk(mop_content):
                for file in files:
                    if file.lower().endswith('.md') and ('prd' in file.lower() or 'requirement' in file.lower()):
                        # Use Path for cross-platform compatibility
                        file_path = Path(root) / file
                        try:
                            with open(str(file_path), 'r', encoding='utf-8') as f:
                                prd_content = f.read()
                                st.info(f"📋 Found PRD document: {file}")
                                break
                        except Exception as e:
                            st.warning(f"⚠️ Could not read PRD file {file}: {e}")
                            continue
                if prd_content:
                    break
            
            # Store PRD content in session state
            st.session_state.prd_content = prd_content
            
        elif len(result) >= 3 and result[0] and result[0].strip():  # Legacy format with codebase content
            codebase_content, guidelines, file_list = result[:3]
            st.info(f"📁 Detected codebase with {len(file_list)} files. Preparing for validation...")
            # Store the structured codebase for validation (legacy 3-tuple format)
            st.session_state.codebase_data = (codebase_content, guidelines, file_list)
            
            # Look for PRD files in the extracted content (legacy path) - CROSS-PLATFORM COMPATIBLE
            prd_content = None
            for root, dirs, files in os.walk(mop_content):
                for file in files:
                    if file.lower().endswith('.md') and ('prd' in file.lower() or 'requirement' in file.lower()):
                        # Use Path for cross-platform compatibility
                        file_path = Path(root) / file
                        try:
                            with open(str(file_path), 'r', encoding='utf-8') as f:
                                prd_content = f.read()
                                st.info(f"📋 Found PRD document: {file}")
                                break
                        except Exception as e:
                            st.warning(f"⚠️ Could not read PRD file {file} (legacy): {e}")
                            continue
                if prd_content:
                    break
            
            # Store PRD content in session state
            st.session_state.prd_content = prd_content
            
        else:
            st.warning("⚠️ No readable files found in the uploaded ZIP")
            # Don't return here, let the workflow continue with other agents
            st.session_state.codebase_data = None
            
        # Ensure validation files and multi-model settings are available
        if not hasattr(st.session_state, 'validation_files'):
            st.session_state.validation_files = {}
        if not hasattr(st.session_state, 'use_multi_model_validation'):
            st.session_state.use_multi_model_validation = True  # Default to multi-model for codebase validation

    # Code Generation
    if "coder" in selected_agents:
        try:
            success, result = generate_code(mop_content)
            if success:
                generated_code = result
                # Save generated code
                os.makedirs("./output", exist_ok=True)
                with open("./output/generated_code.py", "w") as f:
                    f.write(result)

                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "codegen"
                commit_message = "Add generated code from agent"
                # Save PRD if exists for later attach
                prd_file_path = './output/prd.md'
                # Store GitHub push information for later permission (all files)
                st.session_state.pending_github_pushes['Code Generator'] = {
                    'file_paths': ['./output/generated_code.py'],
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---

                st.session_state.workflow_results['Code Generator'] = {
                    'status': 'success',
                    'output': result,
                    'file_path': './output/generated_code.py',
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Code Generator'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Code Generator'] = {
                'status': 'error',
                'error': str(e)
            }

    # Code Review
    if "reviewer" in selected_agents:
        st.info("🔧 DEBUG: Reviewer agent selected - entering code review path")
        try:
            # Use extracted codebase if available, otherwise use generated code
            if hasattr(st.session_state, 'codebase_data') and st.session_state.codebase_data:
                # Debug codebase data
                codebase_data = st.session_state.codebase_data
                st.info(f"🔧 DEBUG: Codebase data type: {type(codebase_data)}, length: {len(codebase_data) if isinstance(codebase_data, (list, tuple)) else 'N/A'}")
                
                # Check if codebase data is valid
                if isinstance(codebase_data, (list, tuple)) and len(codebase_data) >= 3:
                    codebase_content = codebase_data[0]
                    file_list = codebase_data[2] if len(codebase_data) > 2 else []

                    if not codebase_content or codebase_content.strip() == "":
                        st.warning("⚠️ Codebase content is empty, falling back to 'No code to review' message")
                        success = False
                        result = "No code content found in the uploaded ZIP file. Please ensure the ZIP contains readable code files."
                    elif not file_list or len(file_list) == 0:
                        st.warning("⚠️ No valid code files found in ZIP")
                        success = False
                        result = "The ZIP file was extracted successfully, but no recognizable code files were found. Please ensure the ZIP contains source code files with standard extensions (.py, .java, .js, .cs, etc.)."
                    else:
                        # Review the codebase with multi-model validation
                        prd_content = getattr(st.session_state, 'prd_content', None)
                        validation_files = getattr(st.session_state, 'validation_files', {})
                        use_multi_model = getattr(st.session_state, 'use_multi_model_validation', False)

                        st.info("🔍 Reviewing codebase with enhanced validation...")

                        success, result = review_code(
                            st.session_state.codebase_data,
                            prd_content=prd_content,
                            validation_files=validation_files,
                            use_multi_model=use_multi_model
                        )
                else:
                    st.warning("⚠️ Invalid codebase data format")
                    success = False
                    result = "Invalid codebase data format. Please try uploading the ZIP file again."
            elif generated_code:
                success, result = review_code(generated_code)
            else:
                success = False
                result = "No code available for review. Please upload a ZIP file with code or generate code first."
            
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "review"
                commit_message = "Add code review feedback from agent"
                
                # Save review result as a file
                os.makedirs("./output", exist_ok=True)
                review_file = "./output/review.txt"
                with open(review_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['Code Reviewer'] = {
                    'file_path': review_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['Code Reviewer'] = {
                    'status': 'success',
                    'output': result,
                    'file_path': review_file,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Code Reviewer'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Code Reviewer'] = {
                'status': 'error',
                'error': str(e)
            }

    # Code Improvement
    if "improver" in selected_agents and generated_code:
        try:
            success, result = improve_code(generated_code)
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "improve"
                commit_message = "Add improved code from agent"
                
                # Save improved code as a file
                os.makedirs("./output", exist_ok=True)
                improve_file = "./output/improved_code.py"
                with open(improve_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['Code Improver'] = {
                    'file_path': improve_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['Code Improver'] = {
                    'status': 'success',
                    'output': result,
                    'file_path': improve_file,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Code Improver'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Code Improver'] = {
                'status': 'error',
                'error': str(e)
            }

    # API Integration
    if "integrator" in selected_agents:
        try:
            success, result = generate_api_integration(mop_content)
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "api_integration"
                commit_message = "Add API integration result from agent"
                
                # Save API integration result as a file
                os.makedirs("./output", exist_ok=True)
                api_file = "./output/api_integration.txt"
                with open(api_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['API Integration'] = {
                    'file_path': api_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['API Integration'] = {
                    'status': 'success',
                    'output': result,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['API Integration'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['API Integration'] = {
                'status': 'error',
                'error': str(e)
            }

    # Documentation
    if "documenter" in selected_agents and generated_code:
        try:
            success, result = generate_documentation(generated_code)
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "documentation"
                commit_message = "Add documentation from agent"
                
                # Save documentation as a file
                os.makedirs("./output", exist_ok=True)
                doc_file = "./output/documentation.md"
                with open(doc_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['Documentation'] = {
                    'file_path': doc_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['Documentation'] = {
                    'status': 'success',
                    'output': result,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Documentation'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Documentation'] = {
                'status': 'error',
                'error': str(e)
            }

    # PRD Generation
    if "prd_agent" in selected_agents:
        try:
            # Try to use the original PRD agent first for better epic formatting
            try:
                import sys
                import os
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)
                from config.config import API_KEY
                from anthropic import Anthropic
                from src.agents.core.prd_agent import PRDAgent

                # Use original PRD agent
                claude_client = Anthropic(api_key=API_KEY)
                prd_agent = PRDAgent(claude_client=claude_client, name="PRDAgent", api_key=API_KEY)

                # Save PRD to file using original agent
                os.makedirs("./output", exist_ok=True)
                prd_file_path = "./output/prd.md"
                result_message = prd_agent.generate_prd(mop_content=mop_content, output_file=prd_file_path)

                # Read the generated PRD content
                with open(prd_file_path, 'r', encoding='utf-8') as f:
                    prd_content = f.read()

                success = True
                result = prd_content

            except Exception as original_agent_error:
                # Fallback to Claude API direct call
                success, result = generate_prd(mop_content)
                if success:
                    # Save PRD to file
                    os.makedirs("./output", exist_ok=True)
                    prd_file_path = "./output/prd.md"
                    with open(prd_file_path, "w", encoding="utf-8") as f:
                        f.write(result)

            if success:
                # Try to create Jira issues from PRD
                jira_success, jira_message = create_jira_issues_from_prd()

                # Append Jira status to output
                final_output = result
                if jira_success:
                    final_output += f"\n\n✅ **Jira Integration**: {jira_message}"
                else:
                    final_output += f"\n\n⚠️ **Jira Integration**: {jira_message}"

                st.session_state.workflow_results['PRD Generator'] = {
                    'status': 'success',
                    'output': final_output,
                    'file_path': prd_file_path,
                    'jira_status': jira_success,
                    'jira_message': jira_message
                }
            else:
                st.session_state.workflow_results['PRD Generator'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['PRD Generator'] = {
                'status': 'error',
                'error': str(e)
            }

    # QA & Testing
    if "qa_agent" in selected_agents:
        try:
            success, result = generate_qa_tests(mop_content, generated_code)
            if success:
                # Save QA results to file
                os.makedirs("./output", exist_ok=True)
                qa_file_path = "./output/qa_results.txt"
                with open(qa_file_path, "w", encoding="utf-8") as f:
                    f.write(result)
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "qa"
                commit_message = "Add QA results from agent"
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['QA & Testing'] = {
                    'file_path': qa_file_path,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                # Check if there are test failures or issues that need Jira tickets
                has_failures = any(keyword in result.lower() for keyword in ['failed', 'error', 'bug', 'issue', 'failure'])
                final_output = result
                jira_success = False
                jira_message = "No issues detected - no Jira ticket needed"
                if has_failures:
                    # Create Jira bug ticket for test failures
                    jira_success, jira_message = create_jira_bug_ticket(result)
                    if jira_success:
                        final_output += f"\n\n🐛 **Jira Bug Ticket**: {jira_message}"
                    else:
                        final_output += f"\n\n⚠️ **Jira Bug Ticket Failed**: {jira_message}"
                
                st.session_state.workflow_results['QA & Testing'] = {
                    'status': 'success',
                    'output': final_output,
                    'file_path': qa_file_path,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name,
                    'jira_status': jira_success,
                    'jira_message': jira_message,
                    'has_failures': has_failures
                }
            else:
                st.session_state.workflow_results['QA & Testing'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['QA & Testing'] = {
                'status': 'error',
                'error': str(e)
            }
def push_prd_to_github(mop_content):
    """Push the prd.md file to GitHub for all agents."""
    try:
        repo_name = get_repo_name_from_mop(mop_content)
        ensure_github_repo(repo_name)
        prd_file_path = './output/prd.md'
        if not os.path.exists(prd_file_path):
            raise FileNotFoundError('prd.md file not found in ./output')
        # Push for each agent branch
        agent_branches = [
            'codegen', 'review', 'improve', 'qa', 'prd', 'documentation', 'api_integration'
        ]
        for branch_name in agent_branches:
            temp_git_dir = f"./output/{repo_name}_git_{branch_name}"
            if os.path.exists(temp_git_dir):
                shutil.rmtree(temp_git_dir)
            os.makedirs(temp_git_dir, exist_ok=True)
            shutil.copy(prd_file_path, os.path.join(temp_git_dir, "prd.md"))
            commit_message = f"Add prd.md for {branch_name} agent"
            push_to_github(temp_git_dir, repo_name, branch_name, commit_message)
        return True, f"prd.md pushed to GitHub for all agent branches: {', '.join(agent_branches)}"
    except Exception as e:
        return False, f"Failed to push prd.md: {str(e)}"

def handle_generated_files(result, output_dir="./output"):
    os.makedirs(output_dir, exist_ok=True)
    try:
        files_dict = json.loads(result)
        file_paths = []
        for filename, content in files_dict.items():
            file_path = os.path.join(output_dir, filename)
            # Ensure subdirectories exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            file_paths.append(file_path)
        return file_paths, files_dict
    except Exception as e:
        # fallback: treat as single file
        file_path = os.path.join(output_dir, "generated_code.txt")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(result)
        return [file_path], {"generated_code.txt": result}

def detect_language(filename):
    ext = filename.split(".")[-1].lower()
    return {
        "py": "python",
        "js": "javascript",
        "jsx": "jsx",
        "ts": "typescript",
        "tsx": "tsx",
        "css": "css",
        "html": "html",
        "md": "markdown",
        "java": "java",
        "json": "json",
        "txt": "text"
    }.get(ext, "text")

def to_display_string(content):
    if isinstance(content, dict):
        return json.dumps(content, indent=2)
    content_str = str(content)
    # Remove all HTML tags to prevent createElement errors
    import re
    content_str = re.sub(r'<[^>]*>', '', content_str)
    return content_str

if __name__ == "__main__":
    st.info("🔧 DEBUG: Script is running as main - this should appear")
    main()
