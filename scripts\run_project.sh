#!/bin/bash

# RadAgents Project Startup Script
# This script activates the virtual environment and runs the project

echo "🚀 Starting RadAgents Project..."
echo "=================================="

# Check if we're in the correct directory
if [ ! -f "main.py" ]; then
    echo "❌ Error: main.py not found. Please run this script from the RadAgents directory."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "autogen_env" ]; then
    echo "❌ Error: Virtual environment 'autogen_env' not found."
    echo "Please run: python3 -m venv autogen_env && source autogen_env/bin/activate && pip install -r config/requirements.txt"
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source autogen_env/bin/activate

# Check if dependencies are installed
echo "🔍 Checking dependencies..."
python -c "import anthropic, autogen" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Error: Dependencies not properly installed."
    echo "Installing dependencies..."
    pip install -r config/requirements.txt
fi

echo "✅ Environment ready!"
echo ""
# Run the unified main entry point
echo "🚀 Starting RadAgents..."
python main.py
