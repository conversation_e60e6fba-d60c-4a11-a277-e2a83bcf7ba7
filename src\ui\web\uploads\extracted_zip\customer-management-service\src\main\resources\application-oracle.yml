# Oracle Production Database Configuration
# As per PRD: "Maintain existing Oracle database and schema" (line 124)
# Technology Stack: "Oracle (maintained) with JPA/Hibernate" (line 74)

server:
  port: 8080
  servlet:
    context-path: /customer-service

spring:
  application:
    name: customer-management-service
  
  # Oracle Database Configuration (Production)
  datasource:
    url: jdbc:oracle:thin:@${DB_HOST:localhost}:${DB_PORT:1521}:${DB_SERVICE_NAME:XEPDB1}
    username: ${DB_USERNAME:vrepair_user}
    password: ${DB_PASSWORD:vrepair_pass}
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      pool-name: CustomerServiceOraclePool
      connection-test-query: SELECT 1 FROM DUAL
  
  # JPA/Hibernate Configuration for Oracle
  jpa:
    hibernate:
      ddl-auto: validate  # Don't modify existing schema
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        dialect: org.hibernate.dialect.Oracle12cDialect
        format_sql: false
        show_sql: false
        jdbc:
          batch_size: 25
        order_inserts: true
        order_updates: true
        connection:
          provider_disables_autocommit: true
    open-in-view: false
    database-platform: org.hibernate.dialect.Oracle12cDialect
  
  # Redis Cache Configuration
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
  
  cache:
    type: redis
    redis:
      time-to-live: 300000 # 5 minutes
      cache-null-values: false
  
  # Security Configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${JWT_JWK_SET_URI:https://auth.verizon.com/.well-known/jwks.json}

# Application-specific configuration
app:
  database:
    type: oracle
    schema: ${DB_SCHEMA:VREPAIR}
  
  encryption:
    key: ${ENCRYPTION_KEY:dGVzdC1lbmNyeXB0aW9uLWtleS0xMjM0NTY3ODkwMTIzNDU2}
  
  security:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:https://vrepair.verizon.com}
  
  # External System Integrations (as per PRD)
  external-services:
    etms:
      base-url: ${ETMS_BASE_URL:https://etms.verizon.com}
      timeout: 5000
      retry-attempts: 3
      api-key: ${ETMS_API_KEY}
    
    baais:
      base-url: ${BAAIS_BASE_URL:https://baais.verizon.com}
      timeout: 3000
      retry-attempts: 2
      db-connection: ${BAAIS_DB_CONNECTION}
    
    vi:
      base-url: ${VI_BASE_URL:https://vi.verizon.com}
      timeout: 2000
      retry-attempts: 1
      certificate-path: ${VI_CERT_PATH}
    
    uts:
      queue-url: ${UTS_QUEUE_URL:mq://uts.verizon.com:5672}
      timeout: 2100
      retry-attempts: 3
    
    need:
      db-url: ${NEED_DB_URL:***********************************}
      batch-size: 1000
    
    cllinet:
      base-url: ${CLLINET_BASE_URL:https://cllinet.verizon.com}
      oauth-token-url: ${CLLINET_OAUTH_URL}

# Management and monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    redis:
      enabled: true
    db:
      enabled: true

# Logging configuration
logging:
  level:
    com.verizon.vrepair.customer: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/customer-management-service.log
    max-size: 10MB
    max-history: 30

# OpenAPI/Swagger configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  info:
    title: Customer Management Service API
    description: VRepair Customer Management Microservice - Migrated from legacy C++ LSTRESRV system
    version: 1.0.0
    contact:
      name: VRepair Development Team
      email: <EMAIL>
