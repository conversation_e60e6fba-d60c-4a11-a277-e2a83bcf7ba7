# ============================================================================
# RadAgents Project .gitignore
# ============================================================================

# ============================================================================
# SENSITIVE CONFIGURATION & API KEYS
# ============================================================================
# Environment variables and sensitive configuration
.env
.env.local
.env.*.local
config.py
**/config.py

# API Keys and secrets (backup exclusions)
**/api_key*
**/secret*
**/*key*.txt
**/*token*.txt

# ============================================================================
# PYTHON
# ============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml
.pdm-python
.pdm-build/

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ============================================================================
# VIRTUAL ENVIRONMENTS
# ============================================================================
# Virtual environments (all variations)
autogen_env/
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/
.ENV/

# Conda environments
.conda/

# ============================================================================
# PROJECT-SPECIFIC EXCLUSIONS
# ============================================================================
# Generated output files and data
data/output/
output/
generated_scripts/
**/generated_*.py
**/improved_*.py

# Test results and logs
logs/
test_logs/
*.log
*.html
test_results_*.json
test_report_*.html

# Screenshots and test artifacts
data/test_screenshots/
screenshots/
*.png
*.jpg
*.jpeg
*.gif

# Temporary files and caches
nul
*.tmp
*.temp
.cache/
.diskcache/

# AutoGen specific
.autogen_cache/

# ============================================================================
# SELENIUM & WEB TESTING
# ============================================================================
# WebDriver binaries
chromedriver*
geckodriver*
msedgedriver*
operadriver*

# Browser profiles and data
browser_data/
profile/
user_data/

# ============================================================================
# IDE AND EDITOR FILES
# ============================================================================
# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# OPERATING SYSTEM FILES
# ============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.directory
.Trash-*

# ============================================================================
# NODE.JS (for any frontend components)
# ============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# ============================================================================
# DOCKER
# ============================================================================
*.dockerignore
docker-compose.override.yml

# ============================================================================
# GIT
# ============================================================================
.git/
*.orig
*.rej

# ============================================================================
# MISC
# ============================================================================
# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Large data files
*.csv
*.xlsx
*.xls
data/large_datasets/

# Version file (if auto-generated)
version.txt

# ============================================================================
# PROJECT SPECIFIC EXCLUSIONS
# ============================================================================
# Specific to RadAgents project structure
4.15.0
package-lock.json

# Test data that might contain sensitive information
**/demo_login.json
**/working_login_demo.json
data/test_data/*.json

# Any generated documentation
generated_docs/