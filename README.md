# RadAgents - AI Agent Automation Platform

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

RadAgents is an intelligent automation platform that leverages AI agents to streamline software development workflows, from code generation to testing and deployment.

## 🚀 Quick Start

### Prerequisites
- Python 3.11 or higher
- Virtual environment (recommended)
- Git

### Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd RadAgents
   ```

2. **Set up virtual environment**
   ```bash
   python3 -m venv autogen_env
   source autogen_env/bin/activate  # On Windows: autogen_env\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r config/requirements.txt
   ```

4. **Configure API keys**
   - Copy `config/config.py` and update with your API keys
   - Set environment variables or update the config file directly

5. **Run the application**
   ```bash
   # Use the startup script (recommended)
   ./scripts/run_project.sh
   
   # Or run directly
   python main.py
   ```

## 📁 Project Structure

```
RadAgents/
├── 📁 src/                          # Source code
│   ├── 📁 agents/                   # AI Agent implementations
│   │   ├── 📁 core/                 # Core agent types
│   │   │   ├── base_agent.py        # Base agent class
│   │   │   ├── code_generator_agent.py
│   │   │   ├── reviewer_agent.py
│   │   │   ├── improver_agent.py
│   │   │   ├── documentation_agent.py
│   │   │   ├── prd_agent.py
│   │   │   └── qa_agent.py
│   │   └── 📁 specialized/          # Specialized agents
│   │       ├── api_integration_agent.py
│   │       ├── selenium_test_agent.py
│   │       └── claude-sonnet-parser-agent.py
│   ├── 📁 ui/                       # User interfaces
│   │   ├── 📁 web/                  # Web-based UIs (Streamlit)
│   │   │   ├── ui_app.py           # Full-featured web UI
│   │   │   ├── simple_ui.py        # Simplified web UI
│   │   │   ├── dynamic_ui.py       # Dynamic web UI
│   │   │   └── working_ui.py       # Working prototype UI
│   │   └── 📁 cli/                  # Command-line interface
│   │       └── main.py             # CLI workflow runner
│   ├── 📁 workflows/                # Workflow definitions
│   │   ├── 📁 core/                 # Core workflow logic
│   │   │   ├── run_autogen_mop.py  # AutoGen workflow runner
│   │   │   └── QAmop.py            # QA workflow
│   │   └── 📁 templates/            # MOP templates
│   │       ├── mop_cisco_audit.py  # Cisco audit MOP
│   │       ├── PRDmop.py           # PRD MOP template
│   │       └── mop.py              # Generic MOP template
│   ├── 📁 integrations/             # External service integrations
│   │   ├── 📁 github/               # GitHub integration
│   │   │   └── github_utils.py     # GitHub API utilities
│   │   ├── 📁 jira/                 # Jira integration
│   │   │   └── create_jira_issues_from_prd.py
│   │   └── 📁 selenium/             # Selenium testing
│   │       └── selenium_agent_example.py
│   └── 📁 utils/                    # Utility functions
├── 📁 config/                       # Configuration files
│   ├── config.py                   # Main configuration
│   └── requirements.txt            # Python dependencies
├── 📁 tests/                        # Test suite
│   ├── 📁 unit/                     # Unit tests
│   │   ├── test_acceptance_criteria.py
│   │   ├── test_permissions.py
│   │   └── testMop.py
│   ├── 📁 integration/              # Integration tests
│   └── 📁 e2e/                      # End-to-end tests
├── 📁 data/                         # Data files
│   ├── 📁 input/                    # Input data
│   ├── 📁 output/                   # Generated outputs
│   │   ├── generated_code.py
│   │   ├── improved_code.py
│   │   └── ...
│   ├── 📁 test_data/                # Test datasets
│   └── 📁 test_screenshots/         # Test screenshots
├── 📁 docs/                         # Documentation
│   ├── README.md                   # This file
│   ├── README_SETUP.md             # Setup instructions
│   ├── AGENT_PERMISSION_SYSTEM.md  # Permission system docs
│   ├── GITHUB_PERMISSION_FEATURE.md
│   ├── JIRA_INTEGRATION_FEATURES.md
│   ├── SELENIUM_AGENT_README.md
│   └── ...
├── 📁 scripts/                      # Utility scripts
│   ├── run_project.sh              # Main startup script
│   └── push_prd_to_github.py       # GitHub deployment script
├── 📁 logs/                         # Application logs
├── main.py                         # Main application entry point
└── autogen_env/                    # Python virtual environment
```

## 🎯 Features

### 🤖 AI Agents
- **Code Generator**: Creates code from Method of Procedures (MOPs)
- **Code Reviewer**: Automated code review and quality assessment
- **Code Improver**: Enhances code with best practices
- **Documentation Agent**: Generates comprehensive documentation
- **PRD Agent**: Creates Product Requirements Documents
- **QA Agent**: Automated testing and compliance checking
- **API Integration Agent**: Handles external API integrations
- **Selenium Test Agent**: Automated web testing

### 🖥️ Multiple Interfaces
- **CLI Interface**: Interactive command-line workflow
- **Web UI**: Multiple Streamlit-based web applications
  - Full-featured UI with all capabilities
  - Simple UI for basic operations
  - Dynamic UI with advanced features

### 🔗 Integrations
- **GitHub**: Automatic repository creation and code pushing
- **Jira**: Issue creation and project management
- **Selenium**: Automated web testing and screenshots

### 📊 Workflow Automation
- Method of Procedures (MOP) processing
- Multi-agent collaboration
- Permission-based execution system
- Real-time progress tracking

## 🚀 Usage

### Command Line Interface
```bash
python main.py
# Select option 1 for CLI interface
```

### Web Interface
```bash
python main.py
# Select options 2-4 for different web UIs
```

### Direct Streamlit Launch
```bash
streamlit run src/ui/web/ui_app.py
```

## 🔧 Configuration

### API Keys
Update `config/config.py` with your API keys:
- `API_KEY`: Claude/Anthropic API key
- `AZURE_OPENAI_API_KEY`: Azure OpenAI API key
- `GOOGLE_API_KEY`: Google API key
- `GITHUB_TOKEN`: GitHub personal access token
- `JIRA_API_KEY`: Jira API token

### Environment Variables
Alternatively, set environment variables:
```bash
export API_KEY="your-claude-api-key"
export GITHUB_TOKEN="your-github-token"
export JIRA_API_KEY="your-jira-api-key"
```

## 🧪 Testing

### Run Unit Tests
```bash
python -m pytest tests/unit/ -v
```

### Run All Tests
```bash
python -m pytest tests/ -v
```

## 📖 Documentation

- [Setup Guide](docs/README_SETUP.md)
- [Agent Permission System](docs/AGENT_PERMISSION_SYSTEM.md)
- [GitHub Integration](docs/GITHUB_PERMISSION_FEATURE.md)
- [Jira Integration](docs/JIRA_INTEGRATION_FEATURES.md)
- [Selenium Testing](docs/SELENIUM_AGENT_README.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the [documentation](docs/)
- Open an issue on GitHub
- Review existing issues and discussions

## 🔄 Recent Changes

### Project Reorganization (Latest)
- ✅ Reorganized project into industry-standard folder structure
- ✅ Separated concerns: agents, UI, workflows, integrations
- ✅ Updated import paths and configuration
- ✅ Created unified main entry point
- ✅ Improved documentation and setup process