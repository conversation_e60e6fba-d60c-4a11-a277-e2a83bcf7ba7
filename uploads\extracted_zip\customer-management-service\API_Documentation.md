# Customer Management Service API Documentation

## Overview

The Customer Management Service provides comprehensive RESTful APIs for managing customer data in the VRepair system. This service is a modernized Java microservice that replaces the legacy C++ customer management system while maintaining 100% functional parity.

## Base URL

- **Production**: `https://api.vrepair.verizon.com/customer-service`
- **Staging**: `https://staging-api.vrepair.verizon.com/customer-service`  
- **Local**: `http://localhost:8080/customer-service`

## Interactive API Documentation

Once the service is running, you can access the interactive Swagger UI documentation at:

- **Swagger UI**: `{BASE_URL}/swagger-ui.html`
- **OpenAPI Spec**: `{BASE_URL}/v3/api-docs`
- **OpenAPI JSON**: `{BASE_URL}/v3/api-docs.yaml`

## Authentication

All API endpoints require OAuth 2.0 JWT authentication. Include the Bearer token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Roles and Permissions

| Role | Permissions |
|------|-------------|
| **USER** | Basic customer operations (read, create, update) |
| **TECHNICIAN** | Advanced customer operations + search |
| **ADMIN** | All operations + statistics + bulk operations |
| **SUPERVISOR** | Customer activation/deactivation authority |

## API Endpoints

### 1. Create Customer

**POST** `/api/v1/customers`

Creates a new customer with validation and business rule enforcement.

**Required Roles**: USER, ADMIN, TECHNICIAN

**Request Body**:
```json
{
  "billingTelephoneNumber": "**********",
  "serviceAddress": "123 Main St, Anytown, ST 12345",
  "customerName": "John Smith",
  "customerType": "RESIDENTIAL",
  "accountNumber": "ACC123456",
  "serviceClassCode": "RES001",
  "maintenanceLevel": "STANDARD",
  "contactPhone": "**********",
  "emailAddress": "<EMAIL>"
}
```

**Response** (201 Created):
```json
{
  "customerCode": "RES123456",
  "billingTelephoneNumber": "**********",
  "serviceAddress": "123 Main St, Anytown, ST 12345",
  "customerName": "John Smith",
  "customerType": "RESIDENTIAL",
  "customerStatus": "ACTIVE",
  "accountNumber": "ACC123456",
  "createdDate": "2024-01-15T10:30:00",
  "modifiedDate": "2024-01-15T10:30:00"
}
```

### 2. Get Customer by Code

**GET** `/api/v1/customers/{customerCode}`

Retrieves customer details by customer code.

**Required Roles**: USER, ADMIN, TECHNICIAN, SUPERVISOR

**Path Parameters**:
- `customerCode` (string): Customer code (e.g., "RES123456")

**Response** (200 OK):
```json
{
  "customerCode": "RES123456",
  "billingTelephoneNumber": "**********",
  "serviceAddress": "123 Main St, Anytown, ST 12345",
  "customerName": "John Smith",
  "customerType": "RESIDENTIAL",
  "customerStatus": "ACTIVE"
}
```

### 3. Get Customer by Phone Number

**GET** `/api/v1/customers/phone/{phoneNumber}`

Retrieves customer by phone number.

**Required Roles**: USER, ADMIN, TECHNICIAN, SUPERVISOR

**Path Parameters**:
- `phoneNumber` (string): 10-digit phone number (e.g., "**********")

### 4. Update Customer

**PUT** `/api/v1/customers/{customerCode}`

Updates existing customer information (partial updates supported).

**Required Roles**: USER, ADMIN, TECHNICIAN

**Request Body**:
```json
{
  "customerName": "John A. Smith",
  "serviceAddress": "456 New Address, New City, NC 54321",
  "contactPhone": "**********",
  "emailAddress": "<EMAIL>"
}
```

### 5. Search Customers

**GET** `/api/v1/customers/search`

Advanced customer search with multiple criteria and pagination.

**Required Roles**: USER, ADMIN, TECHNICIAN, SUPERVISOR

**Query Parameters**:
- `customerName` (string, optional): Customer name (partial match)
- `phoneNumber` (string, optional): Phone number (exact match)
- `customerType` (enum, optional): RESIDENTIAL, BUSINESS, GOVERNMENT, WHOLESALE
- `customerStatus` (enum, optional): ACTIVE, INACTIVE, PENDING, SUSPENDED, TERMINATED
- `serviceAddress` (string, optional): Service address (partial match)
- `page` (integer, default: 0): Page number (0-based)
- `size` (integer, default: 20): Page size
- `sort` (string, default: "customerName"): Sort field
- `direction` (string, default: "ASC"): Sort direction (ASC/DESC)

**Response** (200 OK):
```json
{
  "content": [
    {
      "customerCode": "RES123456",
      "customerName": "John Smith",
      "billingTelephoneNumber": "**********",
      "customerType": "RESIDENTIAL",
      "customerStatus": "ACTIVE"
    }
  ],
  "page": 0,
  "size": 20,
  "totalElements": 150,
  "totalPages": 8,
  "first": true,
  "last": false
}
```

### 6. Deactivate Customer

**PUT** `/api/v1/customers/{customerCode}/deactivate`

Deactivates a customer (changes status to INACTIVE).

**Required Roles**: ADMIN, SUPERVISOR

### 7. Activate Customer

**PUT** `/api/v1/customers/{customerCode}/activate`

Activates a customer (changes status to ACTIVE).

**Required Roles**: ADMIN, SUPERVISOR

### 8. Get Customer Statistics

**GET** `/api/v1/customers/statistics`

Retrieves customer statistics and metrics.

**Required Roles**: ADMIN

**Response** (200 OK):
```json
{
  "totalCustomers": 50000,
  "activeCustomers": 45000,
  "inactiveCustomers": 5000,
  "residentialCustomers": 35000,
  "businessCustomers": 15000
}
```

### 9. Check Customer Eligibility

**GET** `/api/v1/customers/{customerCode}/eligibility`

Validates customer eligibility for services.

**Required Roles**: USER, ADMIN, TECHNICIAN

**Response** (200 OK):
```json
true
```

## Error Responses

All errors return consistent JSON responses:

```json
{
  "errorCode": "CUSTOMER_NOT_FOUND",
  "message": "Customer not found with code: CUST001",
  "correlationId": "123e4567-e89b-12d3-a456-426614174000",
  "timestamp": "2024-01-15T10:30:00",
  "path": "/api/v1/customers/CUST001"
}
```

### Common Error Codes

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `CUSTOMER_NOT_FOUND` | 404 | Customer does not exist |
| `CUSTOMER_ALREADY_EXISTS` | 409 | Customer with phone number already exists |
| `CUSTOMER_VALIDATION_ERROR` | 400 | Customer data validation failed |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `ACCESS_DENIED` | 403 | Insufficient permissions |
| `UNAUTHORIZED` | 401 | Authentication required |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INTERNAL_SERVER_ERROR` | 500 | Unexpected server error |

### Validation Error Response

For validation errors, additional field details are provided:

```json
{
  "errorCode": "VALIDATION_ERROR",
  "message": "Request validation failed",
  "correlationId": "123e4567-e89b-12d3-a456-426614174000",
  "timestamp": "2024-01-15T10:30:00",
  "path": "/api/v1/customers",
  "fieldErrors": [
    {
      "field": "billingTelephoneNumber",
      "message": "Phone number must be 10 digits",
      "rejectedValue": "123"
    }
  ]
}
```

## Rate Limiting

| User Type | Limit | Window |
|-----------|-------|---------|
| Authenticated Users | 100 requests | per minute |
| Admin Users | 200 requests | per minute |
| Anonymous (health checks only) | 20 requests | per minute |

When rate limit is exceeded, HTTP 429 is returned with retry information.

## Performance Characteristics

- **Response Time**: <1 second (95th percentile)
- **Throughput**: 100+ operations/second  
- **Cache Hit Ratio**: >80%
- **Availability**: 99.9% uptime target

## Data Validation Rules

### Phone Numbers
- Must be exactly 10 digits
- No formatting characters allowed
- Example: `**********`

### Customer Types
- `RESIDENTIAL`: Individual customers
- `BUSINESS`: Business customers (account number required)
- `GOVERNMENT`: Government customers (account number required)  
- `WHOLESALE`: Wholesale customers

### Customer Status
- `ACTIVE`: Active service
- `INACTIVE`: Service suspended
- `PENDING`: Pending activation
- `SUSPENDED`: Suspended for non-payment
- `TERMINATED`: Permanently terminated

### Address Requirements
- Required for all customers
- Maximum 100 characters
- Must contain valid address information

### Account Numbers
- Required for BUSINESS and GOVERNMENT customers
- Maximum 20 characters
- Alphanumeric characters allowed

## Legacy System Compatibility

This API maintains 100% functional parity with the legacy C++ system:

| Legacy C++ Function | API Endpoint | Notes |
|-------------------|--------------|-------|
| `create_customer()` | `POST /customers` | Enhanced validation |
| `find_customer_by_id()` | `GET /customers/{id}` | Cached responses |
| `find_customer_by_phone()` | `GET /customers/phone/{phone}` | Optimized queries |
| `update_customer()` | `PUT /customers/{id}` | Partial updates supported |
| `search_customers()` | `GET /customers/search` | Enhanced pagination |
| `deactivate_customer()` | `PUT /customers/{id}/deactivate` | Audit logging added |
| `activate_customer()` | `PUT /customers/{id}/activate` | Role-based authorization |
| `get_customer_stats()` | `GET /customers/statistics` | Real-time metrics |

## Health Checks

- **Health Endpoint**: `GET /actuator/health`
- **Metrics Endpoint**: `GET /actuator/metrics`
- **Prometheus Metrics**: `GET /actuator/prometheus`

## Support

For API support and questions:
- **Email**: <EMAIL>
- **Documentation**: https://vrepair.verizon.com/docs
- **Status Page**: https://status.vrepair.verizon.com

## Changelog

### Version 1.0.0 (Current)
- Initial release with full legacy parity
- OAuth 2.0/JWT authentication
- Comprehensive validation and error handling
- Performance optimizations with caching
- Audit logging and monitoring


