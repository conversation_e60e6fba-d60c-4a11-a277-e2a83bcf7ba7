# Oracle Database Configuration for VRepair Customer Management Service
# Source this file before running the application: source oracle.env

# Database Configuration
export DB_HOST=localhost
export DB_PORT=1521
export DB_SERVICE_NAME=XEPDB1
export DB_USERNAME=system
export DB_PASSWORD=VRepair123!

# Redis Configuration (Optional)
export REDIS_HOST=localhost
export REDIS_PORT=6379

# Application Configuration
export ENCRYPTION_KEY=dGVzdC1lbmNyeXB0aW9uLWtleS0xMjM0NTY3ODkwMTIzNDU2
export JWT_JWK_SET_URI=https://auth.verizon.com/.well-known/jwks.json

echo "Oracle environment variables loaded successfully!"
echo "Database: ${DB_HOST}:${DB_PORT}:${DB_SERVICE_NAME}"
echo "Username: ${DB_USERNAME}"
