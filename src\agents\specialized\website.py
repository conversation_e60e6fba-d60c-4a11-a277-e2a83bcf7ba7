import streamlit as st
import os
from datetime import datetime
import traceback
import git
from git import Repo
import shutil
import re
import json

# Configure Streamlit page
st.set_page_config(
    page_title="MOP Agent Workflow UI",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add the RADIANT logo image in the top-left corner
st.markdown(
    """
    <style>
    .radiant-corner-img {
        position: fixed;
        top: 30px;
        left: 30px;
        width: 180px;
        height: auto;
        z-index: 9999;
        opacity: 0.92;
        pointer-events: none;
        border-radius: 18px;
        box-shadow: 0 8px 32px rgba(102,126,234,0.18);
        transition: opacity 0.3s;
    }
    @media (max-width: 900px) {
        .radiant-corner-img { display: none; }
    }
    </style>
    <img src="assets/radiant_logo.png" class="radiant-corner-img" alt="Radiant Logo" />
    """,
    unsafe_allow_html=True
)

# Enhanced CSS for gorgeous, modern styling
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

    /* Global Styles */
    .stApp {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-family: 'Inter', sans-serif;
    }

    /* Main Container */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        backdrop-filter: blur(10px);
        margin: 1rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Header Styling */
    .main-header {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        animation: fadeInDown 1s ease-out;
    }

    /* Sidebar Styling */
    .css-1d391kg {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(226, 232, 240, 0.5);
    }

    /* Agent Cards */
    .agent-card {
        background: rgba(255, 255, 255, 0.98);
        border: 2px solid #e2e8f0;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .agent-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .agent-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        border-color: #667eea;
    }

    .agent-card:hover::before {
        transform: scaleX(1);
    }

    /* Status Indicators */
    .status-success {
        color: #10b981;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: fadeInLeft 0.5s ease-out;
    }

    .status-error {
        color: #ef4444;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: fadeInLeft 0.5s ease-out;
    }

    .status-running {
        color: #f59e0b;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: pulse 2s infinite;
    }

    /* Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }

    .stButton > button:active {
        transform: translateY(0);
    }

    /* Checkbox Styling */
    .stCheckbox > label {
        font-weight: 500;
        color: #374151;
        font-size: 1.1rem;
    }

    /* Metrics Cards */
    .metric-card {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.06);
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
        border-color: #667eea;
    }

    /* Progress Bar */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 10px;
        height: 8px;
    }

    /* Expander Styling */
    .streamlit-expanderHeader {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 12px;
        padding: 1rem;
        font-weight: 600;
        color: #374151;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .streamlit-expanderHeader:hover {
        border-color: #667eea;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* Text Areas */
    .stTextArea > div > div > textarea {
        border-radius: 12px;
        border: 2px solid #e2e8f0;
        font-family: 'JetBrains Mono', monospace;
        transition: all 0.3s ease;
    }

    .stTextArea > div > div > textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* File Uploader */
    .stFileUploader > div {
        border: 2px dashed #667eea;
        border-radius: 15px;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.95);
        transition: all 0.3s ease;
    }

    .stFileUploader > div:hover {
        border-color: #5a67d8;
        background: rgba(255, 255, 255, 1);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    /* Animations */
    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* Scrollbar Styling */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
    }

    /* Info/Warning/Error Boxes */
    .stAlert {
        border-radius: 12px;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    /* Code Blocks */
    .stCode {
        border-radius: 12px;
        border: 2px solid #e2e8f0;
        font-family: 'JetBrains Mono', monospace;
    }

    /* Download Button */
    .stDownloadButton > button {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.5rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }

    .stDownloadButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.6);
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables."""
    if 'workflow_results' not in st.session_state:
        st.session_state.workflow_results = {}
    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'selected_agents' not in st.session_state:
        st.session_state.selected_agents = set()
    if 'mop_content' not in st.session_state:
        st.session_state.mop_content = ""
    if 'mop_source' not in st.session_state:
        st.session_state.mop_source = ""
    # GitHub permission tracking
    if 'github_permissions' not in st.session_state:
        st.session_state.github_permissions = {}
    if 'pending_github_pushes' not in st.session_state:
        st.session_state.pending_github_pushes = {}
    # Agent execution permission tracking
    if 'agent_permissions' not in st.session_state:
        st.session_state.agent_permissions = {}
    if 'pending_agent_executions' not in st.session_state:
        st.session_state.pending_agent_executions = {}

def get_agent_descriptions():
    """Return descriptions for each agent."""
    return {
        "coder": {
            "name": "Code Generator",
            "description": "Generates clean Python scripts based on MOP content with best practices and detailed comments.",
            "icon": "💻"
        },
        "reviewer": {
            "name": "Code Reviewer", 
            "description": "Reviews generated code for clarity, functionality, and adherence to best practices.",
            "icon": "🔍"
        },
        "improver": {
            "name": "Code Improver",
            "description": "Enhances and optimizes existing code with performance improvements and better structure.",
            "icon": "✨"
        },
        "integrator": {
            "name": "API Integration",
            "description": "Handles API integration tasks, analysis, design, testing, and optimization.",
            "icon": "🔗"
        },
        "documenter": {
            "name": "ReverseEngineer",
            "description": "Creates comprehensive documentation for generated code and processes.",
            "icon": "📚"
        },
        "prd_agent": {
            "name": "PRD Generator",
            "description": "Generates Product Requirements Documents with user stories and technical specifications.",
            "icon": "📄"
        },
        "qa_agent": {
            "name": "QA & Testing",
            "description": "Performs static code analysis, generates test cases, and checks compliance.",
            "icon": "🧪"
        }
    }

def load_predefined_mops():
    """Load predefined MOP files."""
    mops = {}
    
    # Add built-in test MOPs
    mops["Simple Calculator"] = """
MOP: Simple Calculator Application

Objective: Create a basic calculator that can perform arithmetic operations.

Steps:
1. Create functions for add, subtract, multiply, and divide
2. Create a main function that takes user input
3. Handle division by zero errors
4. Display results clearly
5. Allow multiple calculations

Expected Output:
A complete Python calculator script with error handling.
"""
    
    mops["File Processor"] = """
MOP: File Reading and Processing

Objective: Create a script that reads and processes text files.

Steps:
1. Create a function to read a text file
2. Count the number of lines and words
3. Find the most common words
4. Save results to an output file
5. Handle file not found errors

Expected Output:
A Python script for file processing with comprehensive error handling.
"""
    
    mops["Web Scraper"] = """
MOP: Simple Web Scraper

Objective: Create a basic web scraper to extract data from websites.

Steps:
1. Import required libraries (requests, BeautifulSoup)
2. Create a function to fetch web page content
3. Parse HTML and extract specific data
4. Save extracted data to CSV file
5. Add error handling for network issues

Expected Output:
A Python web scraping script with proper error handling.
"""
    
    # Try to load existing MOP files
    try:
        from mop_cisco_audit import MOP_CONTENT
        mops["Cisco XR Device Audit"] = MOP_CONTENT
    except ImportError:
        pass
    
    try:
        from PRDmop import PRD_MOP_CONTENT
        mops["PRD Generation"] = PRD_MOP_CONTENT
    except ImportError:
        pass
    
    return mops

# Claude API functions
def call_claude_api(prompt, task_type="general"):
    """Call Claude API with the given prompt."""
    try:
        import sys
        from pathlib import Path
        project_root = Path(__file__).parent.parent.parent.parent
        sys.path.insert(0, str(project_root))
        from config.config import API_KEY
        from anthropic import Anthropic
        
        client = Anthropic(api_key=API_KEY)
        
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=2000,
            messages=[{"role": "user", "content": prompt}]
        )
        
        return True, response.content[0].text
        
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        full_traceback = traceback.format_exc()
        return False, f"{error_msg}\n\nFull traceback:\n{full_traceback}"

def generate_code(mop_content, language="Python"):
    """Generate code using Claude API for the specified language, supporting multi-file output."""
    prompt = f"""
You are an expert {language} developer. Generate a complete {language} project based on the following input.

Input:
{mop_content}

If the project requires multiple files (e.g., React: .jsx and .css, Python: .py and requirements.txt), return a JSON object where each key is a filename and each value is the file content. Example:
{{
  "App.jsx": "...",
  "App.css": "...",
  "index.js": "..."
}}

If only one file is needed, return a JSON object with a single file.

Return only the JSON object, no explanation.
"""
    return call_claude_api(prompt, "code_generation")

def review_code(code):
    """Review code using Claude API."""
    prompt = f"""
You are a senior code reviewer. Please review the following expert software code and provide feedback on:
- Code quality and best practices
- Potential bugs or issues
- Suggestions for improvement
- Security considerations
- Performance optimizations

Code to review:
{code}

Provide a detailed review with specific recommendations.
"""
    return call_claude_api(prompt, "code_review")

def improve_code(code):
    """Improve code using Claude API."""
    prompt = f"""
You are an expert software developer. Please improve the following code by:
- Optimizing performance
- Adding better error handling
- Improving code structure
- Adding more comprehensive documentation
- Following expert software best practices

Original code:
{code}

Return the improved version of the code with explanations for major changes.
"""
    return call_claude_api(prompt, "code_improvement")

def generate_api_integration(mop_content):
    """Generate API integration code using Claude API."""
    prompt = f"""
You are an API integration expert. Based on the following MOP, create a Python script that demonstrates API integration concepts:

MOP Content:
{mop_content}

Generate code that includes:
- API client setup
- Request/response handling
- Error handling for API calls
- Data processing
- Configuration management

Return a complete Python script with API integration examples.
"""
    return call_claude_api(prompt, "api_integration")

def generate_documentation(code):
    """Generate documentation using Claude API."""
    prompt = f"""
You are a technical documentation expert. Create comprehensive documentation for the following Python code:

Code:
{code}

Generate documentation that includes:
- Overview and purpose
- Function descriptions
- Parameter explanations
- Usage examples
- Installation requirements
- Error handling notes

Format the documentation in Markdown.
"""
    return call_claude_api(prompt, "documentation")

def generate_prd(mop_content):
    """Generate PRD using Claude API with proper epic structure."""
    prompt = f"""
You are a technical writer specializing in PRD creation. Generate a comprehensive Product Requirements Document (PRD) based on the provided MOP content.

MOP Content:
{mop_content}

Create a PRD document with the following structure and format EXACTLY as shown:

# Product Requirements Document (PRD)

## Product Overview
[Provide a comprehensive overview of the product/feature]

## Business Objectives
[List the key business objectives]

## Target Users
[Define the target user personas]

## Functional Requirements

### Epic 1: [Epic Name]
[Epic description and scope]

**User Stories:**
- **US-001**: As a [user type], I want [functionality] so that [benefit]
- **US-002**: As a [user type], I want [functionality] so that [benefit]
- **US-003**: As a [user type], I want [functionality] so that [benefit]

**Acceptance Criteria:**
- [Specific acceptance criteria for Epic 1]
- [Additional criteria]

### Epic 2: [Epic Name]
[Epic description and scope]

**User Stories:**
- **US-004**: As a [user type], I want [functionality] so that [benefit]
- **US-005**: As a [user type], I want [functionality] so that [benefit]
- **US-006**: As a [user type], I want [functionality] so that [benefit]

**Acceptance Criteria:**
- [Specific acceptance criteria for Epic 2]
- [Additional criteria]

### Epic 3: [Epic Name]
[Epic description and scope]

**User Stories:**
- **US-007**: As a [user type], I want [functionality] so that [benefit]
- **US-008**: As a [user type], I want [functionality] so that [benefit]

**Acceptance Criteria:**
- [Specific acceptance criteria for Epic 3]
- [Additional criteria]

## Technical Requirements
[Detailed technical specifications]

## Non-Functional Requirements
[Performance, security, scalability requirements]

## Success Metrics
[Key performance indicators and success criteria]

## Timeline and Milestones
[Project timeline with key milestones]

## Dependencies and Assumptions
[External dependencies and key assumptions]

IMPORTANT:
- Use EXACTLY the format "### Epic X: [Epic Name]" for epic headers
- Use EXACTLY the format "**US-XXX**: [Description]" for user stories
- Include at least 3 epics with 2-3 user stories each
- Make sure epic numbers are sequential (Epic 1, Epic 2, Epic 3, etc.)
- Make sure user story numbers are sequential (US-001, US-002, etc.)
"""
    return call_claude_api(prompt, "prd_generation")

def create_jira_issues_from_prd():
    """Create Jira issues from PRD file with acceptance criteria."""
    try:
        from generated_scripts.create_jira_issues_from_prd import create_jira_issues_from_prd, test_prd_parsing

        # First test the parsing to see if acceptance criteria are found
        parsing_success = test_prd_parsing()

        if parsing_success:
            # Create the actual Jira issues
            create_jira_issues_from_prd()
            return True, "Jira epics and user stories created successfully with acceptance criteria included in descriptions"
        else:
            return False, "Failed to parse PRD file - check PRD format"

    except Exception as e:
        return False, f"Failed to create Jira issues: {str(e)}"

def create_jira_bug_ticket(test_output):
    """Create Jira bug ticket from test failures."""
    try:
        from local_agents.qa_agent import create_jira_bug
        create_jira_bug(test_output)
        return True, "Jira bug ticket created successfully"
    except Exception as e:
        return False, f"Failed to create Jira bug ticket: {str(e)}"

def check_jira_configuration():
    """Check if Jira is properly configured."""
    try:
        import sys
        from pathlib import Path
        project_root = Path(__file__).parent.parent.parent.parent
        sys.path.insert(0, str(project_root))
        from config.config import JIRA_API_CONFIG
        required_keys = ['base_url', 'api_key', 'email', 'project_key']

        for key in required_keys:
            if not JIRA_API_CONFIG.get(key):
                return False, f"Missing Jira configuration: {key}"

        return True, "Jira configuration is complete"
    except ImportError:
        return False, "Jira configuration not found in config.py"
    except Exception as e:
        return False, f"Jira configuration error: {str(e)}"

def generate_qa_tests(mop_content, code=None):
    """Generate QA tests using Claude API."""
    prompt = f"""
You are a QA engineer. Based on the following MOP{' and code' if code else ''}, create comprehensive test cases:

MOP Content:
{mop_content}

{f'Code to test:{chr(10)}{code}{chr(10)}' if code else ''}

Generate:
- Unit test cases
- Integration test scenarios
- Edge case testing
- Performance test considerations
- Security test cases

Provide test cases in Python using pytest framework.
"""
    return call_claude_api(prompt, "qa_testing")

GITHUB_TOKEN = "****************************************"
GITHUB_USERNAME = "SannithR"
GITHUB_API_URL = "https://github.com/"
GITHUB_PUSH_URL_TEMPLATE = "https://{username}:{token}@github.com/{username}/{repo}.git"

def get_repo_name_from_mop(mop_content):
    # Handle both string and dict types for mop_content
    if isinstance(mop_content, dict):
        # Use the first filename as base, or the first file's content if no filename
        if mop_content:
            first_key = next(iter(mop_content.keys()))
            base = first_key or "mop_repo"
        else:
            base = "mop_repo"
    elif isinstance(mop_content, str):
        # Use first non-empty line as base
        for line in mop_content.split("\n"):
            line = line.strip()
            if line:
                base = line
                break
        else:
            base = "mop_repo"
    else:
        base = "mop_repo"
    # Only allow alphanumeric, dash, underscore
    repo_name = re.sub(r'[^A-Za-z0-9_-]', '', base)
    repo_name = repo_name[:30]  # Limit length
    if not repo_name:
        repo_name = "mop_repo"
    return repo_name

def ensure_github_repo(repo_name):
    import requests
    headers = {"Authorization": f"token {GITHUB_TOKEN}", "Accept": "application/vnd.github.v3+json"}
    # Check if repo exists
    r = requests.get(f"https://api.github.com/repos/{GITHUB_USERNAME}/{repo_name}", headers=headers)
    if r.status_code == 404:
        # Create repo
        data = {"name": repo_name, "private": False, "auto_init": True}
        r = requests.post(f"https://api.github.com/user/repos", headers=headers, json=data)
        if r.status_code not in (201, 202):
            raise Exception(f"Failed to create GitHub repo: {r.text}")
    elif r.status_code != 200:
        raise Exception(f"Failed to check GitHub repo: {r.text}")

def push_to_github(local_path, repo_name, branch_name, commit_message):
    # Remove .git if exists (for clean init)
    git_dir = os.path.join(local_path, ".git")
    if os.path.exists(git_dir):
        shutil.rmtree(git_dir)
    repo = Repo.init(local_path)
    repo.index.add([f for f in os.listdir(local_path) if os.path.isfile(os.path.join(local_path, f))])
    repo.index.commit(commit_message)
    push_url = GITHUB_PUSH_URL_TEMPLATE.format(username=GITHUB_USERNAME, token=GITHUB_TOKEN, repo=repo_name)
    if branch_name not in repo.heads:
        repo.git.checkout('-b', branch_name)
    else:
        repo.git.checkout(branch_name)
    if 'origin' not in [remote.name for remote in repo.remotes]:
        repo.create_remote('origin', push_url)
    else:
        repo.delete_remote('origin')
        repo.create_remote('origin', push_url)
    repo.git.push('--set-upstream', 'origin', branch_name, force=True)

def push_to_github_with_permission(agent_name, file_path, repo_name, branch_name, commit_message):
    """Push to GitHub with user permission."""
    try:
        # Ensure GitHub repo exists
        ensure_github_repo(repo_name)
        
        # Create temp directory for this push
        temp_git_dir = f"./output/{repo_name}_git_{branch_name}"
        if os.path.exists(temp_git_dir):
            shutil.rmtree(temp_git_dir)
        os.makedirs(temp_git_dir, exist_ok=True)
        
        # Copy file to temp directory
        if os.path.exists(file_path):
            shutil.copy(file_path, os.path.join(temp_git_dir, os.path.basename(file_path)))
        
        # Push to GitHub
        push_to_github(temp_git_dir, repo_name, branch_name, commit_message)
        
        # Update session state to mark as pushed
        if agent_name not in st.session_state.github_permissions:
            st.session_state.github_permissions[agent_name] = {}
        st.session_state.github_permissions[agent_name]['pushed'] = True
        st.session_state.github_permissions[agent_name]['repo_url'] = f"https://github.com/{GITHUB_USERNAME}/{repo_name}"
        st.session_state.github_permissions[agent_name]['branch'] = branch_name
        
        return True, f"Successfully pushed to GitHub: {repo_name}/{branch_name}"
    except Exception as e:
        return False, f"Failed to push to GitHub: {str(e)}"

def execute_agent_with_permission(agent_name, agent_function, *args, **kwargs):
    """Execute an agent with user permission."""
    try:
        # Execute the agent function
        success, result = agent_function(*args, **kwargs)
        
        # Update session state to mark as executed
        if agent_name not in st.session_state.agent_permissions:
            st.session_state.agent_permissions[agent_name] = {}
        st.session_state.agent_permissions[agent_name]['executed'] = True
        st.session_state.agent_permissions[agent_name]['success'] = success
        
        return success, result
    except Exception as e:
        # Update session state to mark as failed
        if agent_name not in st.session_state.agent_permissions:
            st.session_state.agent_permissions[agent_name] = {}
        st.session_state.agent_permissions[agent_name]['executed'] = True
        st.session_state.agent_permissions[agent_name]['success'] = False
        st.session_state.agent_permissions[agent_name]['error'] = str(e)
        
        return False, f"Failed to execute {agent_name}: {str(e)}"

def main():
    """Main application function."""
    initialize_session_state()
    
    # Enhanced Header with beautiful design
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 class="main-header">🤖 MOP Agent Workflow UI</h1>
        <p style="font-size: 1.2rem; color: #6b7280; font-weight: 400; margin-top: -1rem;">
            Transform your Method of Procedures into intelligent workflows with AI-powered agents
        </p>
        <div style="width: 100px; height: 4px; background: linear-gradient(90deg, #667eea, #764ba2); margin: 1rem auto; border-radius: 2px;"></div>
    </div>
    """, unsafe_allow_html=True)
    
    # Enhanced sidebar with beautiful styling
    with st.sidebar:
        # Beautiful sidebar header
        st.markdown("""
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="font-size: 3rem; margin-bottom: 0.5rem;">📁</div>
            <h2 style="color: #374151; font-weight: 700; margin: 0;">MOP Selection</h2>
            <p style="color: #6b7280; font-size: 0.9rem; margin-top: 0.5rem;">Choose your Method of Procedure</p>
            <div style="width: 50px; height: 3px; background: linear-gradient(90deg, #667eea, #764ba2); margin: 1rem auto; border-radius: 2px;"></div>
        </div>
        """, unsafe_allow_html=True)

        # Enhanced MOP source selection
        st.markdown("""
        <div style="margin-bottom: 1rem;">
            <h4 style="color: #374151; font-weight: 600;">📋 Source Options</h4>
        </div>
        """, unsafe_allow_html=True)

        mop_source = st.radio(
            "",
            ["📤 Upload File", "🗂️ Upload Multiple Files", "🗂️ Upload Folder (ZIP)", "📚 Predefined MOPs", "✏️ Text Input"],
            key="mop_source_radio",
            help="Select how you want to provide your MOP content"
        )
        
        if mop_source == "📤 Upload File":
            st.markdown("""
            <div style="margin: 1rem 0;">
                <h5 style="color: #374151; font-weight: 600;">📤 Upload Your MOP File</h5>
                <p style="color: #6b7280; font-size: 0.9rem;">Support for any file type</p>
            </div>
            """, unsafe_allow_html=True)
            uploaded_file = st.file_uploader(
                "",
                type=None,  # Allow all file types
                help="Upload any file containing your MOP content"
            )
            if uploaded_file is not None:
                try:
                    content = uploaded_file.read().decode('utf-8')
                except Exception:
                    content = uploaded_file.read()  # fallback for binary
                st.session_state.mop_content = content
                st.session_state.mop_source = f"Uploaded: {uploaded_file.name}"
                st.success(f"✅ Loaded {uploaded_file.name}")

        elif mop_source == "🗂️ Upload Multiple Files":
            st.markdown("""
            <div style=\"margin: 1rem 0;\">
                <h5 style=\"color: #374151; font-weight: 600;\">🗂️ Upload Multiple Files</h5>
                <p style=\"color: #6b7280; font-size: 0.9rem;\">Select and upload multiple files at once (any type). All files will be processed as a folder.</p>
            </div>
            """, unsafe_allow_html=True)
            uploaded_files = st.file_uploader(
                "",
                type=None,
                accept_multiple_files=True,
                help="Upload multiple files to process as a folder."
            )
            if uploaded_files:
                folder_content = {}
                for f in uploaded_files:
                    try:
                        folder_content[f.name] = f.read().decode('utf-8')
                    except Exception:
                        folder_content[f.name] = f.read()  # fallback for binary
                st.session_state.mop_content = folder_content
                st.session_state.mop_source = f"Uploaded multiple files: {', '.join([f.name for f in uploaded_files])}"
                st.success(f"✅ Loaded {len(uploaded_files)} files")

        elif mop_source == "🗂️ Upload Folder (ZIP)":
            st.markdown("""
            <div style=\"margin: 1rem 0;\">
                <h5 style=\"color: #374151; font-weight: 600;\">🗂️ Upload Folder as ZIP</h5>
                <p style=\"color: #6b7280; font-size: 0.9rem;\">Upload a ZIP file containing multiple files as a folder. All files will be processed.</p>
            </div>
            """, unsafe_allow_html=True)
            import zipfile
            import io
            uploaded_zip = st.file_uploader(
                "",
                type=["zip"],
                help="Upload a ZIP file containing multiple files as a folder. All files will be processed."
            )
            if uploaded_zip is not None:
                try:
                    with zipfile.ZipFile(io.BytesIO(uploaded_zip.read())) as z:
                        file_list = z.namelist()
                        st.write(f"Files in ZIP: {file_list}")
                        folder_content = {}
                        for fname in file_list:
                            if not fname.endswith("/"):
                                try:
                                    folder_content[fname] = z.read(fname).decode('utf-8')
                                except Exception:
                                    folder_content[fname] = z.read(fname)  # fallback for binary
                        st.session_state.mop_content = folder_content
                        st.session_state.mop_source = f"Uploaded folder: {uploaded_zip.name}"
                        st.success(f"✅ Loaded folder {uploaded_zip.name} with {len(folder_content)} files")
                except Exception as e:
                    st.error(f"Failed to read ZIP: {e}")

        elif mop_source == "📚 Predefined MOPs":
            st.markdown("""
            <div style="margin: 1rem 0;">
                <h5 style="color: #374151; font-weight: 600;">📚 Choose Template</h5>
                <p style="color: #6b7280; font-size: 0.9rem;">Select from pre-built MOP templates</p>
            </div>
            """, unsafe_allow_html=True)

            predefined_mops = load_predefined_mops()
            if predefined_mops:
                selected_mop = st.selectbox(
                    "",
                    list(predefined_mops.keys()),
                    help="Choose a predefined MOP template"
                )
                if selected_mop:
                    st.session_state.mop_content = predefined_mops[selected_mop]
                    st.session_state.mop_source = f"Predefined: {selected_mop}"
                    st.success(f"✅ Loaded {selected_mop}")
            else:
                st.warning("No predefined MOPs available")

        elif mop_source == "✏️ Text Input":
            st.markdown("""
            <div style="margin: 1rem 0;">
                <h5 style="color: #374151; font-weight: 600;">✏️ Direct Input</h5>
                <p style="color: #6b7280; font-size: 0.9rem;">Paste or type your MOP content</p>
            </div>
            """, unsafe_allow_html=True)

            mop_text = st.text_area(
                "",
                height=200,
                placeholder="Paste your MOP content here...",
                help="Enter your Method of Procedure content directly"
            )
            if mop_text:
                st.session_state.mop_content = mop_text
                st.session_state.mop_source = "Text Input"
                st.success("✅ MOP content entered")
        
        # Display current MOP info
        if st.session_state.mop_content:
            st.info(f"📋 Current MOP: {st.session_state.mop_source}")
            with st.expander("Preview MOP Content"):
                st.text(st.session_state.mop_content[:500] + "..." if len(st.session_state.mop_content) > 500 else st.session_state.mop_content)

        # Enhanced Jira Integration Status
        st.markdown("""
        <div style="margin: 2rem 0 1rem 0;">
            <div style="text-align: center;">
                <div style="font-size: 2.5rem; margin-bottom: 0.5rem;">🎫</div>
                <h3 style="color: #374151; font-weight: 700; margin: 0;">Jira Integration</h3>
                <div style="width: 40px; height: 2px; background: linear-gradient(90deg, #667eea, #764ba2); margin: 0.5rem auto; border-radius: 1px;"></div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        jira_configured, jira_message = check_jira_configuration()

        if jira_configured:
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
                        padding: 1rem; border-radius: 12px; margin: 1rem 0;
                        border-left: 4px solid #10b981;">
                <p style="margin: 0; color: #065f46; font-weight: 600;">✅ {jira_message}</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("""
            <div style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                        padding: 1rem; border-radius: 12px; margin: 0.5rem 0;">
                <p style="margin: 0; color: #1e40af; font-weight: 500;">   PRD Agent: Creates summary dashboard + epics + user stories</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("""
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                        padding: 1rem; border-radius: 12px; margin: 0.5rem 0;">
                <p style="margin: 0; color: #92400e; font-weight: 500;">🐛 QA Agent: Creates bug tickets for test failures</p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
                        padding: 1rem; border-radius: 12px; margin: 1rem 0;
                        border-left: 4px solid #ef4444;">
                <p style="margin: 0; color: #991b1b; font-weight: 600;">❌ {jira_message}</p>
            </div>
            """, unsafe_allow_html=True)

            st.markdown("""
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                        padding: 1rem; border-radius: 12px; margin: 0.5rem 0;">
                <p style="margin: 0; color: #92400e; font-weight: 500;">⚠️ Jira features will be disabled</p>
            </div>
            """, unsafe_allow_html=True)

    # Main content area
    col1, col2 = st.columns([1, 1])

    with col1:
        # --- Agent Selection Header ---
        st.header("🤖 Agent Selection")

        # --- Allow agent selection and dynamic input even if no MOP is selected ---
        # Show a warning if no MOP is selected, but do NOT return/disable the UI
        if not st.session_state.mop_content:
            st.warning("⚠️ No MOP selected. You can still use Dynamic User Input to send any code or prompt (Python, Java, JavaScript, React, etc.) to the selected agents.")

        agent_descriptions = get_agent_descriptions()

        # Enhanced agent selection with beautiful cards
        st.markdown("""
        <div style="margin-bottom: 1.5rem;">
            <h3 style="color: #374151; font-weight: 600; margin-bottom: 1rem;">🎯 Select AI Agents</h3>
            <p style="color: #6b7280; margin-bottom: 1.5rem;">Choose the intelligent agents to process your MOP content</p>
        </div>
        """, unsafe_allow_html=True)

        selected_agents = set()

        # Create agent cards in a grid layout
        for i, (agent_key, agent_info) in enumerate(agent_descriptions.items()):
            # Create beautiful agent card
            is_selected = agent_key in st.session_state.selected_agents
            card_style = """
            <div class="agent-card" style="margin-bottom: 1rem; cursor: pointer;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div style="font-size: 2rem;">{icon}</div>
                    <div style="flex: 1;">
                        <h4 style="margin: 0; color: #374151; font-weight: 600;">{name}</h4>
                        <p style="margin: 0.5rem 0 0 0; color: #6b7280; font-size: 0.9rem;">{description}</p>
                    </div>
                </div>
            </div>
            """.format(
                icon=agent_info['icon'],
                name=agent_info['name'],
                description=agent_info['description']
            )

            st.markdown(card_style, unsafe_allow_html=True)

            # Checkbox for selection
            if st.checkbox(
                f"Enable {agent_info['name']}",
                key=f"agent_{agent_key}",
                value=is_selected,
                help=f"Click to enable/disable {agent_info['name']}"
            ):
                selected_agents.add(agent_key)

        st.session_state.selected_agents = selected_agents

        # --- Dynamic User Input Section ---
        st.markdown("""
        <div style="margin: 1.5rem 0 1rem 0;">
            <h4 style="color: #374151; font-weight: 600;">📝 Dynamic User Input</h4>
            <p style="color: #6b7280; font-size: 0.95rem;">Enter any code or prompt to send to all selected agents below. Select the language for code generation.</p>
        </div>
        """, unsafe_allow_html=True)
        # Language selection for code generation
        code_language = st.selectbox(
            "Select Code Language (for Code Generator agent)",
            ["Python", "Java", "JavaScript", "React", "Angular","Other"],
            key="dynamic_code_language",
            help="Choose the language you want the Code Generator agent to use."
        )
        dynamic_user_input = st.text_area(
            "Dynamic Input for Agents",
            key="dynamic_user_input",
            height=100,
            placeholder="Type your custom prompt or input here..."
        )
        # Show the current dynamic user input below the input area
        if dynamic_user_input:
            st.info(f"Current Dynamic Input: {dynamic_user_input}")

        # Determine code input for review/improve: dynamic input > uploaded file > MOP
        code_input = dynamic_user_input.strip() if dynamic_user_input.strip() else (st.session_state.mop_content.strip() if st.session_state.mop_source and st.session_state.mop_source.startswith("Uploaded:") else "")

        if st.button("SUBMIT", disabled=not (selected_agents and (dynamic_user_input or (st.session_state.mop_source and st.session_state.mop_source.startswith('Uploaded:'))))):
            st.session_state.workflow_running = True
            st.session_state.workflow_results = {}
            st.session_state.pending_agent_executions = {}
            st.session_state.agent_permissions = {}
            
            try:
                # Store agent execution information for permission requests
                for agent_key in selected_agents:
                    agent_descriptions = get_agent_descriptions()
                    agent_name = agent_descriptions[agent_key]['name']
                    
                    if agent_key == "coder":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_code,
                            'args': [dynamic_user_input, code_language],
                            'description': 'Generate code in the selected language'
                        }
                    elif agent_key == "reviewer":
                        # Always show permission for Code Reviewer, even if no code input
                        if code_input:
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': review_code,
                                'args': [code_input],
                                'description': 'Review the provided code for quality and best practices'
                            }
                        else:
                            # Show permission even when no code input, but with a warning
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': review_code,
                                'args': [code_input],
                                'description': 'Review the provided code for quality and best practices (⚠️ No code input detected - will use uploaded file or MOP content)'
                            }
                    elif agent_key == "improver":
                        # Always show permission for Code Improver, even if no code input
                        if code_input:
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': improve_code,
                                'args': [code_input],
                                'description': 'Improve and optimize the provided code'
                            }
                        else:
                            # Show permission even when no code input, but with a warning
                            st.session_state.pending_agent_executions[agent_name] = {
                                'agent_key': agent_key,
                                'function': improve_code,
                                'args': [code_input],
                                'description': 'Improve and optimize the provided code (⚠️ No code input detected - will use uploaded file or MOP content)'
                            }
                    elif agent_key == "integrator":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_api_integration,
                            'args': [dynamic_user_input],
                            'description': 'Generate API integration code and examples'
                        }
                    elif agent_key == "documenter":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_documentation,
                            'args': [dynamic_user_input],
                            'description': 'Generate comprehensive documentation'
                        }
                    elif agent_key == "prd_agent":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_prd,
                            'args': [dynamic_user_input],
                            'description': 'Generate Product Requirements Document with epics and user stories'
                        }
                    elif agent_key == "qa_agent":
                        st.session_state.pending_agent_executions[agent_name] = {
                            'agent_key': agent_key,
                            'function': generate_qa_tests,
                            'args': [dynamic_user_input],
                            'description': 'Generate QA tests and perform code analysis'
                        }
                
                st.session_state.workflow_running = False
                st.rerun()
            except Exception as e:
                st.session_state.workflow_running = False
                st.error(f"❌ Failed to process dynamic input: {str(e)}")

        # Run workflow button
        if st.button("🚀 Run Workflow", disabled=st.session_state.workflow_running or not selected_agents):
            run_workflow()

    with col2:
        # Enhanced results header
        st.markdown("""
        <div style="margin-bottom: 1.5rem;">
            <h3 style="color: #374151; font-weight: 600; margin-bottom: 0.5rem;">📊 Results & Progress</h3>
            <p style="color: #6b7280; margin-bottom: 1rem;">Monitor your workflow execution and download results</p>
        </div>
        """, unsafe_allow_html=True)

        # Enhanced workflow controls
        col_clear, col_status = st.columns([1, 2])

        with col_clear:
            if st.button("🗑️ Clear Results", disabled=st.session_state.workflow_running):
                st.session_state.workflow_results = {}
                st.session_state.pending_github_pushes = {}
                st.session_state.github_permissions = {}
                st.session_state.pending_agent_executions = {}
                st.session_state.agent_permissions = {}
                st.rerun()

        if st.session_state.workflow_running:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
                        padding: 1rem; border-radius: 12px; margin: 1rem 0;
                        border-left: 4px solid #f59e0b;">
                <p class="status-running">🔄 Workflow is running...</p>
            </div>
            """, unsafe_allow_html=True)
            st.progress(0.5)

        # Display results with beautiful styling
        if st.session_state.workflow_results:
            # Enhanced summary statistics with beautiful cards
            total_agents = len(st.session_state.workflow_results)
            successful_agents = sum(1 for result in st.session_state.workflow_results.values() if result.get('status') == 'success')
            failed_agents = total_agents - successful_agents

            # Beautiful metrics cards
            st.markdown("""
            <div style="margin: 1.5rem 0;">
                <h4 style="color: #374151; font-weight: 600; margin-bottom: 1rem;">📈 Execution Summary</h4>
            </div>
            """, unsafe_allow_html=True)

            col_stats1, col_stats2, col_stats3 = st.columns(3)

            with col_stats1:
                st.markdown(f"""
                <div class="metric-card">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📊</div>
                    <div style="font-size: 2rem; font-weight: 700; color: #374151;">{total_agents}</div>
                    <div style="color: #6b7280; font-weight: 500;">Total Agents</div>
                </div>
                """, unsafe_allow_html=True)

            with col_stats2:
                st.markdown(f"""
                <div class="metric-card">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">✅</div>
                    <div style="font-size: 2rem; font-weight: 700; color: #10b981;">{successful_agents}</div>
                    <div style="color: #6b7280; font-weight: 500;">Successful</div>
                </div>
                """, unsafe_allow_html=True)

            with col_stats3:
                st.markdown(f"""
                <div class="metric-card">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">❌</div>
                    <div style="font-size: 2rem; font-weight: 700; color: #ef4444;">{failed_agents}</div>
                    <div style="color: #6b7280; font-weight: 500;">Failed</div>
                </div>
                """, unsafe_allow_html=True)

            st.markdown("<br>", unsafe_allow_html=True)

            # Results display
            for agent_name, result in st.session_state.workflow_results.items():
                with st.expander(f"📋 {agent_name} Results", expanded=True):
                    if result.get('status') == 'success':
                        st.markdown('<p class="status-success">✅ Completed Successfully</p>', unsafe_allow_html=True)

                        # Output display
                        output_text = result.get('output', '')

                        if agent_name == 'Code Generator':
                            output = result.get('output', '')
                            if isinstance(output, dict):
                                for filename, content in output.items():
                                    st.subheader(f"{filename}")
                                    display_content = to_display_string(content)
                                    st.code(display_content, language=detect_language(filename))
                                    st.download_button(
                                        f"📥 Download {filename}",
                                        display_content,
                                        file_name=filename,
                                        key=f"download_{agent_name}_{filename}",
                                        help=f"Download {filename}"
                                    )
                            else:
                                st.subheader("Generated Code:")
                                st.code(output, language='python')
                        else:
                            st.text_area(
                                f"{agent_name} Output",
                                to_display_string(output_text),
                                height=200,
                                key=f"result_{agent_name}",
                                help="Click to select all text, then Ctrl+C to copy"
                            )

                        # Download button for outputs
                        if output_text:
                            file_extension = "py" if agent_name == 'Code Generator' else "txt"
                            file_name = f"{agent_name.lower().replace(' ', '_')}_output.{file_extension}"

                            st.download_button(
                                f"📥 Download {agent_name} Output",
                                to_display_string(output_text),
                                file_name=file_name,
                                key=f"download_{agent_name}",
                                help=f"Download the {agent_name} output"
                            )

                        # Show file info
                        if result.get('file_path') and os.path.exists(result['file_path']):
                            file_size = len(output_text.encode('utf-8'))
                            st.caption(f"📄 File saved: {result['file_path']} ({file_size} bytes)")
                            
                            # --- Step-by-step GitHub Permission Dialog ---
                            # Improved: Show all pending agents at once (no queue/index)
                            pending_agents = [agent for agent in st.session_state.pending_github_pushes.keys() if agent not in st.session_state.github_permissions]
                            for pending_agent in pending_agents:
                                push_info = st.session_state.pending_github_pushes[pending_agent]
                                accept_key = f"accept_{pending_agent}_{push_info['branch_name']}"
                                reject_key = f"reject_{pending_agent}_{push_info['branch_name']}"
                                st.markdown(f"""
                                <div style="margin: 1rem 0; padding: 1rem; background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); 
                                            border-radius: 12px; border-left: 4px solid #3b82f6;">
                                    <h5 style="margin: 0 0 0.5rem 0; color: #1e40af; font-weight: 600;">🚀 GitHub Push Permission</h5>
                                    <p style="margin: 0; color: #1e40af; font-size: 0.9rem;">
                                        Push these files to GitHub repository: <strong>{push_info['repo_name']}/{push_info['branch_name']}</strong>
                                    </p>
                                </div>
                                """, unsafe_allow_html=True)
                                if 'file_paths' in push_info:
                                    for fp in push_info['file_paths']:
                                        st.caption(f"📄 {os.path.basename(fp)}")
                                else:
                                    st.caption(f"📄 {os.path.basename(push_info['file_path'])}")
                                col_accept, col_reject = st.columns(2)
                                accept_clicked = col_accept.button("✅ Accept Push", key=accept_key, help=f"Push {pending_agent} results to GitHub")
                                reject_clicked = col_reject.button("❌ Reject Push", key=reject_key, help=f"Skip pushing {pending_agent} results to GitHub")
                                if accept_clicked:
                                    # Multi-file push logic
                                    def multi_file_push(file_paths, repo_name, branch_name, commit_message):
                                        ensure_github_repo(repo_name)
                                        temp_git_dir = f"./output/{repo_name}_git_{branch_name}"
                                        if os.path.exists(temp_git_dir):
                                            shutil.rmtree(temp_git_dir)
                                        os.makedirs(temp_git_dir, exist_ok=True)
                                        for fp in file_paths:
                                            shutil.copy(fp, os.path.join(temp_git_dir, os.path.basename(fp)))
                                        push_to_github(temp_git_dir, repo_name, branch_name, commit_message)
                                    try:
                                        if 'file_paths' in push_info:
                                            multi_file_push(push_info['file_paths'], push_info['repo_name'], push_info['branch_name'], push_info['commit_message'])
                                        else:
                                            push_to_github_with_permission(
                                                pending_agent,
                                                push_info['file_path'],
                                                push_info['repo_name'],
                                                push_info['branch_name'],
                                                push_info['commit_message']
                                            )
                                        st.success(f"✅ Successfully pushed to GitHub: {push_info['repo_name']}/{push_info['branch_name']}")
                                        st.session_state.github_permissions[pending_agent] = {'pushed': True, 'rejected': False, 'repo_url': f"https://github.com/{GITHUB_USERNAME}/{push_info['repo_name']}", 'branch': push_info['branch_name']}
                                    except Exception as e:
                                        st.error(f"❌ Failed to push: {e}")
                                        st.session_state.github_permissions[pending_agent] = {'pushed': False, 'rejected': True}
                                    st.rerun()
                                if reject_clicked:
                                    st.session_state.github_permissions[pending_agent] = {'pushed': False, 'rejected': True}
                                    st.rerun()
                            # Show status of previous decisions
                            for agent, permission_status in st.session_state.github_permissions.items():
                                if permission_status.get('pushed'):
                                    st.success(f"✅ Pushed to GitHub: {permission_status.get('repo_url', 'Unknown')}")
                                elif permission_status.get('rejected'):
                                    st.info(f"⏭️ GitHub push was rejected for {agent}")

                        # Show Jira integration status for PRD and QA agents
                        if agent_name == 'PRD Generator' and 'jira_status' in result:
                            if result['jira_status']:
                                st.success(f"🎫 Jira Issues Created: {result['jira_message']}")
                                st.info("✅ User stories created with meaningful titles extracted from PRD")
                                st.info("✅ Acceptance criteria from PRD included in user story descriptions")

                                # Show example of what was created
                                with st.expander("📋 View Jira Integration Details"):
                                    st.markdown("""
                                    **What was created in Jira:**
                                    - 📊 **Summary Dashboard**: Overview epic with project statistics and navigation
                                    - 📌 **Functional Epics**: Created with descriptive titles from PRD sections
                                    - 📝 **User Stories**: Created with format `US-XXX: [Meaningful Title]`
                                    - 📋 **Descriptions**: Include original user story + numbered acceptance criteria
                                    - 🔗 **Links**: User stories linked to their respective epics

                                    **Summary Dashboard Epic includes:**
                                    - 🎯 Product overview from PRD
                                    - 📊 Project statistics (epic/story counts)
                                    - 📌 Epic breakdown with story counts
                                    - 🔗 Quick navigation links
                                    - 📅 Creation timestamp

                                    **Example User Story Title:**
                                    `Establish a secure SSH connection to Cisco XR device`

                                    **Example Description:**
                                    ```
                                    **US-001**: As a Network Administrator, I want to establish a secure SSH connection...

                                    **Acceptance Criteria:**
                                    1. The audit process can only be initiated through a secure SSH connection
                                    2. The provided access credentials are successfully used to authenticate
                                    3. The secure connection is maintained throughout the entire audit process
                                    ```

                                    **💡 Tip:** Pin the "📊 PRD Summary Dashboard" epic to your Jira board for easy project overview!
                                    """)
                            else:
                                st.warning(f"⚠️ Jira Integration: {result['jira_message']}")

                        elif agent_name == 'QA & Testing' and 'jira_status' in result:
                            if result.get('has_failures'):
                                if result['jira_status']:
                                    st.error(f"🐛 Issues Found - Jira Bug Created: {result['jira_message']}")
                                else:
                                    st.error(f"🐛 Issues Found - Jira Bug Failed: {result['jira_message']}")
                            else:
                                st.success("✅ No issues detected - All tests passed")

                    elif result.get('status') == 'error':
                        st.markdown('<p class="status-error">❌ Error Occurred</p>', unsafe_allow_html=True)
                        st.error(result.get('error', 'Unknown error'))

                        # Show error details in expandable section
                        if result.get('error_details'):
                            with st.expander("🔍 Error Details"):
                                st.code(result['error_details'], language='text')

        else:
            st.info("👆 Select agents and run the workflow to see results here")

        # Display pending agent executions that require permission
        if st.session_state.pending_agent_executions:
            pending_count = len(st.session_state.pending_agent_executions)
            st.markdown(f"""
            <div style="margin: 2rem 0 1rem 0;">
                <h3 style="color: #374151; font-weight: 600; margin-bottom: 1rem;">🤖 Agent Execution Permissions</h3>
                <p style="color: #6b7280; margin-bottom: 1rem;">Review and approve agent executions before they run ({pending_count} agent{'s' if pending_count != 1 else ''} pending)</p>
            </div>
            """, unsafe_allow_html=True)

            for agent_name, execution_info in st.session_state.pending_agent_executions.items():
                # Check if permission has already been granted or denied
                if agent_name not in st.session_state.agent_permissions:
                    # Get additional info for display
                    agent_key = execution_info['agent_key']
                    input_info = ""
                    
                    # Define agent-specific styling
                    agent_icons = {
                        "coder": "💻",
                        "reviewer": "🔍", 
                        "improver": "✨",
                        "integrator": "🔗",
                        "documenter": "📚",
                        "prd_agent": "📄",
                        "qa_agent": "🧪"
                    }
                    
                    agent_colors = {
                        "coder": "#3b82f6",  # Blue
                        "reviewer": "#10b981",  # Green
                        "improver": "#f59e0b",  # Yellow
                        "integrator": "#8b5cf6",  # Purple
                        "documenter": "#06b6d4",  # Cyan
                        "prd_agent": "#ef4444",  # Red
                        "qa_agent": "#f97316"  # Orange
                    }
                    
                    icon = agent_icons.get(agent_key, "🤖")
                    color = agent_colors.get(agent_key, "#6b7280")
                    
                    if agent_key in ["reviewer", "improver"]:
                        # Show what code will be reviewed/improved
                        code_input = execution_info['args'][0] if execution_info['args'] else ""
                        if code_input:
                            input_preview = code_input[:100] + "..." if len(code_input) > 100 else code_input
                            input_info = f"<br><strong>Input Preview:</strong> <code style='background: #f3f4f6; padding: 2px 4px; border-radius: 4px;'>{input_preview}</code>"
                        else:
                            input_info = "<br><strong>⚠️ No code input detected</strong> - Will use uploaded file or MOP content"
                    elif agent_key == "coder":
                        # Show what will be used for code generation
                        dynamic_input = st.session_state.get('dynamic_user_input', '')
                        if dynamic_input:
                            input_preview = dynamic_input[:100] + "..." if len(dynamic_input) > 100 else dynamic_input
                            input_info = f"<br><strong>Input Preview:</strong> <code style='background: #f3f4f6; padding: 2px 4px; border-radius: 4px;'>{input_preview}</code>"
                        else:
                            input_info = "<br><strong>⚠️ No dynamic input detected</strong> - Will use uploaded file or MOP content"
                    
                    st.markdown(f"""
                    <div style="margin: 1rem 0; padding: 1.5rem; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); 
                                border-radius: 12px; border-left: 4px solid {color};">
                        <h5 style="margin: 0 0 0.5rem 0; color: #92400e; font-weight: 600;">{icon} {agent_name} Execution Permission</h5>
                        <p style="margin: 0; color: #92400e; font-size: 0.9rem;">
                            <strong>Description:</strong> {execution_info['description']}
                        </p>
                        <p style="margin: 0.5rem 0 0 0; color: #92400e; font-size: 0.9rem;">
                            <strong>Function:</strong> {execution_info['function'].__name__}
                        </p>
                        {input_info}
                    </div>
                    """, unsafe_allow_html=True)
                    
                    col_accept, col_reject = st.columns(2)
                    with col_accept:
                        if st.button(f"✅ Execute {agent_name}", key=f"execute_{agent_name}", 
                                   help=f"Execute {agent_name} with the provided parameters"):
                            success, result = execute_agent_with_permission(
                                agent_name,
                                execution_info['function'],
                                *execution_info['args']
                            )
                            
                            if success:
                                # Process the result based on agent type
                                agent_key = execution_info['agent_key']
                                
                                if agent_key == "coder":
                                    # Save generated code with correct extension(s)
                                    os.makedirs("./output", exist_ok=True)
                                    file_paths, files_dict = handle_generated_files(result, output_dir="./output")
                                    # Store GitHub push information for later permission (all files)
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "codegen"
                                    commit_message = "Add generated code from agent"
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_paths': file_paths,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': files_dict,
                                        'file_paths': file_paths,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "reviewer":
                                    # Save review result as a file
                                    os.makedirs("./output", exist_ok=True)
                                    review_file = "./output/review.txt"
                                    with open(review_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "review"
                                    commit_message = "Add code review feedback from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': review_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': review_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "improver":
                                    # Save improved code as a file
                                    os.makedirs("./output", exist_ok=True)
                                    improve_file = "./output/improved_code.py"
                                    with open(improve_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "improve"
                                    commit_message = "Add improved code from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': improve_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': improve_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "integrator":
                                    # Save API integration result as a file
                                    os.makedirs("./output", exist_ok=True)
                                    api_file = "./output/api_integration.txt"
                                    with open(api_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "api_integration"
                                    commit_message = "Add API integration result from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': api_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': api_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "documenter":
                                    # Save documentation as a file
                                    os.makedirs("./output", exist_ok=True)
                                    doc_file = "./output/documentation.md"
                                    with open(doc_file, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "documentation"
                                    commit_message = "Add documentation from agent"
                                    
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': doc_file,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': result,
                                        'file_path': doc_file,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name
                                    }
                                elif agent_key == "prd_agent":
                                    # Save PRD to file
                                    os.makedirs("./output", exist_ok=True)
                                    prd_file_path = "./output/prd.md"
                                    with open(prd_file_path, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "prd"
                                    commit_message = "Add PRD from agent"
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': prd_file_path,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    # Try to create Jira issues from PRD
                                    jira_success, jira_message = create_jira_issues_from_prd()
                                    
                                    # Append Jira status to output
                                    final_output = result
                                    if jira_success:
                                        final_output += f"\n\n✅ **Jira Integration**: {jira_message}"
                                    else:
                                        final_output += f"\n\n⚠️ **Jira Integration**: {jira_message}"
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': final_output,
                                        'file_path': prd_file_path,
                                        'jira_status': jira_success,
                                        'jira_message': jira_message
                                    }
                                elif agent_key == "qa_agent":
                                    # Save QA results to file
                                    os.makedirs("./output", exist_ok=True)
                                    qa_file_path = "./output/qa_results.txt"
                                    with open(qa_file_path, "w", encoding="utf-8") as f:
                                        f.write(result)
                                    
                                    # Store GitHub push information for later permission
                                    repo_name = get_repo_name_from_mop(st.session_state.mop_content)
                                    branch_name = "qa"
                                    commit_message = "Add QA results from agent"
                                    st.session_state.pending_github_pushes[agent_name] = {
                                        'file_path': qa_file_path,
                                        'repo_name': repo_name,
                                        'branch_name': branch_name,
                                        'commit_message': commit_message
                                    }
                                    
                                    # Check if there are test failures or issues that need Jira tickets
                                    has_failures = any(keyword in result.lower() for keyword in ['failed', 'error', 'bug', 'issue', 'failure'])
                                    final_output = result
                                    jira_success = False
                                    jira_message = "No issues detected - no Jira ticket needed"
                                    if has_failures:
                                        # Create Jira bug ticket for test failures
                                        jira_success, jira_message = create_jira_bug_ticket(result)
                                        if jira_success:
                                            final_output += f"\n\n🐛 **Jira Bug Ticket**: {jira_message}"
                                        else:
                                            final_output += f"\n\n⚠️ **Jira Bug Ticket Failed**: {jira_message}"
                                    
                                    st.session_state.workflow_results[agent_name] = {
                                        'status': 'success',
                                        'output': final_output,
                                        'file_path': qa_file_path,
                                        'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                                        'github_branch': branch_name,
                                        'jira_status': jira_success,
                                        'jira_message': jira_message,
                                        'has_failures': has_failures
                                    }
                                
                                # Remove from pending executions
                                del st.session_state.pending_agent_executions[agent_name]
                                st.success(f"✅ {agent_name} executed successfully!")
                            else:
                                st.error(f"❌ {agent_name} execution failed: {result}")
                            
                            st.rerun()
                    
                    with col_reject:
                        if st.button(f"❌ Skip {agent_name}", key=f"skip_{agent_name}",
                                   help=f"Skip executing {agent_name}"):
                            st.session_state.agent_permissions[agent_name] = {'executed': False, 'skipped': True}
                            # Remove from pending executions
                            del st.session_state.pending_agent_executions[agent_name]
                            st.info(f"⏭️ Skipped executing {agent_name}")
                            st.rerun()
                else:
                    # Show status of previous decision
                    permission_status = st.session_state.agent_permissions[agent_name]
                    if permission_status.get('executed') and permission_status.get('success'):
                        st.success(f"✅ {agent_name} executed successfully")
                    elif permission_status.get('executed') and not permission_status.get('success'):
                        st.error(f"❌ {agent_name} execution failed: {permission_status.get('error', 'Unknown error')}")
                    elif permission_status.get('skipped'):
                        st.info(f"⏭️ {agent_name} execution was skipped")

def run_workflow():
    """Execute the selected workflow."""
    st.session_state.workflow_running = True
    st.session_state.workflow_results = {}

    try:
        # Execute workflow based on selected agents
        execute_agent_workflow(st.session_state.selected_agents, st.session_state.mop_content)

    except Exception as e:
        st.error(f"❌ Workflow execution failed: {str(e)}")
        st.exception(e)
    finally:
        st.session_state.workflow_running = False
        st.rerun()

def execute_agent_workflow(selected_agents, mop_content):
    """Execute the workflow with selected agents."""
    generated_code = None

    # Code Generation
    if "coder" in selected_agents:
        try:
            success, result = generate_code(mop_content)
            if success:
                generated_code = result
                # Save generated code
                os.makedirs("./output", exist_ok=True)
                with open("./output/generated_code.py", "w") as f:
                    f.write(result)

                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "codegen"
                commit_message = "Add generated code from agent"
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['Code Generator'] = {
                    'file_path': './output/generated_code.py',
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---

                st.session_state.workflow_results['Code Generator'] = {
                    'status': 'success',
                    'output': result,
                    'file_path': './output/generated_code.py',
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Code Generator'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Code Generator'] = {
                'status': 'error',
                'error': str(e)
            }

    # Code Review
    if "reviewer" in selected_agents and generated_code:
        try:
            success, result = review_code(generated_code)
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "review"
                commit_message = "Add code review feedback from agent"
                
                # Save review result as a file
                os.makedirs("./output", exist_ok=True)
                review_file = "./output/review.txt"
                with open(review_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['Code Reviewer'] = {
                    'file_path': review_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['Code Reviewer'] = {
                    'status': 'success',
                    'output': result,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Code Reviewer'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Code Reviewer'] = {
                'status': 'error',
                'error': str(e)
            }

    # Code Improvement
    if "improver" in selected_agents and generated_code:
        try:
            success, result = improve_code(generated_code)
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "improve"
                commit_message = "Add improved code from agent"
                
                # Save improved code as a file
                os.makedirs("./output", exist_ok=True)
                improve_file = "./output/improved_code.py"
                with open(improve_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['Code Improver'] = {
                    'file_path': improve_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['Code Improver'] = {
                    'status': 'success',
                    'output': result,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Code Improver'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Code Improver'] = {
                'status': 'error',
                'error': str(e)
            }

    # API Integration
    if "integrator" in selected_agents:
        try:
            success, result = generate_api_integration(mop_content)
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "api_integration"
                commit_message = "Add API integration result from agent"
                
                # Save API integration result as a file
                os.makedirs("./output", exist_ok=True)
                api_file = "./output/api_integration.txt"
                with open(api_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['API Integration'] = {
                    'file_path': api_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['API Integration'] = {
                    'status': 'success',
                    'output': result,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['API Integration'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['API Integration'] = {
                'status': 'error',
                'error': str(e)
            }

    # Documentation
    if "documenter" in selected_agents and generated_code:
        try:
            success, result = generate_documentation(generated_code)
            if success:
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "documentation"
                commit_message = "Add documentation from agent"
                
                # Save documentation as a file
                os.makedirs("./output", exist_ok=True)
                doc_file = "./output/documentation.md"
                with open(doc_file, "w", encoding="utf-8") as f:
                    f.write(result)
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['Documentation'] = {
                    'file_path': doc_file,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                st.session_state.workflow_results['Documentation'] = {
                    'status': 'success',
                    'output': result,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name
                }
            else:
                st.session_state.workflow_results['Documentation'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['Documentation'] = {
                'status': 'error',
                'error': str(e)
            }

    # PRD Generation
    if "prd_agent" in selected_agents:
        try:
            # Try to use the original PRD agent first for better epic formatting
            try:
                import sys
        from pathlib import Path
        project_root = Path(__file__).parent.parent.parent.parent
        sys.path.insert(0, str(project_root))
        from config.config import API_KEY
                from anthropic import Anthropic
                from local_agents.prd_agent import PRDAgent

                # Use original PRD agent
                claude_client = Anthropic(api_key=API_KEY)
                prd_agent = PRDAgent(claude_client=claude_client, name="PRDAgent", api_key=API_KEY)

                # Save PRD to file using original agent
                os.makedirs("./output", exist_ok=True)
                prd_file_path = "./output/prd.md"
                result_message = prd_agent.generate_prd(mop_content=mop_content, output_file=prd_file_path)

                # Read the generated PRD content
                with open(prd_file_path, 'r', encoding='utf-8') as f:
                    prd_content = f.read()

                success = True
                result = prd_content

            except Exception as original_agent_error:
                # Fallback to Claude API direct call
                success, result = generate_prd(mop_content)
                if success:
                    # Save PRD to file
                    os.makedirs("./output", exist_ok=True)
                    prd_file_path = "./output/prd.md"
                    with open(prd_file_path, "w", encoding="utf-8") as f:
                        f.write(result)

            if success:
                # Try to create Jira issues from PRD
                jira_success, jira_message = create_jira_issues_from_prd()

                # Append Jira status to output
                final_output = result
                if jira_success:
                    final_output += f"\n\n✅ **Jira Integration**: {jira_message}"
                else:
                    final_output += f"\n\n⚠️ **Jira Integration**: {jira_message}"

                st.session_state.workflow_results['PRD Generator'] = {
                    'status': 'success',
                    'output': final_output,
                    'file_path': prd_file_path,
                    'jira_status': jira_success,
                    'jira_message': jira_message
                }
            else:
                st.session_state.workflow_results['PRD Generator'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['PRD Generator'] = {
                'status': 'error',
                'error': str(e)
            }

    # QA & Testing
    if "qa_agent" in selected_agents:
        try:
            success, result = generate_qa_tests(mop_content, generated_code)
            if success:
                # Save QA results to file
                os.makedirs("./output", exist_ok=True)
                qa_file_path = "./output/qa_results.txt"
                with open(qa_file_path, "w", encoding="utf-8") as f:
                    f.write(result)
                # --- GitHub Integration (Pending Permission) ---
                repo_name = get_repo_name_from_mop(mop_content)
                branch_name = "qa"
                commit_message = "Add QA results from agent"
                
                # Store GitHub push information for later permission
                st.session_state.pending_github_pushes['QA & Testing'] = {
                    'file_path': qa_file_path,
                    'repo_name': repo_name,
                    'branch_name': branch_name,
                    'commit_message': commit_message
                }
                # --- End GitHub Integration ---
                # Check if there are test failures or issues that need Jira tickets
                has_failures = any(keyword in result.lower() for keyword in ['failed', 'error', 'bug', 'issue', 'failure'])
                final_output = result
                jira_success = False
                jira_message = "No issues detected - no Jira ticket needed"
                if has_failures:
                    # Create Jira bug ticket for test failures
                    jira_success, jira_message = create_jira_bug_ticket(result)
                    if jira_success:
                        final_output += f"\n\n🐛 **Jira Bug Ticket**: {jira_message}"
                    else:
                        final_output += f"\n\n⚠️ **Jira Bug Ticket Failed**: {jira_message}"
                st.session_state.workflow_results['QA & Testing'] = {
                    'status': 'success',
                    'output': final_output,
                    'file_path': qa_file_path,
                    'github_repo': f"https://github.com/{GITHUB_USERNAME}/{repo_name}",
                    'github_branch': branch_name,
                    'jira_status': jira_success,
                    'jira_message': jira_message,
                    'has_failures': has_failures
                }
            else:
                st.session_state.workflow_results['QA & Testing'] = {
                    'status': 'error',
                    'error': result
                }
        except Exception as e:
            st.session_state.workflow_results['QA & Testing'] = {
                'status': 'error',
                'error': str(e)
            }
def push_prd_to_github(mop_content):
    """Push the prd.md file to GitHub for all agents."""
    try:
        repo_name = get_repo_name_from_mop(mop_content)
        ensure_github_repo(repo_name)
        prd_file_path = './output/prd.md'
        if not os.path.exists(prd_file_path):
            raise FileNotFoundError('prd.md file not found in ./output')
        # Push for each agent branch
        agent_branches = [
            'codegen', 'review', 'improve', 'qa', 'prd', 'documentation', 'api_integration'
        ]
        for branch_name in agent_branches:
            temp_git_dir = f"./output/{repo_name}_git_{branch_name}"
            if os.path.exists(temp_git_dir):
                shutil.rmtree(temp_git_dir)
            os.makedirs(temp_git_dir, exist_ok=True)
            shutil.copy(prd_file_path, os.path.join(temp_git_dir, "prd.md"))
            commit_message = f"Add prd.md for {branch_name} agent"
            push_to_github(temp_git_dir, repo_name, branch_name, commit_message)
        return True, f"prd.md pushed to GitHub for all agent branches: {', '.join(agent_branches)}"
    except Exception as e:
        return False, f"Failed to push prd.md: {str(e)}"

def handle_generated_files(result, output_dir="./output"):
    os.makedirs(output_dir, exist_ok=True)
    try:
        files_dict = json.loads(result)
        file_paths = []
        for filename, content in files_dict.items():
            file_path = os.path.join(output_dir, filename)
            # Ensure subdirectories exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            file_paths.append(file_path)
        return file_paths, files_dict
    except Exception as e:
        # fallback: treat as single file
        file_path = os.path.join(output_dir, "generated_code.txt")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(result)
        return [file_path], {"generated_code.txt": result}

def detect_language(filename):
    ext = filename.split(".")[-1].lower()
    return {
        "py": "python",
        "js": "javascript",
        "jsx": "jsx",
        "ts": "typescript",
        "tsx": "tsx",
        "css": "css",
        "html": "html",
        "md": "markdown",
        "java": "java",
        "json": "json",
        "txt": "text"
    }.get(ext, "text")

def to_display_string(content):
    if isinstance(content, dict):
        return json.dumps(content, indent=2)
    return str(content)

if __name__ == "__main__":
    main()
