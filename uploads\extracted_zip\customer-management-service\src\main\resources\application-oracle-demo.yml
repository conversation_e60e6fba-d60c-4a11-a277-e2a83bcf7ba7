server:
  port: 8091
  servlet:
    context-path: /customer-service

spring:
  application:
    name: customer-management-service
  
  # Oracle Database Configuration
  datasource:
    url: ***************************************
    username: system
    password: VRepair123!
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      pool-name: CustomerServiceOraclePool
      connection-test-query: SELECT 1 FROM DUAL
  
  # JPA/Hibernate Configuration for Oracle
  jpa:
    hibernate:
      ddl-auto: create-drop
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        dialect: org.hibernate.dialect.Oracle12cDialect
        format_sql: false
        show_sql: true
        jdbc:
          batch_size: 25
    open-in-view: false
    database-platform: org.hibernate.dialect.Oracle12cDialect
  
  # Disable security completely for demo
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration

# Management endpoints - fully open for demo
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  health:
    db:
      enabled: true
  security:
    enabled: false

# Logging
logging:
  level:
    com.verizon.vrepair.customer: DEBUG
    org.springframework: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# OpenAPI/Swagger configuration - no security
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
  info:
    title: Customer Management Service API (Oracle Demo)
    description: VRepair Customer Management Microservice with Oracle Database
    version: 1.0.0-DEMO
