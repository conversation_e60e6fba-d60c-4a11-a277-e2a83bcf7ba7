# from .base_agent import BaseAgent

# class ReviewerAgent(BaseAgent):
#     """Agent for reviewing Python code."""
    
#     def __init__(self, claude_client, name, api_key):
#         super().__init__(
#             name=name,
#             claude_client=claude_client,
#             system_message="You are a code reviewer. Review the provided Python script for clarity, functionality, and adherence to best practices. Suggest improvements.",
#             api_key=api_key
#         )
#         self.api_key = api_key

from .base_agent import BaseAgent

class ReviewerAgent(BaseAgent):
    """Agent for reviewing code."""
    
    def __init__(self, claude_client, name, api_key):
        super().__init__(
            name=name,
            claude_client=claude_client,
            system_message="""
You are a skilled code reviewer. Review the provided script according to the script/code for clarity, functionality, and adherence to best practices. 
Suggest improvements with actionable feedback.
""",
            api_key=api_key
        )
