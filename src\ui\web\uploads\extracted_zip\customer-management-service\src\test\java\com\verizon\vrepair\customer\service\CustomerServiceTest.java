package com.verizon.vrepair.customer.service;

import com.verizon.vrepair.customer.exception.CustomerNotFoundException;
import com.verizon.vrepair.customer.exception.CustomerAlreadyExistsException;
import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import com.verizon.vrepair.customer.repository.CustomerRepository;
import com.verizon.vrepair.customer.service.impl.CustomerServiceImpl;
import com.verizon.vrepair.customer.validation.CustomerValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CustomerService implementation.
 * Tests core business logic with mocked dependencies.
 */
@ExtendWith(MockitoExtension.class)
class CustomerServiceTest {
    
    @Mock
    private CustomerRepository customerRepository;
    
    @Mock
    private CustomerValidator customerValidator;
    
    @InjectMocks
    private CustomerServiceImpl customerService;
    
    private Customer testCustomer;
    
    @BeforeEach
    void setUp() {
        testCustomer = new Customer();
        testCustomer.setCustomerCode("RES123456");
        testCustomer.setBillingTelephoneNumber("**********");
        testCustomer.setServiceAddress("123 Test St, Test City, TC 12345");
        testCustomer.setCustomerName("Test Customer");
        testCustomer.setCustomerType(CustomerType.RESIDENTIAL);
        testCustomer.setCustomerStatus(CustomerStatus.ACTIVE);
        testCustomer.setAccountNumber("TESTACC001");
    }
    
    @Test
    void createCustomer_ValidCustomer_ReturnsCreatedCustomer() {
        // Given
        Customer inputCustomer = new Customer();
        inputCustomer.setBillingTelephoneNumber("**********");
        inputCustomer.setServiceAddress("123 Test St");
        inputCustomer.setCustomerType(CustomerType.RESIDENTIAL);
        
        when(customerRepository.findByPhoneNumber(anyString())).thenReturn(Optional.empty());
        when(customerRepository.save(any(Customer.class))).thenReturn(testCustomer);
        
        // When
        Customer result = customerService.createCustomer(inputCustomer);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getCustomerCode()).isNotNull();
        assertThat(result.getCustomerStatus()).isEqualTo(CustomerStatus.ACTIVE);
        
        verify(customerValidator).validateCustomerForCreation(inputCustomer);
        verify(customerRepository).findByPhoneNumber("**********");
        verify(customerRepository).save(any(Customer.class));
    }
    
    @Test
    void createCustomer_DuplicatePhoneNumber_ThrowsException() {
        // Given
        Customer inputCustomer = new Customer();
        inputCustomer.setBillingTelephoneNumber("**********");
        inputCustomer.setServiceAddress("123 Test St");
        inputCustomer.setCustomerType(CustomerType.RESIDENTIAL);
        
        when(customerRepository.findByPhoneNumber("**********")).thenReturn(Optional.of(testCustomer));
        
        // When/Then
        assertThatThrownBy(() -> customerService.createCustomer(inputCustomer))
                .isInstanceOf(CustomerAlreadyExistsException.class)
                .hasMessageContaining("**********");
        
        verify(customerValidator).validateCustomerForCreation(inputCustomer);
        verify(customerRepository).findByPhoneNumber("**********");
        verify(customerRepository, never()).save(any(Customer.class));
    }
    
    @Test
    void findCustomerById_ExistingCustomer_ReturnsCustomer() {
        // Given
        when(customerRepository.findById("RES123456")).thenReturn(Optional.of(testCustomer));
        
        // When
        Customer result = customerService.findCustomerById("RES123456");
        
        // Then
        assertThat(result).isEqualTo(testCustomer);
        verify(customerRepository).findById("RES123456");
    }
    
    @Test
    void findCustomerById_NonExistentCustomer_ThrowsException() {
        // Given
        when(customerRepository.findById("NONEXISTENT")).thenReturn(Optional.empty());
        
        // When/Then
        assertThatThrownBy(() -> customerService.findCustomerById("NONEXISTENT"))
                .isInstanceOf(CustomerNotFoundException.class)
                .hasMessageContaining("NONEXISTENT");
        
        verify(customerRepository).findById("NONEXISTENT");
    }
    
    @Test
    void findCustomerByPhoneNumber_ExistingPhone_ReturnsCustomer() {
        // Given
        when(customerRepository.findByPhoneNumber("**********")).thenReturn(Optional.of(testCustomer));
        
        // When
        Customer result = customerService.findCustomerByPhoneNumber("**********");
        
        // Then
        assertThat(result).isEqualTo(testCustomer);
        verify(customerRepository).findByPhoneNumber("**********");
    }
    
    @Test
    void updateCustomer_ValidUpdate_ReturnsUpdatedCustomer() {
        // Given
        Customer updateData = new Customer();
        updateData.setCustomerName("Updated Name");
        updateData.setServiceAddress("Updated Address");
        
        Customer updatedCustomer = new Customer();
        updatedCustomer.setCustomerCode("RES123456");
        updatedCustomer.setCustomerName("Updated Name");
        updatedCustomer.setServiceAddress("Updated Address");
        
        when(customerRepository.findById("RES123456")).thenReturn(Optional.of(testCustomer));
        when(customerRepository.save(any(Customer.class))).thenReturn(updatedCustomer);
        
        // When
        Customer result = customerService.updateCustomer("RES123456", updateData);
        
        // Then
        assertThat(result.getCustomerName()).isEqualTo("Updated Name");
        assertThat(result.getServiceAddress()).isEqualTo("Updated Address");
        
        verify(customerValidator).validateCustomerForUpdate(updateData);
        verify(customerRepository).findById("RES123456");
        verify(customerRepository).save(any(Customer.class));
    }
    
    @Test
    void deactivateCustomer_ActiveCustomer_ReturnsDeactivatedCustomer() {
        // Given
        testCustomer.setCustomerStatus(CustomerStatus.ACTIVE);
        Customer deactivatedCustomer = new Customer();
        deactivatedCustomer.setCustomerCode("RES123456");
        deactivatedCustomer.setCustomerStatus(CustomerStatus.INACTIVE);
        
        when(customerRepository.findById("RES123456")).thenReturn(Optional.of(testCustomer));
        when(customerRepository.save(any(Customer.class))).thenReturn(deactivatedCustomer);
        
        // When
        Customer result = customerService.deactivateCustomer("RES123456");
        
        // Then
        assertThat(result.getCustomerStatus()).isEqualTo(CustomerStatus.INACTIVE);
        
        verify(customerRepository).findById("RES123456");
        verify(customerRepository).save(any(Customer.class));
    }
    
    @Test
    void activateCustomer_InactiveCustomer_ReturnsActivatedCustomer() {
        // Given
        testCustomer.setCustomerStatus(CustomerStatus.INACTIVE);
        Customer activatedCustomer = new Customer();
        activatedCustomer.setCustomerCode("RES123456");
        activatedCustomer.setCustomerStatus(CustomerStatus.ACTIVE);
        
        when(customerRepository.findById("RES123456")).thenReturn(Optional.of(testCustomer));
        when(customerRepository.save(any(Customer.class))).thenReturn(activatedCustomer);
        
        // When
        Customer result = customerService.activateCustomer("RES123456");
        
        // Then
        assertThat(result.getCustomerStatus()).isEqualTo(CustomerStatus.ACTIVE);
        
        verify(customerRepository).findById("RES123456");
        verify(customerRepository).save(any(Customer.class));
    }
    
    @Test
    void validateCustomerEligibility_ActiveCustomer_ReturnsTrue() {
        // Given
        testCustomer.setCustomerStatus(CustomerStatus.ACTIVE);
        when(customerRepository.findById("RES123456")).thenReturn(Optional.of(testCustomer));
        
        // When
        boolean result = customerService.validateCustomerEligibility("RES123456");
        
        // Then
        assertThat(result).isTrue();
        verify(customerRepository).findById("RES123456");
    }
    
    @Test
    void validateCustomerEligibility_InactiveCustomer_ReturnsFalse() {
        // Given
        testCustomer.setCustomerStatus(CustomerStatus.INACTIVE);
        when(customerRepository.findById("RES123456")).thenReturn(Optional.of(testCustomer));
        
        // When
        boolean result = customerService.validateCustomerEligibility("RES123456");
        
        // Then
        assertThat(result).isFalse();
        verify(customerRepository).findById("RES123456");
    }
}
