package com.verizon.vrepair.customer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * Database Configuration for VRepair Customer Management Service
 * 
 * Supports both:
 * - Oracle Database (Production) - As per PRD: "Maintain existing Oracle database and schema"
 * - H2 Database (Development) - For local development and testing
 * 
 * Migration from Legacy C++ LSTRESRV:
 * - Legacy: Oracle Pro*C embedded SQL with direct connections
 * - Modern: Spring Data JPA with HikariCP connection pooling
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.verizon.vrepair.customer.repository")
@EnableJpaAuditing
@EnableTransactionManagement
public class DatabaseConfig {

    @Autowired
    private Environment env;

    /**
     * Oracle Database Configuration (Production Profile)
     * 
     * As per PRD requirements:
     * - Technology Stack: "Oracle (maintained) with JPA/Hibernate" (line 74)
     * - Data Preservation: "Maintain existing Oracle database and schema" (line 124)
     * - Performance: Connection pooling optimization (replacing direct connections)
     * 
     * Note: DataSource configuration is handled by Spring Boot auto-configuration
     * using application-oracle.yml properties
     */

    /**
     * H2 Database Configuration (Development Profile)
     * 
     * For local development and testing:
     * - Lightweight in-memory database
     * - No external dependencies
     * - H2 Console available at /h2-console
     * 
     * Note: DataSource configuration is handled by Spring Boot auto-configuration
     * using application-simple.yml properties
     */

    /**
     * Database Migration Information
     * 
     * Legacy C++ Implementation:
     * - Direct Oracle Pro*C embedded SQL
     * - Manual connection management
     * - Stored procedures for business logic
     * - No connection pooling
     * 
     * Modern Java Implementation:
     * - Spring Data JPA with Hibernate
     * - HikariCP connection pooling (50 max, 10 min)
     * - Business logic in Java services
     * - Automatic connection management
     * - Performance optimization with caching
     * 
     * Migration preserves:
     * - All existing Oracle database schema
     * - All data relationships and constraints
     * - All business rules and validation logic
     * - All audit trails and activity logging
     */
}

