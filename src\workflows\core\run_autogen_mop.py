import os
from local_agents.code_generator_agent import CodeGeneratorAgent
from local_agents.api_integration_agent import APIIntegrationAgent
from local_agents.reviewer_agent import ReviewerAgent
from local_agents.improver_agent import ImproverAgent
from local_agents.documentation_agent import DocumentationAgent
from local_agents.prd_agent import PRDAgent
from local_agents.qa_agent import QAAgent

def run_autogen_mop(api_key, mop_content, output_path, api_requirements=None, workflow_type="code"):
    """
    Run AutoGen workflow with multiple agent types.

    Args:
        api_key: Claude API key
        mop_content: Method of Procedure content
        output_path: Path to save output files
        api_requirements: API integration requirements (optional)
    """

    print(f"🚀 Creating AutoGen agents powered by Claude... (Workflow: {workflow_type})")

    # Create Claude client
    from anthropic import Anthropic
    claude_client = Anthropic(api_key=api_key)

    # Initialize base agents
    coder = CodeGeneratorAgent(claude_client=claude_client, name="coder", api_key=api_key)
    reviewer = ReviewerAgent(claude_client=claude_client, name="reviewer", api_key=api_key)
    improver = ImproverAgent(claude_client=claude_client, name="improver", api_key=api_key)
    api_integrator = APIIntegrationAgent(claude_client=claude_client, name="integrator", api_key=api_key)
    document_agent = DocumentationAgent(claude_client=claude_client, name="DocumentAgent", api_key=api_key)
    prd_agent = PRDAgent(claude_client=claude_client, name="PRDAgent", api_key=api_key)
    qa_agent = QAAgent(claude_client=claude_client, name="QAAgent", api_key=api_key)

    print("✅ AutoGen agents created!")

    # Initialize result dictionary
    results = {}

    # Code generation workflow (default)
    if workflow_type in ["code", "full"]:
        print("\n📝 Running Code Generation Workflow...")
        code_response = coder.generate_reply([{"content": mop_content}])
        review_response = reviewer.generate_reply([{"content": f"Review this code:\n\n{code_response}"}])
        final_response = improver.generate_reply([{"content": f"Improve this code:\n\n{code_response}\n\n{review_response}"}])

        # Save final code
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, "w", encoding="utf-8") as file:
            file.write(final_response)

        results.update({
            "original_code": code_response,
            "review": review_response,
            "improved_code": final_response,
        })

    # API integration workflow
    api_analysis, api_design, api_test_and_optimize = None, None, None
    if api_requirements and workflow_type in ["code", "full"]:
        print("\n🔌 Running API Integration Workflow...")
        api_analysis = api_integrator.handle_task(f"Analyze the following API requirements:\n{api_requirements}")
        api_design = api_integrator.handle_task(f"Design API integration:\n{api_requirements}")
        api_test_and_optimize = api_integrator.handle_task(f"Optimize this design:\n{api_design}")

        results.update({
            "api_analysis": api_analysis,
            "api_design": api_design,
            "api_test_and_optimize": api_test_and_optimize,
        })

    print("✅ PRD Agent created!")

    # Step 3: Generate PRD content
    print("\n📄 Running PRD generation...")
    result = prd_agent.generate_prd(mop_content=mop_content, output_file=output_path)
    return {"status": "success" if "✅" in result else "failure", "message": result}


     # QA workflow
    if workflow_type in ["qa", "full"]:
        print("\n🔍 Running Quality Assurance Workflow...")
        static_analysis = qa_agent.analyze_code(mop_content)
        test_cases = qa_agent.generate_test_cases(mop_content)
        compliance_check = qa_agent.check_compliance(mop_content)

        # Save QA results
        qa_file = os.path.join(output_path, "qa_results.txt")
        with open(qa_file, "w", encoding="utf-8") as file:
            file.write(f"Static Code Analysis:\n{static_analysis}\n\n")
            file.write(f"Test Cases:\n{test_cases}\n\n")
            file.write(f"Compliance Check:\n{compliance_check}\n\n")

            results.update({
            "static_analysis": static_analysis,
            "test_cases": test_cases,
            "compliance_check": compliance_check,
        })

    return results
