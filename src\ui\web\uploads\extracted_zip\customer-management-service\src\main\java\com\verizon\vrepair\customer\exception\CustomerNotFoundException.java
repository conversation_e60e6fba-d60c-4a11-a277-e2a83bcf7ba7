package com.verizon.vrepair.customer.exception;

/**
 * Exception thrown when a customer is not found.
 * Replaces legacy C++ error code CUSTOMER_NOT_FOUND.
 */
public class CustomerNotFoundException extends RuntimeException {
    
    private final String customerCode;
    
    public CustomerNotFoundException(String message) {
        super(message);
        this.customerCode = null;
    }
    
    public CustomerNotFoundException(String message, String customerCode) {
        super(message);
        this.customerCode = customerCode;
    }
    
    public CustomerNotFoundException(String message, Throwable cause) {
        super(message, cause);
        this.customerCode = null;
    }
    
    public String getCustomerCode() {
        return customerCode;
    }
}
