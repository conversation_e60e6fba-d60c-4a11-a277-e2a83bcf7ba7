#!/bin/bash

# VRepair Customer Management Service - Oracle Database Startup Script

echo "🚀 Starting VRepair Customer Management Service with Oracle Database"
echo "=================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Load environment variables
if [ -f "oracle.env" ]; then
    echo "📁 Loading Oracle environment variables..."
    source oracle.env
else
    echo "⚠️  oracle.env file not found. Using default values."
fi

# Start Oracle Database and Redis with Docker Compose
echo "🐳 Starting Oracle Database and Redis containers..."
docker-compose up -d

# Wait for Oracle Database to be ready
echo "⏳ Waiting for Oracle Database to be ready..."
sleep 30

# Check Oracle Database health
echo "🔍 Checking Oracle Database connection..."
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker exec vrepair-oracle-xe sqlplus -s vrepair_user/vrepair_pass@VREPAIR <<< "SELECT 1 FROM DUAL;" > /dev/null 2>&1; then
        echo "✅ Oracle Database is ready!"
        break
    else
        echo "⏳ Attempt $attempt/$max_attempts: Oracle Database not ready yet, waiting..."
        sleep 10
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ Oracle Database failed to start after $max_attempts attempts"
    echo "📋 Checking container logs..."
    docker-compose logs oracle-db
    exit 1
fi

# Build the application if needed
if [ ! -f "target/customer-management-service-1.0.0-SNAPSHOT.jar" ]; then
    echo "🔨 Building application..."
    mvn clean package -DskipTests
fi

# Start the Spring Boot application
echo "🌟 Starting Customer Management Service..."
echo "📊 Application will be available at: http://localhost:8080/customer-service"
echo "📖 API Documentation: http://localhost:8080/customer-service/swagger-ui.html"
echo "💾 Database Console: http://localhost:5500/em (admin/VRepair123!)"
echo ""
echo "🛑 Press Ctrl+C to stop the application"
echo "=================================================="

java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=oracle,prod
