version: '3.8'

services:
  # Oracle Database XE (Free Edition)
  oracle-db:
    image: gvenzl/oracle-xe:21-slim-faststart
    container_name: vrepair-oracle-xe
    environment:
      # Database configuration
      ORACLE_PASSWORD: VRepair123!
      ORACLE_DATABASE: VREPAIR
      ORACLE_USER: vrepair_user
      ORACLE_PASSWORD_USER: vrepair_pass
    ports:
      - "1521:1521"
      - "5500:5500"  # Oracle Enterprise Manager
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./init-scripts:/container-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
    networks:
      - vrepair-network

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: vrepair-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - vrepair-network

volumes:
  oracle_data:
    driver: local
  redis_data:
    driver: local

networks:
  vrepair-network:
    driver: bridge
