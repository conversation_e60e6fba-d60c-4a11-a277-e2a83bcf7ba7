from anthropic import Anthropic
import autogen
from autogen import ConversableAgent

class ClaudeAgent(ConversableAgent):
    """Custom AutoGen agent powered by <PERSON>"""
    
    def __init__(self, name, claude_client, system_message):
        super().__init__(name=name, human_input_mode="NEVER")
        self.claude_client = claude_client
        self._system_message = system_message
    
    def generate_reply(self, messages=None, sender=None, **kwargs):
        """Generate reply using <PERSON>"""
        if messages:
            last_message = messages[-1]['content']
            
            prompt = f"{self._system_message}\n\nTask: {last_message}"            
            try:
                response = self.claude_client.messages.create(
                    model="claude-sonnet-4-20250514",
                    max_tokens=1000,
                    messages=[{"role": "user", "content": prompt}]
                )
                return response.content[0].text
            except Exception as e:
                return f"Error: {e}"
        
        return "No message to respond to."

def run_autogen_fibonacci(api_key):
    """Run AutoGen workflow with <PERSON> agents"""
    
    print("🚀 Creating AutoGen agents powered by <PERSON>...")
    
    # Create Claude client
    claude_client = Anthropic(api_key=api_key)
    
    # Create AutoGen agents using Claude
    coder = ClaudeAgent(
        name="coder",
        claude_client=claude_client,
        system_message="You are a Python expert. Write simple, clean code with comments."
    )
    
    reviewer = ClaudeAgent(
        name="reviewer", 
        claude_client=claude_client,
        system_message="You are a code reviewer. Give helpful feedback on code quality and improvements."
    )
    
    improver = ClaudeAgent(
        name="improver",
        claude_client=claude_client, 
        system_message="You are a code improver. Take code and feedback to create a better version."
    )
    
    print("✅ AutoGen agents created!")
    
    # AutoGen conversation flow
    print("\n" + "="*60)
    print("🤖 AUTOGEN MULTI-AGENT CONVERSATION FLOW")
    print("="*60)
    
    # Step 1: Initial request
    task = "Write a Python function to generate the first 10 Fibonacci numbers"
    print(f"\n📝 Task: {task}")
    
    # Step 2: Coder responds
    print(f"\n💻 {coder.name.upper()} working...")
    code_response = coder.generate_reply([{"content": task}])
    print(f"CODER: {code_response}")
    
    # Step 3: Reviewer gives feedback
    print(f"\n🔍 {reviewer.name.upper()} reviewing...")
    review_task = f"Review this code and suggest improvements: {code_response}"
    review_response = reviewer.generate_reply([{"content": review_task}])
    print(f"REVIEWER: {review_response}")
    
    # Step 4: Improver creates final version
    print(f"\n✨ {improver.name.upper()} improving...")
    improve_task = f"Improve this code based on feedback:\n\nCODE:\n{code_response}\n\nFEEDBACK:\n{review_response}"
    final_response = improver.generate_reply([{"content": improve_task}])
    print(f"IMPROVER: {final_response}")
    
    print("\n🎉 AutoGen workflow complete!")
    
    return {
        "original_code": code_response,
        "review": review_response, 
        "improved_code": final_response
    }

def main():
    # Your Claude API key
    api_key = '************************************************************************************************************'
    
    if api_key == 'your-claude-api-key-here':
        print("❌ Please add your Claude API key!")
        return
    
    # Check installations
    try:
        import autogen
        from anthropic import Anthropic
        print("✅ All packages found")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Install with: pip install pyautogen anthropic")
        return
    
    # Run the workflow
    results = run_autogen_fibonacci(api_key)
    
    print("\n" + "="*60)
    print("📊 WORKFLOW SUMMARY")
    print("="*60)
    print("✅ Used real AutoGen framework")
    print("✅ Powered by Claude API")  
    print("✅ Multi-agent conversation flow")
    print(f"✅ Generated: {len(results)} different outputs")

if __name__ == "__main__":
    main()