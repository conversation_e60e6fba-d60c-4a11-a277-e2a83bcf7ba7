package com.verizon.vrepair.customer.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.verizon.vrepair.customer.dto.CreateCustomerRequest;
import com.verizon.vrepair.customer.dto.CustomerDto;
import com.verizon.vrepair.customer.dto.UpdateCustomerRequest;
import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import com.verizon.vrepair.customer.repository.CustomerRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.OracleContainer;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive integration tests for Customer Service.
 * Tests complete request/response flow with real database and cache.
 * Validates functional parity with legacy system requirements.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("integration-test")
@Testcontainers
@Transactional
class CustomerServiceIntegrationTest {
    
    @Container
    static OracleContainer oracle = new OracleContainer("gvenzl/oracle-xe:21-slim")
            .withDatabaseName("testdb")
            .withUsername("testuser")
            .withPassword("testpass")
            .withReuse(true);
    
    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
            .withExposedPorts(6379)
            .withReuse(true);
    
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Oracle database configuration
        registry.add("spring.datasource.url", oracle::getJdbcUrl);
        registry.add("spring.datasource.username", oracle::getUsername);
        registry.add("spring.datasource.password", oracle::getPassword);
        
        // Redis configuration
        registry.add("spring.redis.host", redis::getHost);
        registry.add("spring.redis.port", () -> redis.getMappedPort(6379));
        
        // Test-specific configurations
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
        registry.add("app.encryption.key", () -> "dGVzdC1lbmNyeXB0aW9uLWtleS0xMjM0NTY3ODkwMTIzNDU2");
    }
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    private CreateCustomerRequest validCreateRequest;
    private UpdateCustomerRequest validUpdateRequest;
    
    @BeforeEach
    void setUp() {
        // Clean database
        customerRepository.deleteAll();
        
        // Setup test data
        validCreateRequest = new CreateCustomerRequest();
        validCreateRequest.setBillingTelephoneNumber("**********");
        validCreateRequest.setServiceAddress("123 Integration Test St, Test City, TC 12345");
        validCreateRequest.setCustomerName("Integration Test Customer");
        validCreateRequest.setCustomerType("RESIDENTIAL");
        validCreateRequest.setAccountNumber("INTACC001");
        validCreateRequest.setServiceClassCode("RES001");
        validCreateRequest.setMaintenanceLevel("STANDARD");
        
        validUpdateRequest = new UpdateCustomerRequest();
        validUpdateRequest.setCustomerName("Updated Integration Test Customer");
        validUpdateRequest.setServiceAddress("456 Updated St, Updated City, UC 54321");
        validUpdateRequest.setContactPhone("**********");
    }
    
    @Test
    @WithMockUser(roles = "USER")
    void createCustomer_CompleteWorkflow_CreatesAndRetrievesSuccessfully() throws Exception {
        // When - Create customer
        MvcResult createResult = mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validCreateRequest)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.billingTelephoneNumber", is("**********")))
                .andExpect(jsonPath("$.customerName", is("Integration Test Customer")))
                .andExpect(jsonPath("$.customerType", is("RESIDENTIAL")))
                .andExpect(jsonPath("$.customerStatus", is("ACTIVE")))
                .andReturn();
        
        // Extract customer code from response
        String responseContent = createResult.getResponse().getContentAsString();
        CustomerDto createdCustomer = objectMapper.readValue(responseContent, CustomerDto.class);
        String customerCode = createdCustomer.getCustomerCode();
        
        assertThat(customerCode).isNotNull();
        assertThat(customerCode).startsWith("RES"); // Residential prefix
        
        // Then - Retrieve customer by ID
        mockMvc.perform(get("/api/v1/customers/" + customerCode))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.customerCode", is(customerCode)))
                .andExpect(jsonPath("$.billingTelephoneNumber", is("**********")))
                .andExpect(jsonPath("$.customerName", is("Integration Test Customer")))
                .andExpect(jsonPath("$.serviceAddress", is("123 Integration Test St, Test City, TC 12345")))
                .andExpect(jsonPath("$.accountNumber", is("INTACC001")));
        
        // Verify database persistence
        Optional<Customer> dbCustomer = customerRepository.findById(customerCode);
        assertThat(dbCustomer).isPresent();
        assertThat(dbCustomer.get().getBillingTelephoneNumber()).isEqualTo("**********");
        assertThat(dbCustomer.get().getCustomerStatus()).isEqualTo(CustomerStatus.ACTIVE);
    }
    
    @Test
    @WithMockUser(roles = "ADMIN")
    void deactivateAndActivateCustomer_CompleteWorkflow_WorksCorrectly() throws Exception {
        // Given - Create active customer
        Customer activeCustomer = new Customer("ACTDEACT001", "**********", 
                "Activation Test Address", CustomerType.BUSINESS);
        activeCustomer.setCustomerName("Activation Test Customer");
        activeCustomer.setAccountNumber("ACTACC001");
        activeCustomer.setCustomerStatus(CustomerStatus.ACTIVE);
        customerRepository.save(activeCustomer);
        
        // When - Deactivate customer
        mockMvc.perform(put("/api/v1/customers/ACTDEACT001/deactivate")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.customerStatus", is("INACTIVE")));
        
        // Then - Verify deactivation in database
        Optional<Customer> deactivatedCustomer = customerRepository.findById("ACTDEACT001");
        assertThat(deactivatedCustomer).isPresent();
        assertThat(deactivatedCustomer.get().getCustomerStatus()).isEqualTo(CustomerStatus.INACTIVE);
        
        // When - Reactivate customer
        mockMvc.perform(put("/api/v1/customers/ACTDEACT001/activate")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.customerStatus", is("ACTIVE")));
        
        // Then - Verify reactivation in database
        Optional<Customer> reactivatedCustomer = customerRepository.findById("ACTDEACT001");
        assertThat(reactivatedCustomer).isPresent();
        assertThat(reactivatedCustomer.get().getCustomerStatus()).isEqualTo(CustomerStatus.ACTIVE);
    }
    
    @Test
    @WithMockUser(roles = "ADMIN")
    void getCustomerStatistics_AdminAccess_ReturnsStatistics() throws Exception {
        // Given - Create test customers with different types and statuses
        createTestCustomersForStatistics();
        
        // When - Get statistics
        mockMvc.perform(get("/api/v1/customers/statistics"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.totalCustomers").isNumber())
                .andExpect(jsonPath("$.activeCustomers").isNumber())
                .andExpect(jsonPath("$.inactiveCustomers").isNumber())
                .andExpect(jsonPath("$.residentialCustomers").isNumber())
                .andExpect(jsonPath("$.businessCustomers").isNumber());
    }
    
    @Test
    void errorHandling_InvalidInput_ReturnsProperErrorResponse() throws Exception {
        // Given - Invalid customer request (missing required fields)
        CreateCustomerRequest invalidRequest = new CreateCustomerRequest();
        invalidRequest.setBillingTelephoneNumber("123"); // Invalid phone format
        
        // When - Attempt to create invalid customer
        mockMvc.perform(post("/api/v1/customers")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode").exists())
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.correlationId").exists())
                .andExpect(jsonPath("$.timestamp").exists());
    }
    
    @Test
    @WithMockUser(roles = "USER")
    void notFoundHandling_NonExistentCustomer_ReturnsNotFound() throws Exception {
        // When - Try to get non-existent customer
        mockMvc.perform(get("/api/v1/customers/NONEXISTENT"))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.errorCode", is("CUSTOMER_NOT_FOUND")))
                .andExpect(jsonPath("$.message").exists())
                .andExpect(jsonPath("$.correlationId").exists());
    }
    
    private void createTestCustomersForStatistics() {
        // Residential customers
        for (int i = 1; i <= 10; i++) {
            Customer customer = new Customer("RES" + String.format("%03d", i), 
                    "555100" + String.format("%04d", i),
                    "Residential Address " + i, CustomerType.RESIDENTIAL);
            customer.setCustomerName("Residential Customer " + i);
            customer.setAccountNumber("RESACC" + String.format("%03d", i));
            customer.setCustomerStatus(i <= 8 ? CustomerStatus.ACTIVE : CustomerStatus.INACTIVE);
            customerRepository.save(customer);
        }
        
        // Business customers
        for (int i = 1; i <= 5; i++) {
            Customer customer = new Customer("BUS" + String.format("%03d", i), 
                    "555200" + String.format("%04d", i),
                    "Business Address " + i, CustomerType.BUSINESS);
            customer.setCustomerName("Business Customer " + i);
            customer.setAccountNumber("BUSACC" + String.format("%03d", i));
            customer.setCustomerStatus(CustomerStatus.ACTIVE);
            customerRepository.save(customer);
        }
    }
}

