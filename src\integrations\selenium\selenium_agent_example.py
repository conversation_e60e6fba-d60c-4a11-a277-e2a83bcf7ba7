"""
Example usage of SeleniumTestAgent
Demonstrates all capabilities and integration patterns
"""

import os
import sys
import json
from pathlib import Path

# Add local_agents to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'local_agents'))

from selenium_test_agent import SeleniumTestAgent

def example_basic_usage():
    """Basic usage example"""
    print("=== Basic Selenium Test Agent Usage ===")
    
    # Initialize agent
    agent = SeleniumTestAgent(name="MySeleniumAgent")
    
    # Setup browser
    success = agent.setup_browser(browser="chrome", headless=False)
    if not success:
        print("Failed to setup browser")
        return
    
    # Navigate to a website
    agent.navigate_to("https://www.google.com")
    
    # Find and interact with elements
    agent.type_text("name", "q", "Selenium automation")
    agent.click_element("name", "btnK")
    
    # Wait for results
    agent.wait_for_element("id", "search", "visible", timeout=10)
    
    # Capture screenshot
    screenshot_path = agent.capture_screenshot("google_search_results")
    print(f"Screenshot saved: {screenshot_path}")
    
    # Cleanup
    agent.cleanup()
    print("Basic usage example completed")

def example_test_suite_execution():
    """Test suite execution example"""
    print("\n=== Test Suite Execution Example ===")
    
    # Initialize agent
    agent = SeleniumTestAgent(name="TestSuiteAgent")
    
    # Load configuration
    config_path = "test_data/selenium_config.yaml"
    if os.path.exists(config_path):
        agent.load_config(config_path)
        print(f"Configuration loaded from {config_path}")
    
    # Run test suite
    test_suite_path = "test_data/example_test_suite.json"
    if os.path.exists(test_suite_path):
        print(f"Running test suite: {test_suite_path}")
        results = agent.run_test_suite(test_suite_path)
        
        if "error" not in results:
            print(f"Test suite completed:")
            print(f"- Total scenarios: {results['total_scenarios']}")
            print(f"- Passed: {results['passed_scenarios']}")
            print(f"- Failed: {results['failed_scenarios']}")
            print(f"- Success rate: {results['success_rate']:.1f}%")
            print(f"- Execution time: {results['execution_time']:.2f}s")
            
            # Generate HTML report
            report_path = agent.generate_test_report(results)
            if report_path:
                print(f"HTML report generated: {report_path}")
        else:
            print(f"Test suite failed: {results['error']}")
    else:
        print(f"Test suite file not found: {test_suite_path}")
    
    # Cleanup
    agent.cleanup()
    print("Test suite execution example completed")

def example_data_driven_testing():
    """Data-driven testing example"""
    print("\n=== Data-Driven Testing Example ===")
    
    # Initialize agent
    agent = SeleniumTestAgent(name="DataDrivenAgent")
    
    # Load test data from CSV
    csv_path = "test_data/test_data.csv"
    if os.path.exists(csv_path):
        test_data = agent.load_test_data_csv(csv_path)
        print(f"Loaded {len(test_data)} test records from CSV")
        
        # Setup browser
        agent.setup_browser(browser="chrome", headless=True)
        
        # Run tests for each data record
        for i, data in enumerate(test_data[:2]):  # Limit to first 2 records for demo
            print(f"Running test with data record {i+1}: {data['username']}")
            
            # Create dynamic test scenario
            scenario = {
                "name": f"Login Test - {data['username']}",
                "steps": [
                    {
                        "action": "navigate",
                        "url": "https://example.com/login"
                    },
                    {
                        "action": "type",
                        "locator_type": "id",
                        "locator_value": "username",
                        "text": data["username"]
                    },
                    {
                        "action": "type",
                        "locator_type": "id",
                        "locator_value": "password",
                        "text": data["password"]
                    },
                    {
                        "action": "screenshot",
                        "filename": f"login_attempt_{data['username']}"
                    }
                ]
            }
            
            # Run the scenario
            result = agent.run_test_scenario(scenario)
            print(f"Test result: {result['status']}")
    else:
        print(f"CSV file not found: {csv_path}")
    
    # Cleanup
    agent.cleanup()
    print("Data-driven testing example completed")

def example_autogen_integration():
    """AutoGen integration example"""
    print("\n=== AutoGen Integration Example ===")
    
    # Initialize agent for AutoGen communication
    agent = SeleniumTestAgent(name="AutoGenSeleniumAgent")
    
    # Simulate AutoGen messages
    test_messages = [
        {"content": "setup browser chrome headless"},
        {"content": "navigate to https://www.google.com"},
        {"content": "run test file: test_data/example_test_suite.json"}
    ]
    
    for message in test_messages:
        print(f"Processing message: {message['content']}")
        response = agent.generate_reply([message])
        print(f"Agent response: {response}")
        print("-" * 50)
    
    # Cleanup
    agent.cleanup()
    print("AutoGen integration example completed")

def example_multi_browser_testing():
    """Multi-browser testing example"""
    print("\n=== Multi-Browser Testing Example ===")
    
    browsers = ["chrome", "firefox", "edge"]
    
    for browser in browsers:
        print(f"Testing with {browser} browser...")
        
        # Initialize agent
        agent = SeleniumTestAgent(name=f"{browser.title()}Agent")
        
        try:
            # Setup browser
            success = agent.setup_browser(browser=browser, headless=True)
            if success:
                print(f"✓ {browser} setup successful")
                
                # Simple navigation test
                if agent.navigate_to("https://www.google.com"):
                    print(f"✓ {browser} navigation successful")
                    
                    # Capture screenshot
                    screenshot = agent.capture_screenshot(f"{browser}_test")
                    if screenshot:
                        print(f"✓ {browser} screenshot captured: {screenshot}")
                else:
                    print(f"✗ {browser} navigation failed")
            else:
                print(f"✗ {browser} setup failed")
                
        except Exception as e:
            print(f"✗ {browser} test failed: {str(e)}")
        finally:
            agent.cleanup()
        
        print("-" * 30)
    
    print("Multi-browser testing example completed")

def create_custom_test_scenario():
    """Create a custom test scenario"""
    print("\n=== Creating Custom Test Scenario ===")
    
    custom_scenario = {
        "suite_name": "Custom E-commerce Test Suite",
        "browser": "chrome",
        "headless": false,
        "scenarios": [
            {
                "name": "Product Search and Add to Cart",
                "description": "Search for a product and add it to cart",
                "stop_on_failure": True,
                "steps": [
                    {
                        "action": "navigate",
                        "url": "https://example-shop.com"
                    },
                    {
                        "action": "type",
                        "locator_type": "css",
                        "locator_value": "input[name='search']",
                        "text": "laptop"
                    },
                    {
                        "action": "click",
                        "locator_type": "css",
                        "locator_value": "button[type='submit']"
                    },
                    {
                        "action": "wait",
                        "locator_type": "class",
                        "locator_value": "product-list",
                        "condition": "visible",
                        "timeout": 10
                    },
                    {
                        "action": "click",
                        "locator_type": "xpath",
                        "locator_value": "//div[@class='product-item'][1]//button[contains(text(), 'Add to Cart')]"
                    },
                    {
                        "action": "wait",
                        "locator_type": "class",
                        "locator_value": "cart-notification",
                        "condition": "visible",
                        "timeout": 5
                    },
                    {
                        "action": "verify_text",
                        "locator_type": "class",
                        "locator_value": "cart-notification",
                        "expected_text": "Item added to cart"
                    },
                    {
                        "action": "screenshot",
                        "filename": "product_added_to_cart"
                    }
                ]
            }
        ]
    }
    
    # Save custom scenario
    custom_file = "test_data/custom_test_suite.json"
    with open(custom_file, 'w') as f:
        json.dump(custom_scenario, f, indent=2)
    
    print(f"Custom test scenario created: {custom_file}")
    
    # Run the custom scenario
    agent = SeleniumTestAgent(name="CustomTestAgent")
    results = agent.run_test_suite(custom_file)
    
    if "error" not in results:
        print(f"Custom test completed with {results['success_rate']:.1f}% success rate")
    else:
        print(f"Custom test failed: {results['error']}")
    
    agent.cleanup()
    print("Custom test scenario example completed")

def main():
    """Run all examples"""
    print("Selenium Test Agent - Comprehensive Examples")
    print("=" * 50)
    
    # Create necessary directories
    os.makedirs("test_screenshots", exist_ok=True)
    os.makedirs("test_logs", exist_ok=True)
    os.makedirs("test_data", exist_ok=True)
    
    try:
        # Run examples
        example_basic_usage()
        example_test_suite_execution()
        example_data_driven_testing()
        example_autogen_integration()
        example_multi_browser_testing()
        create_custom_test_scenario()
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"Error running examples: {str(e)}")
    
    print("\nAll examples completed!")

if __name__ == "__main__":
    main()