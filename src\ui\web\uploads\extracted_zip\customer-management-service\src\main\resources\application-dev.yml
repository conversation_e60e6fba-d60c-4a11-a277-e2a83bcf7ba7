spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
        show_sql: false
    database-platform: org.hibernate.dialect.H2Dialect
  
  # Disable Redis for dev mode
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
  
  cache:
    type: simple  # Use simple cache instead of Redis
  
  security:
    oauth2:
      resourceserver:
        jwt:
          # Use a mock JWK set URI for development
          jwk-set-uri: http://localhost:8080/.well-known/jwks.json

# Application-specific configuration for dev
app:
  encryption:
    key: dGVzdC1lbmNyeXB0aW9uLWtleS0xMjM0NTY3ODkwMTIzNDU2
  
  security:
    cors:
      allowed-origins: http://localhost:3000,http://localhost:8080
  
  external-services:
    etms:
      base-url: http://localhost:8081
      timeout: 5000
      retry-attempts: 1
    
    baais:
      base-url: http://localhost:8082
      timeout: 3000
      retry-attempts: 1
    
    vi:
      base-url: http://localhost:8083
      timeout: 2000
      retry-attempts: 1

# Enable management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    redis:
      enabled: false  # Disable Redis health check
    db:
      enabled: true

# Development logging
logging:
  level:
    com.verizon.vrepair.customer: DEBUG
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
