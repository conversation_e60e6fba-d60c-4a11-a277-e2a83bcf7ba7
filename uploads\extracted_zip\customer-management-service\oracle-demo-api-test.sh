#!/bin/bash

echo "🎯 VRepair Customer Management Service - Oracle Database API Demo"
echo "================================================================"
echo ""

BASE_URL="http://localhost:8091/customer-service"

echo "1️⃣ Testing Health Check (Oracle Database)..."
curl -s "$BASE_URL/actuator/health" | jq '.'
echo ""
echo ""

echo "2️⃣ Creating Demo Customers in Oracle Database..."
echo "   📊 Customer 1: Residential Customer"
CUSTOMER1_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/customers" \
  -H "Content-Type: application/json" \
  -d '{
    "billingTelephoneNumber": "**********",
    "customerName": "Oracle Demo Customer",
    "customerType": "RESIDENTIAL",
    "serviceAddress": "123 Oracle Street, Database City, DC 12345",
    "accountNumber": "ORACLE001",
    "serviceClassCode": "RES",
    "contactPhone": "**********",
    "emailAddress": "<EMAIL>"
  }')

echo "$CUSTOMER1_RESPONSE" | jq '.'
CUSTOMER1_CODE=$(echo "$CUSTOMER1_RESPONSE" | jq -r '.customerCode')
echo "✅ Residential Customer created with code: $CUSTOMER1_CODE"
echo ""

echo "   📊 Customer 2: Business Customer"
CUSTOMER2_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/customers" \
  -H "Content-Type: application/json" \
  -d '{
    "billingTelephoneNumber": "**********",
    "customerName": "Oracle Business Corp",
    "customerType": "BUSINESS",
    "serviceAddress": "456 Enterprise Ave, Corporate City, CC 54321",
    "accountNumber": "ORACLE002",
    "serviceClassCode": "BUS",
    "contactPhone": "**********",
    "emailAddress": "<EMAIL>"
  }')

echo "$CUSTOMER2_RESPONSE" | jq '.'
CUSTOMER2_CODE=$(echo "$CUSTOMER2_RESPONSE" | jq -r '.customerCode')
echo "✅ Business Customer created with code: $CUSTOMER2_CODE"
echo ""

echo "3️⃣ Retrieving Customers from Oracle Database..."
echo "   📋 Getting Residential Customer:"
curl -s "$BASE_URL/api/v1/customers/$CUSTOMER1_CODE" | jq '.'
echo ""
echo "   📋 Getting Business Customer:"
curl -s "$BASE_URL/api/v1/customers/$CUSTOMER2_CODE" | jq '.'
echo ""

echo "4️⃣ Listing All Customers (Oracle Database)..."
curl -s "$BASE_URL/api/v1/customers?page=0&size=10" | jq '.'
echo ""

echo "5️⃣ Searching Customers by Name in Oracle..."
curl -s "$BASE_URL/api/v1/customers/search?customerName=Oracle" | jq '.'
echo ""

echo "6️⃣ Customer Statistics from Oracle Database..."
curl -s "$BASE_URL/api/v1/customers/statistics" | jq '.'
echo ""

echo "7️⃣ Creating Government Customer..."
curl -s -X POST "$BASE_URL/api/v1/customers" \
  -H "Content-Type: application/json" \
  -d '{
    "billingTelephoneNumber": "**********",
    "customerName": "Government Agency Oracle",
    "customerType": "GOVERNMENT",
    "serviceAddress": "789 Government Plaza, Federal City, FC 98765",
    "accountNumber": "GOV001",
    "serviceClassCode": "GOV",
    "contactPhone": "**********",
    "emailAddress": "<EMAIL>"
  }' | jq '.'
echo ""

echo "8️⃣ Final Statistics (All Customer Types in Oracle)..."
curl -s "$BASE_URL/api/v1/customers/statistics" | jq '.'
echo ""

echo "🎉 Oracle Database Demo Complete!"
echo "================================================================"
echo "📊 Database: Oracle XE (localhost:1521/XEPDB1)"
echo "📋 Available URLs:"
echo "   🏥 Health:     $BASE_URL/actuator/health"
echo "   📖 Swagger:    $BASE_URL/swagger-ui/index.html"
echo "   💾 Oracle EM:  http://localhost:5500/em"
echo "   📊 Metrics:    $BASE_URL/actuator/metrics"
echo ""
echo "🔑 Oracle Database Access:"
echo "   Host: localhost:1521"
echo "   Service: XEPDB1"
echo "   Username: system"
echo "   Password: VRepair123!"
echo "================================================================"
