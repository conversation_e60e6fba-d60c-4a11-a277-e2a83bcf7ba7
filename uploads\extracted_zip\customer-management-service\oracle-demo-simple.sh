#!/bin/bash

echo "🚀 VRepair Customer Management Service - Oracle Database Demo"
echo "============================================================"

# Navigate to correct directory
cd /Users/<USER>/Downloads/Vrepair_Arch/customer-management-service

# Clean up
pkill -f customer-management-service 2>/dev/null
sleep 2

# Ensure Oracle is running
echo "🔍 Checking Oracle Database..."
if docker ps | grep -q vrepair-oracle-xe; then
    echo "✅ Oracle Database is running"
else
    echo "🐳 Starting Oracle Database..."
    docker-compose up -d
    sleep 20
fi

echo ""
echo "🌟 Starting service with Oracle Database..."
echo "📊 Database: Oracle XE (localhost:1521/XEPDB1)"
echo "🌐 Service URL: http://localhost:8091/customer-service"
echo "📖 Swagger UI: http://localhost:8091/customer-service/swagger-ui/index.html"
echo "💾 Oracle EM: http://localhost:5500/em"
echo ""

# Start service
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar \
  --spring.profiles.active=oracle-demo \
  --logging.level.root=INFO &

SERVICE_PID=$!
echo "🔄 Service PID: $SERVICE_PID"
echo "⏳ Waiting for service startup..."

# Wait and test
sleep 30

if curl -s http://localhost:8091/customer-service/actuator/health > /dev/null 2>&1; then
    echo ""
    echo "🎉 SUCCESS! Oracle Database Demo is Ready!"
    echo "============================================================"
    
    # Show health status
    echo "📊 Health Status:"
    curl -s http://localhost:8091/customer-service/actuator/health | jq '.'
    echo ""
    
    echo "📋 Demo URLs:"
    echo "   🏥 Health: http://localhost:8091/customer-service/actuator/health"
    echo "   📖 Swagger: http://localhost:8091/customer-service/swagger-ui/index.html"
    echo "   💾 Oracle EM: http://localhost:5500/em"
    echo ""
    echo "🔑 Oracle Database Info:"
    echo "   Host: localhost:1521"
    echo "   Service: XEPDB1"
    echo "   Username: system"
    echo "   Password: VRepair123!"
    echo ""
    
    # Open in browser
    echo "🎯 Opening demo in browser..."
    open http://localhost:8091/customer-service/swagger-ui/index.html
    open http://localhost:5500/em
    
    echo ""
    echo "🛑 To stop: kill $SERVICE_PID"
    echo "============================================================"
else
    echo "❌ Service failed to start. Checking logs..."
    ps aux | grep customer-management
fi
