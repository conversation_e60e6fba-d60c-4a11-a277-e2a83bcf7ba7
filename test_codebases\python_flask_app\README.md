# Flask User Management API

A simple Flask application for user management with SQLAlchemy and authentication.

## Features

- User CRUD operations
- Authentication with Flask-Login
- SQLAlchemy ORM integration
- Environment-based configuration
- RESTful API design

## Setup

1. Install dependencies: `pip install -r requirements.txt`
2. Set environment variables
3. Run the application: `python app.py`

## API Endpoints

- GET /api/users - Get all users (requires authentication)
- POST /api/users - Create new user
