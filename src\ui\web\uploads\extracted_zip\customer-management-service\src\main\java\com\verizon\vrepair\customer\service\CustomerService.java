package com.verizon.vrepair.customer.service;

import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Customer service interface.
 * Defines business operations for customer management.
 * Replaces legacy C++ customer service functions.
 */
public interface CustomerService {
    
    /**
     * Get all customers with pagination.
     * Replaces legacy C++ function: get_all_customers()
     */
    Page<Customer> getAllCustomers(Pageable pageable);
    
    /**
     * Create a new customer.
     * Replaces legacy C++ function: create_customer()
     */
    Customer createCustomer(Customer customer);
    
    /**
     * Update existing customer.
     * Replaces legacy C++ function: update_customer()
     */
    Customer updateCustomer(String customerCode, Customer customer);
    
    /**
     * Find customer by ID.
     * Replaces legacy C++ function: get_customer_by_id()
     */
    Customer findCustomerById(String customerCode);
    
    /**
     * Find customer by phone number.
     * Replaces legacy C++ function: find_customer_by_phone()
     */
    Customer findCustomerByPhoneNumber(String phoneNumber);
    
    /**
     * Search customers with multiple criteria.
     * Replaces legacy C++ function: search_customers()
     */
    List<Customer> searchCustomers(String customerName, String phoneNumber, 
                                  CustomerType customerType, CustomerStatus customerStatus,
                                  String serviceAddress, int page, int size);
    
    /**
     * Deactivate customer.
     * Replaces legacy C++ function: deactivate_customer()
     */
    Customer deactivateCustomer(String customerCode);
    
    /**
     * Activate customer.
     * Replaces legacy C++ function: activate_customer()
     */
    Customer activateCustomer(String customerCode);
    
    /**
     * Validate customer eligibility for service.
     * Replaces legacy C++ function: validate_customer_eligibility()
     */
    boolean validateCustomerEligibility(String customerCode);
    
    /**
     * Get customer statistics.
     * Replaces legacy C++ function: get_customer_stats()
     */
    CustomerStatistics getCustomerStatistics();
    
    /**
     * Customer statistics data structure.
     */
    class CustomerStatistics {
        private final long totalCustomers;
        private final long activeCustomers;
        private final long inactiveCustomers;
        private final long residentialCustomers;
        private final long businessCustomers;
        
        public CustomerStatistics(long totalCustomers, long activeCustomers, long inactiveCustomers,
                                long residentialCustomers, long businessCustomers) {
            this.totalCustomers = totalCustomers;
            this.activeCustomers = activeCustomers;
            this.inactiveCustomers = inactiveCustomers;
            this.residentialCustomers = residentialCustomers;
            this.businessCustomers = businessCustomers;
        }
        
        public long getTotalCustomers() { return totalCustomers; }
        public long getActiveCustomers() { return activeCustomers; }
        public long getInactiveCustomers() { return inactiveCustomers; }
        public long getResidentialCustomers() { return residentialCustomers; }
        public long getBusinessCustomers() { return businessCustomers; }
    }
}
