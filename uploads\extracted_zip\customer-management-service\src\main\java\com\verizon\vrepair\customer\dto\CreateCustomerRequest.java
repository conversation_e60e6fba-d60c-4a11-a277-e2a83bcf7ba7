package com.verizon.vrepair.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Request DTO for creating a new customer.
 * Contains validation rules matching legacy C++ system requirements.
 */
@Schema(description = "Request to create a new customer")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateCustomerRequest {
    
    @NotBlank(message = "Billing telephone number is required")
    @Pattern(regexp = "^\\d{10}$", message = "Phone number must be 10 digits")
    @Schema(description = "Billing telephone number (10 digits)", example = "5551234567")
    private String billingTelephoneNumber;
    
    @NotBlank(message = "Service address is required")
    @Size(min = 1, max = 100, message = "Service address must be between 1 and 100 characters")
    @Schema(description = "Customer service address", example = "123 Main St, Anytown, ST 12345")
    private String serviceAddress;
    
    @Size(max = 50, message = "Customer name must not exceed 50 characters")
    @Schema(description = "Customer name", example = "<PERSON>")
    private String customerName;
    
    @NotBlank(message = "Customer type is required")
    @Pattern(regexp = "^(RESIDENTIAL|BUSINESS|GOVERNMENT|WHOLESALE)$", 
             message = "Customer type must be RESIDENTIAL, BUSINESS, GOVERNMENT, or WHOLESALE")
    @Schema(description = "Customer type", example = "RESIDENTIAL", 
            allowableValues = {"RESIDENTIAL", "BUSINESS", "GOVERNMENT", "WHOLESALE"})
    private String customerType;
    
    @Size(max = 20, message = "Account number must not exceed 20 characters")
    @Schema(description = "Account number (required for business customers)", example = "ACC123456")
    private String accountNumber;
    
    @Size(max = 10, message = "Service class code must not exceed 10 characters")
    @Schema(description = "Service class code", example = "RES001")
    private String serviceClassCode;
    
    @Size(max = 20, message = "Maintenance level must not exceed 20 characters")
    @Schema(description = "Maintenance level", example = "STANDARD", 
            allowableValues = {"BASIC", "STANDARD", "PREMIUM", "ENHANCED"})
    private String maintenanceLevel;
    
    @Pattern(regexp = "^\\d{10}$|^$", message = "Contact phone must be 10 digits or empty")
    @Schema(description = "Contact phone number (10 digits)", example = "**********")
    private String contactPhone;
    
    @Pattern(regexp = "^\\d{10}$|^$", message = "Alternate contact must be 10 digits or empty")
    @Schema(description = "Alternate contact phone number (10 digits)", example = "**********")
    private String alternateContact;
    
    @Email(message = "Email address must be valid")
    @Size(max = 100, message = "Email address must not exceed 100 characters")
    @Schema(description = "Email address", example = "<EMAIL>")
    private String emailAddress;
    
    @Size(max = 500, message = "Special instructions must not exceed 500 characters")
    @Schema(description = "Special instructions or notes", 
            example = "Customer prefers morning appointments")
    private String specialInstructions;
    
    // Default constructor
    public CreateCustomerRequest() {}
    
    // Getters and setters
    public String getBillingTelephoneNumber() {
        return billingTelephoneNumber;
    }
    
    public void setBillingTelephoneNumber(String billingTelephoneNumber) {
        this.billingTelephoneNumber = billingTelephoneNumber;
    }
    
    public String getServiceAddress() {
        return serviceAddress;
    }
    
    public void setServiceAddress(String serviceAddress) {
        this.serviceAddress = serviceAddress;
    }
    
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getCustomerType() {
        return customerType;
    }
    
    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }
    
    public String getAccountNumber() {
        return accountNumber;
    }
    
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }
    
    public String getServiceClassCode() {
        return serviceClassCode;
    }
    
    public void setServiceClassCode(String serviceClassCode) {
        this.serviceClassCode = serviceClassCode;
    }
    
    public String getMaintenanceLevel() {
        return maintenanceLevel;
    }
    
    public void setMaintenanceLevel(String maintenanceLevel) {
        this.maintenanceLevel = maintenanceLevel;
    }
    
    public String getContactPhone() {
        return contactPhone;
    }
    
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
    
    public String getAlternateContact() {
        return alternateContact;
    }
    
    public void setAlternateContact(String alternateContact) {
        this.alternateContact = alternateContact;
    }
    
    public String getEmailAddress() {
        return emailAddress;
    }
    
    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }
    
    public String getSpecialInstructions() {
        return specialInstructions;
    }
    
    public void setSpecialInstructions(String specialInstructions) {
        this.specialInstructions = specialInstructions;
    }
    
    @Override
    public String toString() {
        return "CreateCustomerRequest{" +
                "billingTelephoneNumber='" + billingTelephoneNumber + '\'' +
                ", serviceAddress='" + serviceAddress + '\'' +
                ", customerName='" + customerName + '\'' +
                ", customerType='" + customerType + '\'' +
                ", accountNumber='" + accountNumber + '\'' +
                '}';
    }
}
