package com.verizon.vrepair.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Request DTO for updating an existing customer.
 * All fields are optional for partial updates.
 */
@Schema(description = "Request to update customer information")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateCustomerRequest {
    
    @Size(max = 50, message = "Customer name must not exceed 50 characters")
    @Schema(description = "Customer name", example = "<PERSON> Smith")
    private String customerName;
    
    @Size(min = 1, max = 100, message = "Service address must be between 1 and 100 characters")
    @Schema(description = "Customer service address", example = "123 Main St, Anytown, ST 12345")
    private String serviceAddress;
    
    @Pattern(regexp = "^\\d{10}$|^$", message = "Contact phone must be 10 digits or empty")
    @Schema(description = "Contact phone number (10 digits)", example = "5559876543")
    private String contactPhone;
    
    @Pattern(regexp = "^\\d{10}$|^$", message = "Alternate contact must be 10 digits or empty")
    @Schema(description = "Alternate contact phone number (10 digits)", example = "5555551234")
    private String alternateContact;
    
    @Email(message = "Email address must be valid")
    @Size(max = 100, message = "Email address must not exceed 100 characters")
    @Schema(description = "Email address", example = "<EMAIL>")
    private String emailAddress;
    
    @Size(max = 500, message = "Special instructions must not exceed 500 characters")
    @Schema(description = "Special instructions or notes", 
            example = "Customer prefers morning appointments")
    private String specialInstructions;
    
    // Default constructor
    public UpdateCustomerRequest() {}
    
    // Getters and setters
    public String getCustomerName() {
        return customerName;
    }
    
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
    
    public String getServiceAddress() {
        return serviceAddress;
    }
    
    public void setServiceAddress(String serviceAddress) {
        this.serviceAddress = serviceAddress;
    }
    
    public String getContactPhone() {
        return contactPhone;
    }
    
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
    
    public String getAlternateContact() {
        return alternateContact;
    }
    
    public void setAlternateContact(String alternateContact) {
        this.alternateContact = alternateContact;
    }
    
    public String getEmailAddress() {
        return emailAddress;
    }
    
    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }
    
    public String getSpecialInstructions() {
        return specialInstructions;
    }
    
    public void setSpecialInstructions(String specialInstructions) {
        this.specialInstructions = specialInstructions;
    }
    
    @Override
    public String toString() {
        return "UpdateCustomerRequest{" +
                "customerName='" + customerName + '\'' +
                ", serviceAddress='" + serviceAddress + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", emailAddress='" + emailAddress + '\'' +
                '}';
    }
}
