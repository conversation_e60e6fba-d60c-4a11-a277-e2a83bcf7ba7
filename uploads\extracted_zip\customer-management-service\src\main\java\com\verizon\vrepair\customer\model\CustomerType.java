package com.verizon.vrepair.customer.model;

/**
 * Customer type enumeration.
 * Replaces legacy C++ customer type constants with type-safe enum.
 * 
 * Legacy C++ mapping:
 * - RES -> RESIDENTIAL
 * - BUS -> BUSINESS  
 * - GOV -> GOVERNMENT
 * - WHO -> WHOLESALE
 */
public enum CustomerType {
    RESIDENTIAL("Residential customer"),
    BUSINESS("Business customer"),
    GOVERNMENT("Government customer"),
    WHOLESALE("Wholesale customer");
    
    private final String description;
    
    CustomerType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Convert from legacy C++ customer type codes.
     * Maintains backward compatibility with legacy system.
     * 
     * @param legacyCode Legacy customer type code (RES, BUS, GOV, WHO)
     * @return Corresponding CustomerType enum
     * @throws IllegalArgumentException if legacy code is not recognized
     */
    public static CustomerType fromLegacyCode(String legacyCode) {
        if (legacyCode == null || legacyCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Legacy customer type code cannot be null or empty");
        }
        
        return switch (legacyCode.trim().toUpperCase()) {
            case "RES" -> RESIDENTIAL;
            case "BUS" -> BUSINESS;
            case "GOV" -> GOVERNMENT;
            case "WHO" -> WHOLESALE;
            default -> throw new IllegalArgumentException("Unknown legacy customer type code: " + legacyCode);
        };
    }
    
    /**
     * Convert to legacy C++ customer type code.
     * Used for integration with legacy systems that still expect old codes.
     * 
     * @return Legacy customer type code
     */
    public String toLegacyCode() {
        return switch (this) {
            case RESIDENTIAL -> "RES";
            case BUSINESS -> "BUS";
            case GOVERNMENT -> "GOV";
            case WHOLESALE -> "WHO";
        };
    }
    
    /**
     * Get customer code prefix used in customer code generation.
     * Maintains legacy customer code format: [PREFIX][SEQUENCE]
     * 
     * @return Customer code prefix
     */
    public String getCodePrefix() {
        return switch (this) {
            case RESIDENTIAL -> "RES";
            case BUSINESS -> "BUS";
            case GOVERNMENT -> "GOV";
            case WHOLESALE -> "WHO";
        };
    }
}


