package com.verizon.vrepair.customer.repository;

import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Customer repository interface.
 * Replaces legacy C++ Pro*C database access with Spring Data JPA.
 * 
 * Legacy C++ equivalent: customer_db.pc Pro*C embedded SQL functions
 */
@Repository
public interface CustomerRepository extends JpaRepository<Customer, String> {
    
    /**
     * Find customer by phone number.
     * Replaces legacy C++ function: find_customer_by_phone()
     */
    @Query("SELECT c FROM Customer c WHERE c.billingTelephoneNumber = :phoneNumber")
    Optional<Customer> findByPhoneNumber(@Param("phoneNumber") String phoneNumber);
    
    /**
     * Find customers by service address containing text.
     * Replaces legacy C++ function: search_customers_by_address()
     */
    @Query("SELECT c FROM Customer c WHERE UPPER(c.serviceAddress) LIKE UPPER(CONCAT('%', :address, '%'))")
    Page<Customer> findByServiceAddressContaining(@Param("address") String address, Pageable pageable);
    
    /**
     * Find customers by name containing text.
     * Replaces legacy C++ function: search_customers_by_name()
     */
    @Query("SELECT c FROM Customer c WHERE UPPER(c.customerName) LIKE UPPER(CONCAT('%', :name, '%'))")
    Page<Customer> findByCustomerNameContaining(@Param("name") String name, Pageable pageable);
    
    /**
     * Find customers by account number.
     * Replaces legacy C++ function: find_customer_by_account()
     */
    Optional<Customer> findByAccountNumber(String accountNumber);
    
    /**
     * Find customers by type and status.
     * Replaces legacy C++ function: get_customers_by_type_status()
     */
    Page<Customer> findByCustomerTypeAndCustomerStatus(CustomerType customerType, 
                                                       CustomerStatus customerStatus, 
                                                       Pageable pageable);
    
    /**
     * Complex customer search with multiple criteria.
     * Replaces legacy C++ function: search_customers_advanced()
     */
    @Query("SELECT c FROM Customer c WHERE " +
           "(:customerName IS NULL OR UPPER(c.customerName) LIKE UPPER(CONCAT('%', :customerName, '%'))) AND " +
           "(:phoneNumber IS NULL OR c.billingTelephoneNumber = :phoneNumber) AND " +
           "(:customerType IS NULL OR c.customerType = :customerType) AND " +
           "(:customerStatus IS NULL OR c.customerStatus = :customerStatus) AND " +
           "(:serviceAddress IS NULL OR UPPER(c.serviceAddress) LIKE UPPER(CONCAT('%', :serviceAddress, '%')))")
    Page<Customer> searchCustomers(@Param("customerName") String customerName,
                                  @Param("phoneNumber") String phoneNumber,
                                  @Param("customerType") CustomerType customerType,
                                  @Param("customerStatus") CustomerStatus customerStatus,
                                  @Param("serviceAddress") String serviceAddress,
                                  Pageable pageable);
    
    /**
     * Count customers by status.
     * Replaces legacy C++ function: get_customer_count_by_status()
     */
    long countByCustomerStatus(CustomerStatus customerStatus);
    
    /**
     * Count customers by type.
     * Replaces legacy C++ function: get_customer_count_by_type()
     */
    long countByCustomerType(CustomerType customerType);
    
    /**
     * Get customer statistics.
     * Replaces legacy C++ function: get_customer_statistics()
     */
    @Query("SELECT " +
           "COUNT(c) as totalCustomers, " +
           "SUM(CASE WHEN c.customerStatus = 'ACTIVE' THEN 1 ELSE 0 END) as activeCustomers, " +
           "SUM(CASE WHEN c.customerStatus = 'INACTIVE' THEN 1 ELSE 0 END) as inactiveCustomers, " +
           "SUM(CASE WHEN c.customerType = 'RESIDENTIAL' THEN 1 ELSE 0 END) as residentialCustomers, " +
           "SUM(CASE WHEN c.customerType = 'BUSINESS' THEN 1 ELSE 0 END) as businessCustomers " +
           "FROM Customer c")
    CustomerStatistics getCustomerStatistics();
    
    /**
     * Customer statistics projection interface.
     */
    interface CustomerStatistics {
        Long getTotalCustomers();
        Long getActiveCustomers();
        Long getInactiveCustomers();
        Long getResidentialCustomers();
        Long getBusinessCustomers();
    }
}
