#!/bin/bash

# Script to kill any process using port 8080
# Use this to free up port 8080 before starting the main service

echo "🔍 Checking for processes using port 8080..."
echo ""

# Find processes using port 8080
PROCESSES=$(lsof -ti :8080)

if [ -z "$PROCESSES" ]; then
    echo "✅ Port 8080 is free - no processes found using this port"
    echo ""
    echo "You can now start the Customer Management Service with:"
    echo "   ./start-with-oracle.sh"
    echo ""
else
    echo "🚨 Found processes using port 8080:"
    lsof -i :8080
    echo ""
    echo "💀 Killing processes using port 8080..."
    
    for pid in $PROCESSES; do
        echo "   Killing process $pid..."
        kill -9 $pid
    done
    
    echo ""
    echo "✅ Port 8080 has been freed"
    echo ""
    echo "You can now start the Customer Management Service with:"
    echo "   ./start-with-oracle.sh"
    echo ""
fi
