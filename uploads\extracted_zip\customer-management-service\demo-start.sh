#!/bin/bash

echo "🚀 Starting VRepair Customer Management Service Demo"
echo "=================================================="

# Clean up any existing processes
echo "🧹 Cleaning up existing processes..."
pkill -f customer-management-service 2>/dev/null
pkill -f spring-boot 2>/dev/null
sleep 3

# Navigate to service directory
cd /Users/<USER>/Downloads/Vrepair_Arch/customer-management-service

# Build if needed
if [ ! -f "target/customer-management-service-1.0.0-SNAPSHOT.jar" ]; then
    echo "🔨 Building application..."
    mvn clean package -DskipTests
fi

# Start Oracle and Redis containers
echo "🐳 Starting database containers..."
docker-compose up -d
sleep 10

# Start the service with H2 database (simpler for demo)
echo "🌟 Starting Customer Management Service..."
echo "📊 Using H2 database for demo"
echo "🌐 Service will be available at: http://localhost:8090/customer-service"
echo "📖 Swagger UI: http://localhost:8090/customer-service/swagger-ui/index.html"
echo "💾 H2 Console: http://localhost:8090/customer-service/h2-console"
echo ""

# Start service in background with output to log
nohup java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar \
  --spring.profiles.active=simple \
  --server.port=8090 \
  --logging.level.root=INFO > demo-service.log 2>&1 &

SERVICE_PID=$!
echo "🔄 Service starting with PID: $SERVICE_PID"
echo "⏳ Waiting for service to initialize..."

# Wait for service to start
for i in {1..20}; do
    if curl -s http://localhost:8090/customer-service/actuator/health > /dev/null 2>&1; then
        echo "✅ Service is ready!"
        break
    fi
    echo "   Attempt $i/20: Waiting for service..."
    sleep 3
done

# Test if service is running
if curl -s http://localhost:8090/customer-service/actuator/health > /dev/null 2>&1; then
    echo ""
    echo "🎉 SUCCESS! Service is running!"
    echo "=================================================="
    echo "📋 Demo URLs:"
    echo "   🏥 Health Check: http://localhost:8090/customer-service/actuator/health"
    echo "   📖 Swagger UI:   http://localhost:8090/customer-service/swagger-ui/index.html"
    echo "   💾 H2 Console:   http://localhost:8090/customer-service/h2-console"
    echo "   📊 Metrics:      http://localhost:8090/customer-service/actuator/metrics"
    echo ""
    echo "🔑 H2 Database Login:"
    echo "   JDBC URL: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE"
    echo "   Username: sa"
    echo "   Password: (leave empty)"
    echo ""
    echo "🎯 Opening Swagger UI in browser..."
    sleep 2
    open http://localhost:8090/customer-service/swagger-ui/index.html
    echo ""
    echo "🛑 To stop the service, run: kill $SERVICE_PID"
    echo "=================================================="
else
    echo "❌ Service failed to start. Check demo-service.log for details."
    tail -20 demo-service.log
fi
