import streamlit as st
import os
import traceback

# Configure Streamlit page
st.set_page_config(
    page_title="MOP Agent Workflow UI - Working Version",
    page_icon="🤖",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state variables."""
    if 'workflow_results' not in st.session_state:
        st.session_state.workflow_results = {}
    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'selected_agents' not in st.session_state:
        st.session_state.selected_agents = set()
    if 'mop_content' not in st.session_state:
        st.session_state.mop_content = ""

def test_claude_connection():
    """Test Claude API connection."""
    try:
        from config import API_KEY
        from anthropic import Anthropic
        
        if not API_KEY:
            return False, "API_KEY not found in config.py"
        
        # Test Claude connection
        client = Anthropic(api_key=API_KEY)
        
        # Simple test message
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=100,
            messages=[{"role": "user", "content": "Hello, respond with '<PERSON> is working!'"}]
        )
        
        return True, f"Claude connection successful: {response.content[0].text}"
        
    except Exception as e:
        return False, f"Claude connection failed: {str(e)}"

def generate_code_with_claude(mop_content):
    """Generate code directly using Claude API."""
    try:
        from config import API_KEY
        from anthropic import Anthropic
        
        # Initialize Claude client
        client = Anthropic(api_key=API_KEY)
        
        # Create prompt for code generation
        prompt = f"""
You are an expert Python developer. Generate a clean and efficient Python script based on the following MOP (Method of Procedure).

MOP Content:
{mop_content}

Please generate a complete Python script that implements the requirements described in the MOP. Include:
- Proper function definitions
- Error handling
- Clear comments and documentation
- Best practices

Return only the Python code without any additional explanation.
"""
        
        # Generate code using Claude
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=2000,
            messages=[{"role": "user", "content": prompt}]
        )
        
        generated_code = response.content[0].text
        return True, generated_code
        
    except Exception as e:
        return False, f"Code generation failed: {str(e)}\n\nFull traceback:\n{traceback.format_exc()}"

def review_code_with_claude(code):
    """Review code using Claude API."""
    try:
        from config import API_KEY
        from anthropic import Anthropic
        
        client = Anthropic(api_key=API_KEY)
        
        prompt = f"""
You are a senior code reviewer. Please review the following Python code and provide feedback on:
- Code quality and best practices
- Potential bugs or issues
- Suggestions for improvement
- Security considerations

Code to review:
{code}

Provide a detailed review with specific recommendations.
"""
        
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=1500,
            messages=[{"role": "user", "content": prompt}]
        )
        
        return True, response.content[0].text
        
    except Exception as e:
        return False, f"Code review failed: {str(e)}"

def main():
    """Main application function."""
    initialize_session_state()
    
    st.title("🤖 MOP Agent Workflow UI - Working Version")
    st.markdown("---")
    
    # Test Claude connection
    st.header("🔧 System Check")
    
    if st.button("Test Claude Connection"):
        with st.spinner("Testing Claude API..."):
            success, message = test_claude_connection()
        
        if success:
            st.success(f"✅ {message}")
        else:
            st.error(f"❌ {message}")
            st.stop()
    
    # MOP Content Input
    st.header("📝 MOP Content")
    
    # Predefined test MOPs
    test_mops = {
        "Simple Calculator": """
MOP: Simple Calculator Application

Objective: Create a basic calculator that can perform arithmetic operations.

Steps:
1. Create functions for add, subtract, multiply, and divide
2. Create a main function that takes user input
3. Handle division by zero errors
4. Display results clearly
5. Allow multiple calculations
""",
        "File Reader": """
MOP: File Reading and Processing

Objective: Create a script that reads and processes text files.

Steps:
1. Create a function to read a text file
2. Count the number of lines and words
3. Find the most common words
4. Save results to an output file
5. Handle file not found errors
""",
        "Custom MOP": ""
    }
    
    selected_mop = st.selectbox("Choose a test MOP or create custom:", list(test_mops.keys()))
    
    if selected_mop == "Custom MOP":
        mop_content = st.text_area(
            "Enter your custom MOP content:",
            height=200,
            placeholder="Enter your MOP content here..."
        )
    else:
        mop_content = st.text_area(
            f"MOP Content ({selected_mop}):",
            value=test_mops[selected_mop],
            height=200
        )
    
    if mop_content:
        st.session_state.mop_content = mop_content
        st.success("✅ MOP content loaded")
    
    # Agent Selection
    st.header("🤖 Agent Selection")
    
    col1, col2 = st.columns(2)
    
    with col1:
        use_code_generator = st.checkbox("💻 Code Generator", value=True)
    with col2:
        use_code_reviewer = st.checkbox("🔍 Code Reviewer", value=False)
    
    selected_agents = set()
    if use_code_generator:
        selected_agents.add('coder')
    if use_code_reviewer:
        selected_agents.add('reviewer')
    
    st.session_state.selected_agents = selected_agents
    
    # Run Workflow
    st.header("🚀 Workflow Execution")
    
    if st.button("Run Workflow", disabled=st.session_state.workflow_running):
        if not st.session_state.mop_content:
            st.error("❌ Please enter MOP content first!")
        elif not st.session_state.selected_agents:
            st.error("❌ Please select at least one agent!")
        else:
            st.session_state.workflow_running = True
            st.session_state.workflow_results = {}
            
            # Code Generation
            if 'coder' in st.session_state.selected_agents:
                with st.spinner("💻 Generating code..."):
                    success, result = generate_code_with_claude(st.session_state.mop_content)
                
                if success:
                    st.session_state.workflow_results['Code Generator'] = {
                        'status': 'success',
                        'output': result
                    }
                    
                    # Save generated code
                    try:
                        os.makedirs("./output", exist_ok=True)
                        with open("./output/generated_code.py", "w") as f:
                            f.write(result)
                    except Exception as e:
                        st.warning(f"Could not save file: {e}")
                        
                else:
                    st.session_state.workflow_results['Code Generator'] = {
                        'status': 'error',
                        'output': result
                    }
            
            # Code Review
            if 'reviewer' in st.session_state.selected_agents and 'Code Generator' in st.session_state.workflow_results:
                if st.session_state.workflow_results['Code Generator']['status'] == 'success':
                    generated_code = st.session_state.workflow_results['Code Generator']['output']
                    
                    with st.spinner("🔍 Reviewing code..."):
                        success, result = review_code_with_claude(generated_code)
                    
                    if success:
                        st.session_state.workflow_results['Code Reviewer'] = {
                            'status': 'success',
                            'output': result
                        }
                    else:
                        st.session_state.workflow_results['Code Reviewer'] = {
                            'status': 'error',
                            'output': result
                        }
            
            st.session_state.workflow_running = False
            st.rerun()
    
    # Results Display
    if st.session_state.workflow_results:
        st.header("📊 Results")
        
        for agent_name, result in st.session_state.workflow_results.items():
            with st.expander(f"📋 {agent_name} Results", expanded=True):
                if result.get('status') == 'success':
                    st.success("✅ Success")
                    
                    if agent_name == 'Code Generator':
                        st.subheader("Generated Code:")
                        st.code(result.get('output', ''), language='python')
                        
                        # Download button
                        st.download_button(
                            "📥 Download Code",
                            result.get('output', ''),
                            file_name="generated_code.py",
                            mime="text/plain"
                        )
                    else:
                        st.text_area("Output", result.get('output', ''), height=300)
                        
                else:
                    st.error("❌ Error")
                    st.text_area("Error Details", result.get('output', ''), height=200)
        
        # Clear results button
        if st.button("🗑️ Clear Results"):
            st.session_state.workflow_results = {}
            st.rerun()

if __name__ == "__main__":
    main()
