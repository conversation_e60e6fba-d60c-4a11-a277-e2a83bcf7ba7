import os
import json
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set, Union
from dataclasses import dataclass, asdict, field
import numpy as np
from collections import defaultdict
import asyncio
from concurrent.futures import ThreadPoolExecutor
import logging
import time
from functools import lru_cache

# AutoGen imports
import autogen
from autogen import AssistantAgent, UserProxyAgent

# Anthropic API
import anthropic
from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT

# Database connections
import psycopg2
from psycopg2.extras import Json, execute_values
from pgvector.psycopg2 import register_vector
from neo4j import GraphDatabase
import redis

# Code parsing
import ast
import libcst as cst
import astroid

# Embeddings
from sentence_transformers import SentenceTransformer
import torch

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ParsedEntity:
    """Parsed code entity with metadata"""
    id: str
    file_path: str
    entity_type: str
    name: str
    qualified_name: str
    start_line: int
    end_line: int
    start_col: int
    end_col: int
    source_code: str
    ast_digest: str
    
    # Structure
    parameters: List[Dict[str, Any]] = field(default_factory=list)
    return_type: Optional[Dict[str, Any]] = None
    type_annotations: Dict[str, str] = field(default_factory=dict)
    
    # Metrics
    complexity: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, Any] = field(default_factory=dict)
    
    # Dependencies
    imports: List[Dict[str, Any]] = field(default_factory=list)
    calls: List[Dict[str, Any]] = field(default_factory=list)
    decorators: List[Dict[str, Any]] = field(default_factory=list)
    
    # Claude analysis
    claude_analysis: Optional[Dict[str, Any]] = None

@dataclass
class ClaudeAnalysis:
    """Analysis results from Claude Sonnet 4"""
    purpose: str
    business_context: str
    design_patterns: List[str]
    quality_insights: Dict[str, Any]
    relationships: Dict[str, Any]
    domain_concepts: List[str]
    improvement_suggestions: List[str]
    test_scenarios: List[str]

class ClaudeAnalyzer:
    """Analyze code using Claude Sonnet 4"""
    
    def __init__(self, api_key: str):
        self.client = Anthropic(api_key=api_key)
        self.model = "claude-3-5-sonnet-20241022"  # Claude Sonnet 4
        self.cache = redis.Redis(host='localhost', port=6379, decode_responses=True)
        
    @lru_cache(maxsize=100)
    def analyze_code(self, code: str, context: Dict[str, Any]) -> ClaudeAnalysis:
        """Analyze code using Claude Sonnet 4"""
        
        # Check cache first
        cache_key = f"claude_analysis:{hashlib.md5(code.encode()).hexdigest()}"
        cached = self.cache.get(cache_key)
        if cached:
            return self._parse_analysis(json.loads(cached))
        
        prompt = self._build_analysis_prompt(code, context)
        
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=2000,
                temperature=0.1,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            )
            
            # Parse response
            result = self._parse_claude_response(response.content[0].text)
            
            # Cache result
            self.cache.setex(cache_key, 3600, json.dumps(result))
            
            return self._parse_analysis(result)
            
        except Exception as e:
            logger.error(f"Claude API error: {e}")
            # Return minimal analysis on error
            return ClaudeAnalysis(
                purpose="Error analyzing code",
                business_context="",
                design_patterns=[],
                quality_insights={},
                relationships={},
                domain_concepts=[],
                improvement_suggestions=[],
                test_scenarios=[]
            )
    
    def _build_analysis_prompt(self, code: str, context: Dict[str, Any]) -> str:
        """Build comprehensive analysis prompt"""
        
        return f"""Analyze this code and provide a comprehensive JSON response with the following structure:

{{
    "purpose": "Main purpose and functionality",
    "business_context": "Business logic and domain context",
    "design_patterns": ["List", "of", "patterns"],
    "quality_insights": {{
        "maintainability": "assessment",
        "testability": "assessment",
        "complexity": "assessment",
        "security_concerns": ["list of concerns"],
        "performance_considerations": ["list of considerations"]
    }},
    "relationships": {{
        "calls": ["functions this code calls"],
        "called_by": ["likely callers based on purpose"],
        "related_concepts": ["related domain concepts"],
        "dependencies": ["external dependencies"]
    }},
    "domain_concepts": ["business", "domain", "concepts"],
    "improvement_suggestions": ["specific", "actionable", "suggestions"],
    "test_scenarios": ["key test cases to consider"]
}}

Context:
- File: {context.get('file_path', 'Unknown')}
- Module: {context.get('module_name', 'Unknown')}
- Related entities: {', '.join(context.get('related_entities', []))}

Code to analyze:
```python
{code[:2000]}  # Limit for token efficiency
```

Provide only the JSON response, no additional text."""

    def _parse_claude_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON from Claude's response"""
        
        try:
            # Find JSON in response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # Try parsing entire response as JSON
                return json.loads(response)
                
        except json.JSONDecodeError:
            logger.error(f"Failed to parse Claude response as JSON")
            # Return structured default
            return {
                "purpose": "Failed to parse response",
                "business_context": "",
                "design_patterns": [],
                "quality_insights": {},
                "relationships": {},
                "domain_concepts": [],
                "improvement_suggestions": [],
                "test_scenarios": []
            }
    
    def _parse_analysis(self, data: Dict[str, Any]) -> ClaudeAnalysis:
        """Convert dict to ClaudeAnalysis object"""
        
        return ClaudeAnalysis(
            purpose=data.get('purpose', ''),
            business_context=data.get('business_context', ''),
            design_patterns=data.get('design_patterns', []),
            quality_insights=data.get('quality_insights', {}),
            relationships=data.get('relationships', {}),
            domain_concepts=data.get('domain_concepts', []),
            improvement_suggestions=data.get('improvement_suggestions', []),
            test_scenarios=data.get('test_scenarios', [])
        )

class CodeParser:
    """Multi-strategy code parser"""
    
    def parse_file(self, file_path: str) -> List[ParsedEntity]:
        """Parse file using AST"""
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        entities = []
        
        try:
            tree = ast.parse(content)
            
            # Add parent references
            for parent in ast.walk(tree):
                for child in ast.iter_child_nodes(parent):
                    child.parent = parent
            
            # Extract entities
            for node in ast.walk(tree):
                entity = None
                
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    entity = self._parse_function(node, content, file_path)
                elif isinstance(node, ast.ClassDef):
                    entity = self._parse_class(node, content, file_path)
                
                if entity:
                    entities.append(entity)
                    
        except SyntaxError as e:
            logger.error(f"Parsing error in {file_path}: {e}")
            
        return entities
    
    def _parse_function(self, node: ast.FunctionDef, content: str, file_path: str) -> ParsedEntity:
        """Parse function details"""
        
        # Extract parameters
        parameters = []
        for arg in node.args.args:
            param_info = {
                'name': arg.arg,
                'type': ast.unparse(arg.annotation) if arg.annotation else None,
            }
            parameters.append(param_info)
        
        # Extract return type
        return_type = None
        if node.returns:
            return_type = {
                'annotation': ast.unparse(node.returns),
                'is_async': isinstance(node, ast.AsyncFunctionDef)
            }
        
        # Calculate complexity
        complexity = self._calculate_complexity(node)
        
        # Extract calls
        calls = []
        for child in ast.walk(node):
            if isinstance(child, ast.Call):
                if isinstance(child.func, ast.Name):
                    calls.append({'name': child.func.id, 'line': child.lineno})
                elif isinstance(child.func, ast.Attribute):
                    calls.append({'name': child.func.attr, 'line': child.lineno})
        
        return ParsedEntity(
            id=f"{file_path}:{node.name}:{node.lineno}",
            file_path=file_path,
            entity_type="function",
            name=node.name,
            qualified_name=self._get_qualified_name(node),
            start_line=node.lineno,
            end_line=node.end_lineno or node.lineno,
            start_col=node.col_offset,
            end_col=node.end_col_offset or node.col_offset,
            source_code=ast.get_source_segment(content, node) or "",
            ast_digest=hashlib.md5(ast.dump(node).encode()).hexdigest(),
            parameters=parameters,
            return_type=return_type,
            complexity=complexity,
            calls=calls
        )
    
    def _parse_class(self, node: ast.ClassDef, content: str, file_path: str) -> ParsedEntity:
        """Parse class details"""
        
        # Extract base classes
        bases = [ast.unparse(base) for base in node.bases]
        
        # Extract methods
        methods = []
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                methods.append(item.name)
        
        return ParsedEntity(
            id=f"{file_path}:{node.name}:{node.lineno}",
            file_path=file_path,
            entity_type="class",
            name=node.name,
            qualified_name=self._get_qualified_name(node),
            start_line=node.lineno,
            end_line=node.end_lineno or node.lineno,
            start_col=node.col_offset,
            end_col=node.end_col_offset or node.col_offset,
            source_code=ast.get_source_segment(content, node) or "",
            ast_digest=hashlib.md5(ast.dump(node).encode()).hexdigest(),
            metrics={'methods': len(methods), 'bases': bases}
        )
    
    def _calculate_complexity(self, node: ast.AST) -> Dict[str, int]:
        """Calculate cyclomatic complexity"""
        
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return {'cyclomatic': complexity}
    
    def _get_qualified_name(self, node: ast.AST) -> str:
        """Get fully qualified name"""
        
        parts = [node.name]
        current = getattr(node, 'parent', None)
        
        while current:
            if isinstance(current, (ast.ClassDef, ast.FunctionDef)):
                parts.append(current.name)
            current = getattr(current, 'parent', None)
        
        return '.'.join(reversed(parts))

class PGVectorStore:
    """PostgreSQL with pgvector for embeddings"""
    
    def __init__(self, connection_params: Dict[str, str]):
        self.conn = psycopg2.connect(**connection_params)
        register_vector(self.conn)
        self._create_tables()
        
        # Multiple embedding models
        self.models = {
            'code': SentenceTransformer('microsoft/codebert-base'),
            'doc': SentenceTransformer('all-mpnet-base-v2'),
            'identifier': SentenceTransformer('all-MiniLM-L6-v2')
        }
    
    def _create_tables(self):
        """Create tables with vector columns"""
        
        with self.conn.cursor() as cur:
            cur.execute("""
                CREATE TABLE IF NOT EXISTS code_entities (
                    id TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    entity_type TEXT NOT NULL,
                    name TEXT NOT NULL,
                    qualified_name TEXT NOT NULL,
                    source_code TEXT,
                    metadata JSONB,
                    claude_analysis JSONB,
                    code_embedding vector(768),
                    doc_embedding vector(768),
                    identifier_embedding vector(384),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_code_embedding 
                ON code_entities USING ivfflat (code_embedding vector_cosine_ops)
                WITH (lists = 100);
                
                CREATE INDEX IF NOT EXISTS idx_doc_embedding 
                ON code_entities USING ivfflat (doc_embedding vector_cosine_ops)
                WITH (lists = 100);
                
                CREATE INDEX IF NOT EXISTS idx_name ON code_entities(name);
                CREATE INDEX IF NOT EXISTS idx_file_path ON code_entities(file_path);
            """)
            self.conn.commit()
    
    def store_entity(self, entity: ParsedEntity, claude_analysis: ClaudeAnalysis):
        """Store entity with embeddings and Claude analysis"""
        
        # Generate embeddings
        code_embedding = self.models['code'].encode(entity.source_code)
        
        # Enhanced doc embedding with Claude insights
        doc_text = f"""
        {entity.source_code}
        Purpose: {claude_analysis.purpose}
        Business Context: {claude_analysis.business_context}
        Domain: {', '.join(claude_analysis.domain_concepts)}
        """
        doc_embedding = self.models['doc'].encode(doc_text)
        
        # Identifier embedding
        identifier_text = f"{entity.name} {entity.qualified_name}"
        identifier_embedding = self.models['identifier'].encode(identifier_text)
        
        # Prepare metadata
        metadata = {
            'complexity': entity.complexity,
            'parameters': entity.parameters,
            'return_type': entity.return_type,
            'calls': entity.calls,
            'metrics': entity.metrics
        }
        
        # Store Claude analysis
        claude_data = asdict(claude_analysis)
        
        with self.conn.cursor() as cur:
            cur.execute("""
                INSERT INTO code_entities 
                (id, file_path, entity_type, name, qualified_name, source_code,
                 metadata, claude_analysis, code_embedding, doc_embedding, identifier_embedding)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    source_code = EXCLUDED.source_code,
                    metadata = EXCLUDED.metadata,
                    claude_analysis = EXCLUDED.claude_analysis,
                    code_embedding = EXCLUDED.code_embedding,
                    doc_embedding = EXCLUDED.doc_embedding,
                    identifier_embedding = EXCLUDED.identifier_embedding,
                    updated_at = CURRENT_TIMESTAMP
            """, (
                entity.id,
                entity.file_path,
                entity.entity_type,
                entity.name,
                entity.qualified_name,
                entity.source_code,
                Json(metadata),
                Json(claude_data),
                code_embedding,
                doc_embedding,
                identifier_embedding
            ))
            self.conn.commit()

class Neo4jGraphBuilder:
    """Build knowledge graph in Neo4j"""
    
    def __init__(self, uri: str, user: str, password: str):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self._create_constraints()
    
    def _create_constraints(self):
        """Create indexes and constraints"""
        
        with self.driver.session() as session:
            constraints = [
                "CREATE CONSTRAINT IF NOT EXISTS FOR (n:CodeEntity) REQUIRE n.id IS UNIQUE",
                "CREATE INDEX IF NOT EXISTS FOR (n:CodeEntity) ON (n.name)",
                "CREATE INDEX IF NOT EXISTS FOR (n:CodeEntity) ON (n.file_path)",
                "CREATE INDEX IF NOT EXISTS FOR (n:DesignPattern) ON (n.name)",
                "CREATE INDEX IF NOT EXISTS FOR (n:DomainConcept) ON (n.name)",
                "CREATE CONSTRAINT IF NOT EXISTS FOR (n:Module) REQUIRE n.path IS UNIQUE"
            ]
            
            for constraint in constraints:
                try:
                    session.run(constraint)
                except Exception as e:
                    logger.debug(f"Constraint: {e}")
    
    def store_entity_with_analysis(self, entity: ParsedEntity, analysis: ClaudeAnalysis):
        """Store entity with Claude-inferred relationships"""
        
        with self.driver.session() as session:
            # Create entity node
            session.run("""
                MERGE (e:CodeEntity {id: $id})
                SET e.name = $name,
                    e.qualified_name = $qualified_name,
                    e.entity_type = $entity_type,
                    e.file_path = $file_path,
                    e.purpose = $purpose,
                    e.business_context = $business_context,
                    e.complexity = $complexity,
                    e.quality_score = $quality_score
            """, {
                'id': entity.id,
                'name': entity.name,
                'qualified_name': entity.qualified_name,
                'entity_type': entity.entity_type,
                'file_path': entity.file_path,
                'purpose': analysis.purpose,
                'business_context': analysis.business_context,
                'complexity': entity.complexity.get('cyclomatic', 0),
                'quality_score': self._calculate_quality_score(analysis)
            })
            
            # Create module relationship
            session.run("""
                MERGE (m:Module {path: $path})
                MERGE (e:CodeEntity {id: $entity_id})
                MERGE (e)-[:DEFINED_IN]->(m)
            """, {
                'path': entity.file_path,
                'entity_id': entity.id
            })
            
            # Create design pattern relationships
            for pattern in analysis.design_patterns:
                session.run("""
                    MERGE (p:DesignPattern {name: $pattern})
                    MERGE (e:CodeEntity {id: $entity_id})
                    MERGE (e)-[:IMPLEMENTS_PATTERN]->(p)
                """, {
                    'pattern': pattern,
                    'entity_id': entity.id
                })
            
            # Create domain concept relationships
            for concept in analysis.domain_concepts:
                session.run("""
                    MERGE (c:DomainConcept {name: $concept})
                    MERGE (e:CodeEntity {id: $entity_id})
                    MERGE (e)-[:IMPLEMENTS_CONCEPT]->(c)
                """, {
                    'concept': concept,
                    'entity_id': entity.id
                })
            
            # Create call relationships
            for call in entity.calls:
                session.run("""
                    MATCH (caller:CodeEntity {id: $caller_id})
                    MERGE (callee:CodeEntity {name: $callee_name})
                    MERGE (caller)-[:CALLS {line: $line}]->(callee)
                """, {
                    'caller_id': entity.id,
                    'callee_name': call['name'],
                    'line': call.get('line', 0)
                })
            
            # Create Claude-inferred relationships
            relationships = analysis.relationships
            
            # Related concepts
            for concept in relationships.get('related_concepts', []):
                session.run("""
                    MATCH (e:CodeEntity {id: $entity_id})
                    MERGE (c:DomainConcept {name: $concept})
                    MERGE (e)-[:RELATED_TO_CONCEPT]->(c)
                """, {
                    'entity_id': entity.id,
                    'concept': concept
                })
    
    def _calculate_quality_score(self, analysis: ClaudeAnalysis) -> float:
        """Calculate quality score from Claude analysis"""
        
        score = 1.0
        
        # Deduct for quality issues
        insights = analysis.quality_insights
        if insights.get('maintainability') == 'poor':
            score -= 0.2
        if insights.get('testability') == 'poor':
            score -= 0.2
        if len(insights.get('security_concerns', [])) > 0:
            score -= 0.1 * len(insights['security_concerns'])
        
        # Bonus for patterns
        score += 0.05 * len(analysis.design_patterns)
        
        return max(0.0, min(1.0, score))

class ClaudeParsingAgent:
    """AutoGen agent using Claude Sonnet 4 for code analysis"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Initialize components
        self.parser = CodeParser()
        self.claude_analyzer = ClaudeAnalyzer(config['anthropic_api_key'])
        self.pg_store = PGVectorStore(config['pgvector'])
        self.neo4j_builder = Neo4jGraphBuilder(
            config['neo4j']['uri'],
            config['neo4j']['user'],
            config['neo4j']['password']
        )
        
        # Documentation directory
        self.doc_dir = Path(config.get('documentation_dir', './docs'))
        
        # Initialize AutoGen with Claude
        self._init_autogen()
    
    def _init_autogen(self):
        """Initialize AutoGen agents"""
        
        self.assistant = AssistantAgent(
            name="claude_parsing_assistant",
            system_message="""You are a sophisticated code parsing agent that uses Claude Sonnet 4 
            for deep code analysis. You can:
            1. Parse code structure using AST
            2. Analyze code purpose and quality using Claude
            3. Create semantic embeddings in PGVector
            4. Build knowledge graphs in Neo4j
            5. Integrate with pre-generated documentation
            
            You provide intelligent insights about code architecture, patterns, and quality.""",
            llm_config={
                "config_list": [{
                    "model": "claude-3-5-sonnet-20241022",
                    "api_key": self.config['anthropic_api_key'],
                    "api_type": "anthropic"
                }]
            }
        )
        
        self.user_proxy = UserProxyAgent(
            name="user_proxy",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=10,
            code_execution_config={"work_dir": "claude_workspace"}
        )
        
        self._register_functions()
    
    def _register_functions(self):
        """Register parsing functions"""
        
        @self.user_proxy.register_for_execution()
        @self.assistant.register_for_llm(description="Parse and analyze a file with Claude")
        def parse_file(file_path: str) -> Dict[str, Any]:
            """Parse file and analyze with Claude"""
            
            try:
                # Parse code structure
                entities = self.parser.parse_file(file_path)
                
                # Load pre-generated documentation if available
                doc_map = self._load_documentation(file_path)
                
                results = []
                for entity in entities:
                    # Prepare context
                    context = {
                        'file_path': file_path,
                        'module_name': Path(file_path).stem,
                        'related_entities': [e.name for e in entities if e != entity][:5],
                        'documentation': doc_map.get(entity.name, {})
                    }
                    
                    # Analyze with Claude
                    analysis = self.claude_analyzer.analyze_code(
                        entity.source_code,
                        context
                    )
                    
                    # Store in PGVector
                    self.pg_store.store_entity(entity, analysis)
                    
                    # Build knowledge graph
                    self.neo4j_builder.store_entity_with_analysis(entity, analysis)
                    
                    results.append({
                        'entity': entity.name,
                        'type': entity.entity_type,
                        'purpose': analysis.purpose,
                        'patterns': analysis.design_patterns,
                        'quality_score': self._calculate_quality_score(analysis)
                    })
                
                return {
                    'status': 'success',
                    'file': file_path,
                    'entities_analyzed': len(entities),
                    'results': results
                }
                
            except Exception as e:
                logger.error(f"Error parsing {file_path}: {e}")
                return {
                    'status': 'error',
                    'file': file_path,
                    'error': str(e)
                }
        
        @self.user_proxy.register_for_execution()
        @self.assistant.register_for_llm(description="Search for similar code")
        def search_similar(query: str, search_type: str = 'semantic', limit: int = 10) -> List[Dict]:
            """Search for similar code"""
            
            with self.pg_store.conn.cursor() as cur:
                if search_type == 'semantic':
                    # Use doc embeddings enriched by Claude
                    embedding = self.pg_store.models['doc'].encode(query)
                    embedding_col = 'doc_embedding'
                else:
                    # Use code embeddings
                    embedding = self.pg_store.models['code'].encode(query)
                    embedding_col = 'code_embedding'
                
                cur.execute(f"""
                    SELECT id, name, qualified_name, file_path,
                           claude_analysis->>'purpose' as purpose,
                           1 - ({embedding_col} <=> %s::vector) as similarity
                    FROM code_entities
                    ORDER BY {embedding_col} <=> %s::vector
                    LIMIT %s
                """, (embedding, embedding, limit))
                
                results = []
                for row in cur.fetchall():
                    results.append({
                        'name': row[1],
                        'qualified_name': row[2],
                        'file': row[3],
                        'purpose': row[4],
                        'similarity': float(row[5])
                    })
                
                return results
        
        @self.user_proxy.register_for_execution()
        @self.assistant.register_for_llm(description="Analyze code quality and patterns")
        def analyze_codebase_quality() -> Dict[str, Any]:
            """Analyze overall codebase quality"""
            
            with self.neo4j_builder.driver.session() as session:
                # Get pattern distribution
                pattern_result = session.run("""
                    MATCH (e:CodeEntity)-[:IMPLEMENTS_PATTERN]->(p:DesignPattern)
                    RETURN p.name as pattern, COUNT(e) as count
                    ORDER BY count DESC
                """)
                
                patterns = {r['pattern']: r['count'] for r in pattern_result}
                
                # Get quality metrics
                quality_result = session.run("""
                    MATCH (e:CodeEntity)
                    WHERE e.quality_score IS NOT NULL
                    RETURN AVG(e.quality_score) as avg_quality,
                           MIN(e.quality_score) as min_quality,
                           MAX(e.quality_score) as max_quality,
                           COUNT(e) as total_entities
                """)
                
                quality = quality_result.single()
                
                # Get domain concepts
                concept_result = session.run("""
                    MATCH (e:CodeEntity)-[:IMPLEMENTS_CONCEPT]->(c:DomainConcept)
                    RETURN c.name as concept, COUNT(e) as count
                    ORDER BY count DESC
                    LIMIT 10
                """)
                
                concepts = {r['concept']: r['count'] for r in concept_result}
                
                return {
                    'design_patterns': patterns,
                    'quality_metrics': {
                        'average': float(quality['avg_quality'] or 0),
                        'minimum': float(quality['min_quality'] or 0),
                        'maximum': float(quality['max_quality'] or 0),
                        'total_analyzed': quality['total_entities']
                    },
                    'top_domain_concepts': concepts
                }
        
        @self.user_proxy.register_for_execution()
        @self.assistant.register_for_llm(description="Get improvement suggestions")
        def get_improvement_suggestions(entity_name: str) -> Dict[str, Any]:
            """Get Claude's improvement suggestions for an entity"""
            
            with self.pg_store.conn.cursor() as cur:
                cur.execute("""
                    SELECT claude_analysis
                    FROM code_entities
                    WHERE name = %s
                    LIMIT 1
                """, (entity_name,))
                
                row = cur.fetchone()
                if row and row[0]:
                    analysis = row[0]
                    return {
                        'entity': entity_name,
                        'purpose': analysis.get('purpose'),
                        'quality_insights': analysis.get('quality_insights'),
                        'improvement_suggestions': analysis.get('improvement_suggestions'),
                        'test_scenarios': analysis.get('test_scenarios')
                    }
                
                return {'error': f'Entity {entity_name} not found'}
    
    def _load_documentation(self, file_path: str) -> Dict[str, Dict]:
        """Load pre-generated documentation if available"""
        
        doc_file = self.doc_dir / f"{Path(file_path).stem}_doc.json"
        
        if doc_file.exists():
            with open(doc_file, 'r') as f:
                doc_data = json.load(f)
                return {
                    entity['name']: entity
                    for entity in doc_data.get('entities', [])
                }
        
        return {}
    
    def _calculate_quality_score(self, analysis: ClaudeAnalysis) -> float:
        """Calculate quality score from analysis"""
        
        score = 1.0
        insights = analysis.quality_insights
        
        if insights.get('maintainability') == 'poor':
            score -= 0.2
        if insights.get('testability') == 'poor':
            score -= 0.2
        if len(insights.get('security_concerns', [])) > 0:
            score -= 0.1 * min(3, len(insights['security_concerns']))
        
        score += 0.05 * min(4, len(analysis.design_patterns))
        
        return max(0.0, min(1.0, score))

# Example usage
if __name__ == "__main__":
    # Configuration
    config = {
        'anthropic_api_key': os.environ.get('ANTHROPIC_API_KEY'),
        'pgvector': {
            'host': 'localhost',
            'port': 5432,
            'database': 'codebase',
            'user': 'postgres',
            'password': 'password'
        },
        'neo4j': {
            'uri': 'bolt://localhost:7687',
            'user': 'neo4j',
            'password': 'password'
        },
        'documentation_dir': '/path/to/documentation'
    }
    
    # Create agent
    agent = ClaudeParsingAgent(config)
    
    # Parse and analyze a file
    agent.user_proxy.initiate_chat(
        agent.assistant,
        message="Parse and analyze the file /path/to/code/auth_service.py"
    )
    
    # Search for similar code
    agent.user_proxy.initiate_chat(
        agent.assistant,
        message="Find code similar to 'user authentication with JWT tokens'"
    )
    
    # Analyze codebase quality
    agent.user_proxy.initiate_chat(
        agent.assistant,
        message="Analyze the overall codebase quality and patterns"
    )
    
    # Get improvement suggestions
    agent.user_proxy.initiate_chat(
        agent.assistant,
        message="What improvements does Claude suggest for the authenticate_user function?"
    )
