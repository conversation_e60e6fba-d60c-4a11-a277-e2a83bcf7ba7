# ============================================================================
# RadAgents Environment Variables Template
# ============================================================================
# Copy this file to .env and fill in your actual API keys and configuration

# ============================================================================
# API KEYS CONFIGURATION
# ============================================================================

# Claude API Key for AutoGen agents
# Get from: https://console.anthropic.com/
API_KEY=your_claude_api_key_here

# Google API Key for API integration agent
# Get from: https://console.developers.google.com/
GOOGLE_API_KEY=your_google_api_key_here

# Gemini API Key for multi-model validation
# Get from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Azure OpenAI API Key for API reviewer agent
# Get from: https://portal.azure.com/
AZURE_OPENAI_API_KEY=your_azure_openai_key_here

# ============================================================================
# GITHUB CONFIGURATION
# ============================================================================

# GitHub username and personal access token
# Get token from: https://github.com/settings/tokens
GITHUB_USERNAME=your_github_username
GITHUB_TOKEN=your_github_token_here

# ============================================================================
# JIRA API CONFIGURATION
# ============================================================================

# Jira instance configuration
# Base URL should be your Jira domain (e.g., https://yourcompany.atlassian.net)
JIRA_BASE_URL=https://yourcompany.atlassian.net
JIRA_API_KEY=your_jira_api_key_here
JIRA_EMAIL=<EMAIL>
JIRA_PROJECT_KEY=YOUR_PROJECT_KEY
