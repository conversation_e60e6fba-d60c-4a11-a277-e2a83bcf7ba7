from autogen import ConversableAgent

class BaseAgent(ConversableAgent):
    """Base class for custom AutoGen agents."""
    
    def __init__(self, name, claude_client, system_message, api_key=None,google_api_key=None):
        super().__init__(name=name, human_input_mode="NEVER")
        self.claude_client = claude_client
        self._system_message = system_message
        self.api_key = api_key  # Add support for API key
        

    def generate_reply(self, messages=None, **kwargs):
        """Generate reply using <PERSON>"""
        if not messages:
            return "No message to respond to."
        try:
            prompt = f"{self._system_message}\n\nTask: {messages[-1]['content']}"
            response = self.claude_client.messages.create(
                 model="claude-sonnet-4-20250514",
                max_tokens=2000,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        except Exception as e:
            return f"Error: {e}"


