# Config package initialization
# This file makes the config directory a proper Python package

# Import all configuration variables to make them available at package level
from .config import (
    API_KEY,
    google_api_key,
    GOOGLE_API_KEY,
    GEMINI_API_KEY,
    AZURE_OPENAI_API_KEY,
    OUTPUT_PATH,
    GITHUB_USERNAME,
    GITHUB_TOKEN,
    JIRA_API_CONFIG,
    get_required_env_var
)

# Make all config variables available when importing from config
__all__ = [
    'API_KEY',
    'google_api_key',
    'GOOGLE_API_KEY', 
    'GEMINI_API_KEY',
    'AZURE_OPENAI_API_KEY',
    'OUTPUT_PATH',
    'GITHUB_USERNAME',
    'GITHUB_TOKEN',
    'JIRA_API_CONFIG',
    'get_required_env_var'
]
