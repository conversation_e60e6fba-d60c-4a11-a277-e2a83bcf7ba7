# RadAgents - Intelligent Agent Automation Platform

## 🚀 Quick Start

The project is now **fully configured and ready to run**! 

### Option 1: Use the Startup Script (Recommended)
```bash
./run_project.sh
```

### Option 2: Manual Setup
```bash
# Activate virtual environment
source autogen_env/bin/activate

# Run the main workflow
python main.py
```

## 🎯 What This Project Does

RadAgents is an intelligent automation platform that uses AI agents to:

- **Generate Code**: Create scripts from Method of Procedures (MOPs)
- **Review Code**: Automated code review and quality assessment
- **Improve Code**: Enhance existing code with best practices
- **API Integration**: Connect with external APIs and services
- **Documentation**: Generate comprehensive documentation
- **PRD Generation**: Create Product Requirements Documents
- **QA Testing**: Automated testing and compliance checking
- **Jira Integration**: Create and manage Jira issues
- **GitHub Integration**: Push code to GitHub repositories

## 🛠️ Available Interfaces

### 1. Command Line Interface (Default)
```bash
python main.py
```
- Interactive agent selection
- Step-by-step workflow execution
- Real-time progress updates

### 2. Web UI Applications
```bash
# Full-featured UI
streamlit run ui_app.py

# Simple UI
streamlit run simple_ui.py

# Dynamic UI
streamlit run dynamic_ui.py
```

## 📋 Agent Options

When running the project, you can select from these agents:

1. **CodeGenerator** - Generates code from MOPs
2. **Reviewer** - Reviews and analyzes code quality
3. **Improver** - Enhances code with improvements
4. **APIIntegration** - Handles API integrations
5. **ReverseEngineering** - Creates documentation from code
6. **PRD** - Generates Product Requirements Documents
7. **QA** - Performs testing and quality assurance

## 📁 Project Structure

```
RadAgents/
├── main.py                 # Main workflow script
├── config.py              # Configuration (API keys, settings)
├── run_project.sh         # Startup script
├── autogen_env/           # Python virtual environment
├── local_agents/          # Agent implementations
├── workflows/             # Workflow definitions
├── output/               # Generated files and reports
├── test_data/            # Sample test data
└── generated_scripts/    # Generated automation scripts
```

## 🔧 Configuration

### Environment Variables (Optional)
Create a `.env` file to override default API keys:

```bash
# API Keys
API_KEY=your_claude_api_key
GOOGLE_API_KEY=your_google_api_key
AZURE_OPENAI_API_KEY=your_azure_key

# GitHub
GITHUB_USERNAME=your_username
GITHUB_TOKEN=your_github_token

# Jira
JIRA_BASE_URL=https://your-domain.atlassian.net
JIRA_API_KEY=your_jira_key
JIRA_EMAIL=<EMAIL>
JIRA_PROJECT_KEY=YOUR_PROJECT
```

**Note**: The project includes fallback API keys, so it works out of the box without additional configuration.

## 🎮 Example Usage

### 1. Generate Code from MOP
```bash
./run_project.sh
# Select option 1 (main workflow)
# Choose MOP: 1 (Cisco Audit) or 2 (PRD)
# Select agents: 1 (CodeGenerator)
```

### 2. Full Workflow (Code + Review + Improve)
```bash
./run_project.sh
# Select agents: 1,2,3 (CodeGenerator, Reviewer, Improver)
```

### 3. PRD with Jira Integration
```bash
./run_project.sh
# Select agents: 6 (PRD)
# Automatically creates Jira epics and user stories
```

## 📊 Output Files

The project generates various outputs in the `output/` directory:

- `generated_code.py` - AI-generated code
- `review_feedback.txt` - Code review results
- `improved_code.py` - Enhanced code version
- `integration_result.txt` - API integration results
- `documented_script.py` - Documented code
- `prd.md` - Product Requirements Document
- `qa_results.txt` - Quality assurance reports
- `test_generated.py` - Generated test cases

## 🔗 Integrations

### GitHub
- Automatically creates repositories
- Pushes generated code to branches
- Manages version control

### Jira
- Creates epics, user stories, and tasks
- Tracks project progress
- Manages bug reports

### APIs
- Google API integration
- Custom API connections
- External service integration

## ✅ System Requirements

- **Python**: 3.11+ (already configured)
- **Virtual Environment**: `autogen_env` (already set up)
- **Dependencies**: All installed and ready
- **Platform**: macOS, Linux, Windows

## 🛡️ Security

- API keys can be configured via environment variables
- Secure token handling for GitHub and Jira
- Encrypted communication with external services

## 📝 Logs and Monitoring

- Comprehensive logging in `test_logs/`
- Screenshot capture for UI testing
- Detailed audit trails
- Performance monitoring

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: All dependencies are pre-installed
2. **API Key Issues**: Fallback keys are provided
3. **Permission Errors**: Use `chmod +x run_project.sh`
4. **Virtual Environment**: Already activated in startup script

### Getting Help

1. Check the logs in `test_logs/`
2. Review generated outputs in `output/`
3. Verify configuration in `config.py`

## 🎉 Success!

Your RadAgents project is **fully operational**. The test run successfully:

- ✅ Generated Cisco XR audit code
- ✅ Created GitHub repository
- ✅ Pushed code to version control
- ✅ All agents working properly

**Ready to automate your workflows with AI agents!** 🤖
