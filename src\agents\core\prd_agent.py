# from .base_agent import BaseAgent

# class PRDAgent(BaseAgent):
#     """Agent for generating Product Requirements Documents (PRD)."""

#     def __init__(self, claude_client, name, api_key):
#         super().__init__(
#             name=name,
#             claude_client=claude_client,
#             system_message="""
# You are a technical writer specializing in PRD creation. Generate a PRD document based on the provided MOP content, 
# including user stories, acceptance criteria, and technical specifications.
# """,
#             api_key=api_key
#         )

#     def generate_prd(self, mop_content, output_file):
#         """Generate PRD document and save it to the specified file."""
#         prd_content = self.generate_reply([{"content": mop_content}])
#         with open(output_file, "w", encoding="utf-8") as file:
#             file.write(prd_content)
#         return f"PRD successfully generated and saved to {output_file}."


from .base_agent import BaseAgent

class PRDAgent(BaseAgent):
    """
    Agent for generating Product Requirements Documents (PRD).
    """

    def __init__(self, claude_client, name, api_key):
        """
        Initialize the PRD Agent with required configurations.

        Args:
            claude_client: Instance of the Claude client for interacting with the AI.
            name: Name of the agent.
            api_key: API key for authentication.
        """
        super().__init__(
            name=name,
            claude_client=claude_client,
            system_message="""
You are a technical writer specializing in PRD creation. Generate a PRD document based on the provided MOP content. 
The document should include:
- Title
- Epics
- User stories
- Acceptance criteria
- Technical specifications
- Integration with Jira if mentioned.
""",
            api_key=api_key
        )

    def generate_prd(self, mop_content, output_file):
        """
        Generate a PRD document from the provided MOP content and save it.

        Args:
            mop_content: The MOP content to be transformed into a PRD.
            output_file: The path where the generated PRD file will be saved.

        Returns:
            A string indicating the success and location of the saved PRD file.
        """
        try:
            print("🔄 Generating PRD content...")
            prd_content = self.generate_reply([{"content": mop_content}])

            print("💾 Saving PRD content...")
            with open(output_file, "w", encoding="utf-8") as file:
                file.write(prd_content)

            return f"✅ PRD successfully generated and saved to {output_file}."
        except Exception as e:
            return f"❌ Failed to generate PRD: {str(e)}"
