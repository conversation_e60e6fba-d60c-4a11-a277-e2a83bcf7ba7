import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import axios from 'axios';
import UserList from './components/UserList';
import UserForm from './components/UserForm';
import './App.css';

function App() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await axios.get('/api/users');
      setUsers(response.data);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const addUser = async (userData) => {
    try {
      const response = await axios.post('/api/users', userData);
      setUsers([...users, response.data]);
    } catch (error) {
      console.error('Error adding user:', error);
    }
  };

  return (
    <Router>
      <div className="App">
        <header className="App-header">
          <h1>User Management Dashboard</h1>
        </header>
        <main>
          <Routes>
            <Route 
              path="/" 
              element={<UserList users={users} loading={loading} />} 
            />
            <Route 
              path="/add-user" 
              element={<UserForm onSubmit={addUser} />} 
            />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
