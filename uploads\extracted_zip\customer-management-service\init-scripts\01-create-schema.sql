-- VRepair Customer Management Database Schema
-- Oracle Database Initialization Script

-- Create CUSTOMERS table (matching the JPA entity structure)
CREATE TABLE CUSTOMERS (
    CUSTOMER_CODE VARCHAR2(20) PRIMARY KEY,
    BILLING_TELEPHONE_NUM VARCHAR2(15) NOT NULL,
    SERVICE_ADDRESS VARCHAR2(500),
    CUSTOMER_NAME VARCHAR2(100),
    CUSTOMER_TYPE VARCHAR2(20),
    CUSTOMER_STATUS VARCHAR2(20) DEFAULT 'ACTIVE',
    ACCOUNT_NUMBER VARCHAR2(50),
    SERVICE_CLASS_CODE VARCHAR2(10),
    CONTACT_PHONE VARCHAR2(15),
    EMAIL_ADDRESS VARCHAR2(100),
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    MODIFIED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CREATED_BY VARCHAR2(50) DEFAULT 'SYSTEM',
    M<PERSON><PERSON>IED_BY VARCHAR2(50) DEFAULT 'SYSTEM'
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX IDX_CUSTOMERS_BTN ON CUSTOMERS (BILLING_TELEPHONE_NUM);
CREATE INDEX IDX_CUSTOMERS_TYPE ON CUSTOMERS (CUSTOMER_TYPE);
CREATE INDEX IDX_CUSTOMERS_STATUS ON CUSTOMERS (CUSTOMER_STATUS);
CREATE INDEX IDX_CUSTOMERS_ACCOUNT ON CUSTOMERS (ACCOUNT_NUMBER);

-- Insert sample test data
INSERT INTO CUSTOMERS (
    CUSTOMER_CODE, BILLING_TELEPHONE_NUM, SERVICE_ADDRESS, 
    CUSTOMER_NAME, CUSTOMER_TYPE, CUSTOMER_STATUS,
    ACCOUNT_NUMBER, SERVICE_CLASS_CODE, CONTACT_PHONE, EMAIL_ADDRESS
) VALUES (
    'CUST001', '**********', '123 Main St, New York, NY 10001',
    'John Doe', 'RESIDENTIAL', 'ACTIVE',
    'ACC123456', 'RES', '**********', '<EMAIL>'
);

INSERT INTO CUSTOMERS (
    CUSTOMER_CODE, BILLING_TELEPHONE_NUM, SERVICE_ADDRESS, 
    CUSTOMER_NAME, CUSTOMER_TYPE, CUSTOMER_STATUS,
    ACCOUNT_NUMBER, SERVICE_CLASS_CODE, CONTACT_PHONE, EMAIL_ADDRESS
) VALUES (
    'CUST002', '**********', '456 Business Ave, New York, NY 10002',
    'ABC Corporation', 'BUSINESS', 'ACTIVE',
    'ACC789012', 'BUS', '**********', '<EMAIL>'
);

INSERT INTO CUSTOMERS (
    CUSTOMER_CODE, BILLING_TELEPHONE_NUM, SERVICE_ADDRESS, 
    CUSTOMER_NAME, CUSTOMER_TYPE, CUSTOMER_STATUS,
    ACCOUNT_NUMBER, SERVICE_CLASS_CODE, CONTACT_PHONE, EMAIL_ADDRESS
) VALUES (
    'CUST003', '**********', '789 Enterprise Blvd, New York, NY 10003',
    'XYZ Enterprises', 'ENTERPRISE', 'ACTIVE',
    'ACC345678', 'ENT', '**********', '<EMAIL>'
);

-- Commit the changes
COMMIT;

-- Display confirmation
SELECT 'Database schema created successfully!' as STATUS FROM DUAL;
SELECT 'Sample data inserted: ' || COUNT(*) || ' customers' as DATA_STATUS FROM CUSTOMERS;
