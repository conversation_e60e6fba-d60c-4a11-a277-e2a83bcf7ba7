server:
  port: 8080
  servlet:
    context-path: /customer-service

spring:
  application:
    name: customer-management-service
  
  profiles:
    active: dev
  
  datasource:
    url: jdbc:oracle:thin:@${DB_HOST:localhost}:${DB_PORT:1521}:${DB_NAME:XE}
    username: ${DB_USERNAME:vrepair_user}
    password: ${DB_PASSWORD:vrepair_pass}
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      pool-name: CustomerServicePool
  
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        dialect: org.hibernate.dialect.OracleDialect
        format_sql: false
        show_sql: false
        jdbc:
          batch_size: 25
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
  
  cache:
    type: redis
    redis:
      time-to-live: 300000 # 5 minutes default
      cache-null-values: false
  
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${JWT_JWK_SET_URI:https://auth.verizon.com/.well-known/jwks.json}

# Application-specific configuration
app:
  encryption:
    key: ${ENCRYPTION_KEY:dGVzdC1lbmNyeXB0aW9uLWtleS0xMjM0NTY3ODkwMTIzNDU2}
  
  security:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,https://vrepair.verizon.com}
  
  external-services:
    etms:
      base-url: ${ETMS_BASE_URL:http://etms-service:8080}
      timeout: 5000
      retry-attempts: 3
    
    baais:
      base-url: ${BAAIS_BASE_URL:http://baais-service:8080}
      timeout: 3000
      retry-attempts: 2
    
    vi:
      base-url: ${VI_BASE_URL:http://vi-service:8080}
      timeout: 2000
      retry-attempts: 1

# Management and monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    redis:
      enabled: true
    db:
      enabled: true

# Logging configuration
logging:
  level:
    com.verizon.vrepair.customer: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/customer-management-service.log
    max-size: 10MB
    max-history: 30

# OpenAPI/Swagger configuration
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    try-it-out-enabled: true
    filter: true
    display-request-duration: true
    display-operation-id: true
    default-models-expand-depth: 2
    default-model-expand-depth: 2
    doc-expansion: none
    tags-sorter: alpha
    operations-sorter: alpha
  show-actuator: false
  group-configs:
    - group: 'customer-api'
      display-name: 'Customer Management API'
      paths-to-match: '/api/v1/customers/**'
    - group: 'actuator'
      display-name: 'Actuator Endpoints'  
      paths-to-match: '/actuator/**'
  info:
    title: Customer Management Service API
    description: VRepair Customer Management Microservice - Modernized from legacy C++ system
    version: 1.0.0
    contact:
      name: VRepair Development Team
      email: <EMAIL>
