# Database Configuration Guide
## VRepair Customer Management Service

### Overview
As per the PRD requirements, the Customer Management Service supports both Oracle (production) and H2 (development) databases, maintaining the **"as is"** migration approach with technology modernization only.

## 📋 Migration Summary

### Legacy System (C++)
- **Database**: Oracle 11g+ with 50+ stored procedures
- **Access Method**: Oracle Pro*C embedded SQL
- **Connection Management**: Direct database connections
- **Technology**: C++ with manual memory management

### Modern System (Java)
- **Database**: Oracle (maintained) + H2 (development)
- **Access Method**: Spring Data JPA/Hibernate
- **Connection Management**: HikariCP connection pooling
- **Technology**: Java 17+ with Spring Boot

## 🎯 Database Configurations

### 1. Oracle Database (Production)

**Profile**: `oracle`, `prod`, `production`

**Configuration File**: `application-oracle.yml`

**Connection Details**:
```yaml
datasource:
  url: jdbc:oracle:thin:@${DB_HOST:localhost}:${DB_PORT:1521}:${DB_SERVICE_NAME:XEPDB1}
  username: ${DB_USERNAME:vrepair_user}
  password: ${DB_PASSWORD:vrepair_pass}
  driver-class-name: oracle.jdbc.OracleDriver
```

**Features**:
- ✅ HikariCP connection pooling (50 max, 10 min connections)
- ✅ Oracle 12c+ dialect support
- ✅ Connection leak detection
- ✅ Batch processing optimization
- ✅ Schema validation (ddl-auto: validate)

**How to Run**:
```bash
# With Oracle profile
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=oracle

# With environment variables
export DB_HOST=oracle-server
export DB_USERNAME=vrepair_user
export DB_PASSWORD=your_password
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=oracle
```

### 2. H2 Database (Development)

**Profile**: `h2`, `dev`, `simple`, `test`

**Configuration File**: `application-simple.yml`

**Connection Details**:
```yaml
datasource:
  url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
  username: sa
  password: (empty)
  driver-class-name: org.h2.Driver
```

**Features**:
- ✅ In-memory database (fast startup)
- ✅ H2 Console available at `/h2-console`
- ✅ Auto-schema creation (ddl-auto: create-drop)
- ✅ Perfect for development and testing
- ✅ No external dependencies

**How to Run**:
```bash
# With H2/simple profile (current setup)
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=simple
```

## 🌐 Accessing Databases

### H2 Console Access
```
URL: http://localhost:8080/customer-service/h2-console

Login Credentials:
- JDBC URL: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
- User Name: sa
- Password: (leave blank)
```

### Oracle Database
```
Standard Oracle client tools:
- SQL*Plus
- Oracle SQL Developer
- DBeaver
- IntelliJ Database Tools
```

## 📊 Database Schema Compatibility

### Customer Table Structure
```sql
-- Oracle Schema (Production)
CREATE TABLE CUSTOMERS (
    CUSTOMER_CODE VARCHAR2(20) PRIMARY KEY,
    BILLING_TELEPHONE_NUM VARCHAR2(15) NOT NULL,
    SERVICE_ADDRESS VARCHAR2(500),
    CUSTOMER_NAME VARCHAR2(100),
    CUSTOMER_TYPE VARCHAR2(20),
    CUSTOMER_STATUS VARCHAR2(20),
    ACCOUNT_NUMBER VARCHAR2(50),
    SERVICE_CLASS_CODE VARCHAR2(10),
    CONTACT_PHONE VARCHAR2(15),
    EMAIL_ADDRESS VARCHAR2(100),
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    MODIFIED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- H2 Schema (Development) - Auto-generated by Hibernate
-- Same structure, compatible field types
```

## 🔧 Configuration Profiles

### Available Profiles

| Profile | Database | Purpose | Configuration File |
|---------|----------|---------|-------------------|
| `oracle` | Oracle | Production | `application-oracle.yml` |
| `simple` | H2 | Development | `application-simple.yml` |
| `dev` | H2 | Development | `application-simple.yml` |
| `test` | H2 | Testing | `application-simple.yml` |

### Profile Selection
```bash
# Oracle (Production)
--spring.profiles.active=oracle

# H2 (Development) - Current
--spring.profiles.active=simple

# Multiple profiles
--spring.profiles.active=oracle,prod
```

## 🚀 Deployment Examples

### Development Environment
```bash
# Start with H2 (current setup)
cd /Users/<USER>/Downloads/Vrepair_Arch/customer-management-service
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=simple
```

### Production Environment
```bash
# Start with Oracle
export DB_HOST=prod-oracle-server.verizon.com
export DB_USERNAME=vrepair_prod_user
export DB_PASSWORD=secure_password
export REDIS_HOST=redis-cluster.verizon.com
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=oracle
```

## 🔍 Verification Commands

### Health Check
```bash
curl http://localhost:8080/customer-service/actuator/health
```

### Database Connection Test
```bash
# Check database component in health endpoint
curl http://localhost:8080/customer-service/actuator/health | jq '.components.db'
```

### API Test
```bash
# Test customer endpoint
curl "http://localhost:8080/customer-service/api/v1/customers?page=0&size=10"
```

---

## ✅ PRD Compliance Summary

**Migration Approach**: ✅ **"As Is" Functionality**
- All existing Oracle database schema preserved
- All business logic maintained
- All data relationships intact
- Technology stack modernized (C++ → Java)

**Database Requirements**: ✅ **Fully Compliant**
- **PRD Line 74**: "Oracle (maintained) with JPA/Hibernate" ✅
- **PRD Line 124**: "Maintain existing Oracle database and schema" ✅
- **PRD Line 131**: "Database technology changes (Oracle remains)" ✅

**Performance Improvements**: ✅ **Enhanced**
- Connection pooling (replacing direct connections)
- Query optimization with JPA/Hibernate
- Caching layer for frequently accessed data
- Batch processing for bulk operations
