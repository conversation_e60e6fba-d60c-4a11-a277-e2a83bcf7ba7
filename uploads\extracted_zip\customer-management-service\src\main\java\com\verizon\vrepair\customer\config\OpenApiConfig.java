package com.verizon.vrepair.customer.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * OpenAPI/Swagger configuration for Customer Management Service.
 * Provides comprehensive API documentation with security schemes and examples.
 */
@Configuration
public class OpenApiConfig {
    
    @Value("${spring.application.name:customer-management-service}")
    private String applicationName;
    
    @Value("${server.servlet.context-path:/customer-service}")
    private String contextPath;
    
    @Bean
    public OpenAPI customerServiceOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(serverList())
                .tags(tagList())
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("bearerAuth", securityScheme())
                );
    }
    
    private Info apiInfo() {
        return new Info()
                .title("VRepair Customer Management Service API")
                .description("""
                        ## Customer Management Service API
                        
                        This API provides comprehensive customer management functionality for the VRepair system.
                        It's a modernized Java microservice that replaces the legacy C++ customer management system
                        while maintaining 100% functional parity.
                        
                        ### Key Features
                        - **Customer CRUD Operations**: Create, read, update, and manage customer records
                        - **Advanced Search**: Multi-criteria customer search with pagination
                        - **Status Management**: Customer activation, deactivation, and lifecycle management
                        - **Business Validation**: Legacy business rules and validation preserved
                        - **Security**: OAuth 2.0/JWT authentication with role-based access control
                        - **Audit Logging**: Comprehensive audit trail for all operations
                        - **Performance**: Sub-second response times with Redis caching
                        
                        ### Legacy System Migration
                        This service modernizes the following legacy C++ components:
                        - Customer data structures → JPA entities
                        - Pro*C database access → Spring Data JPA
                        - C-style validation → Modern Java validation
                        - Legacy error codes → RESTful HTTP status codes
                        
                        ### Authentication
                        All API endpoints require OAuth 2.0 JWT authentication. Include the Bearer token
                        in the Authorization header for all requests.
                        
                        ### Rate Limiting
                        - **Authenticated Users**: 100 requests/minute
                        - **Admin Users**: 200 requests/minute
                        - **Anonymous Requests**: 20 requests/minute (health checks only)
                        
                        ### Error Handling
                        All errors return consistent JSON responses with:
                        - Error code for programmatic handling
                        - Human-readable message
                        - Correlation ID for support tracking
                        - Timestamp and request path
                        
                        ### Performance Targets
                        - **Response Time**: <1 second (95th percentile)
                        - **Throughput**: 100+ operations/second
                        - **Availability**: 99.9% uptime
                        - **Cache Hit Ratio**: >80%
                        """)
                .version("1.0.0")
                .contact(new Contact()
                        .name("VRepair Development Team")
                        .email("<EMAIL>")
                        .url("https://vrepair.verizon.com"))
                .license(new License()
                        .name("Verizon Internal Use")
                        .url("https://verizon.com/licenses/internal"));
    }
    
    private List<Server> serverList() {
        return Arrays.asList(
                new Server()
                        .url("https://api.vrepair.verizon.com" + contextPath)
                        .description("Production Server"),
                new Server()
                        .url("https://staging-api.vrepair.verizon.com" + contextPath)
                        .description("Staging Server"),
                new Server()
                        .url("http://localhost:8080" + contextPath)
                        .description("Local Development Server")
        );
    }
    
    private List<Tag> tagList() {
        return Arrays.asList(
                new Tag()
                        .name("Customer Management")
                        .description("Core customer CRUD operations and lifecycle management"),
                new Tag()
                        .name("Customer Search")
                        .description("Advanced customer search and filtering capabilities"),
                new Tag()
                        .name("Customer Status")
                        .description("Customer activation, deactivation, and status transitions"),
                new Tag()
                        .name("Customer Statistics")
                        .description("Customer analytics and reporting (Admin only)"),
                new Tag()
                        .name("Customer Validation")
                        .description("Customer eligibility and business rule validation")
        );
    }
    
    private SecurityScheme securityScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .description("""
                        JWT Bearer token authentication. 
                        
                        **How to obtain a token:**
                        1. Authenticate with the enterprise OAuth 2.0 provider
                        2. Include the JWT token in the Authorization header
                        3. Format: `Authorization: Bearer <your-jwt-token>`
                        
                        **Token Claims:**
                        - `sub`: User identifier
                        - `roles`: Array of user roles (USER, TECHNICIAN, ADMIN, SUPERVISOR)
                        - `exp`: Token expiration timestamp
                        - `iss`: Token issuer
                        
                        **Role Permissions:**
                        - **USER**: Basic customer operations (read, create, update)
                        - **TECHNICIAN**: Advanced customer operations + search
                        - **ADMIN**: All operations + statistics + bulk operations
                        - **SUPERVISOR**: Customer activation/deactivation authority
                        """);
    }
}


