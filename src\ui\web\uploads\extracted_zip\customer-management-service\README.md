# Customer Management Service

A modernized Java microservice for customer management, replacing the legacy VRepair C++ system while maintaining 100% functional parity.

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Maven 3.6+
- Oracle Database (for production) or H2 (for testing)
- Redis (optional, for caching)

### Run the Service with Documentation

```bash
# Start the service with interactive documentation
./start-with-docs.sh
```

### Manual Start

```bash
# Build and run
mvn clean spring-boot:run

# Or build and run JAR
mvn clean package
java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar
```

## 📖 API Documentation

Once the service is running, access the interactive documentation:

### 🌐 Interactive Documentation
- **Swagger UI**: http://localhost:8080/customer-service/swagger-ui.html
- **OpenAPI Spec**: http://localhost:8080/customer-service/v3/api-docs
- **Health Check**: http://localhost:8080/customer-service/actuator/health

### 📚 Documentation Files
- **[Complete API Guide](./API_Documentation.md)** - Comprehensive API documentation
- **[Development Summary](../Customer_Service_MVP_Development_Summary.md)** - Project overview and architecture

## 🔐 Authentication

All API endpoints require OAuth 2.0 JWT authentication:

```bash
# Include Bearer token in requests
curl -H "Authorization: Bearer <your-jwt-token>" \
     http://localhost:8080/customer-service/api/v1/customers
```

### User Roles
- **USER**: Basic customer operations
- **TECHNICIAN**: Advanced operations + search
- **ADMIN**: All operations + statistics
- **SUPERVISOR**: Activation/deactivation authority

## 📋 Key API Endpoints

| Method | Endpoint | Description | Required Role |
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/customers` | Create customer | USER+ |
| `GET` | `/api/v1/customers/{id}` | Get customer | USER+ |
| `PUT` | `/api/v1/customers/{id}` | Update customer | USER+ |
| `GET` | `/api/v1/customers/search` | Search customers | USER+ |
| `PUT` | `/api/v1/customers/{id}/activate` | Activate customer | SUPERVISOR+ |
| `GET` | `/api/v1/customers/statistics` | Get statistics | ADMIN |

## 🧪 Testing

```bash
# Run all tests
mvn test

# Run specific test categories
mvn test -Dtest=CustomerServiceTest
mvn test -Dtest=CustomerServiceIntegrationTest
mvn test -Dtest=LegacyComparisonTest
```

## 📊 Performance Targets

- **Response Time**: <1 second (95th percentile)
- **Throughput**: 100+ operations/second
- **Cache Hit Ratio**: >80%
- **Availability**: 99.9% uptime

## 🏗️ Architecture

### Technology Stack
- **Java 17** - Modern LTS version
- **Spring Boot 3.2** - Application framework
- **Spring Data JPA** - Database access
- **Oracle Database** - Primary data store
- **Redis** - Caching layer
- **OAuth 2.0/JWT** - Authentication
- **Swagger/OpenAPI** - API documentation

### Legacy Migration
This service modernizes legacy C++ components:
- Customer data structures → JPA entities
- Pro*C database access → Spring Data JPA
- C-style validation → Modern Java validation
- Legacy error codes → RESTful HTTP responses

## 🔧 Configuration

### Application Profiles
- **default/dev**: Development configuration
- **test**: Testing configuration with H2 database
- **prod**: Production configuration

### Environment Variables
```bash
# Database
DB_HOST=localhost
DB_PORT=1521
DB_NAME=XE
DB_USERNAME=vrepair_user
DB_PASSWORD=vrepair_pass

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Security
JWT_JWK_SET_URI=https://auth.verizon.com/.well-known/jwks.json
ENCRYPTION_KEY=<base64-encoded-key>
```

## 📈 Monitoring

### Health Checks
- **Health**: `/actuator/health`
- **Metrics**: `/actuator/metrics`
- **Prometheus**: `/actuator/prometheus`

### Performance Metrics
- Customer operation counters
- Response time histograms
- Cache hit ratios
- Database connection pool metrics
- JVM memory and GC metrics

## 🚨 Error Handling

All errors return consistent JSON responses:
```json
{
  "errorCode": "CUSTOMER_NOT_FOUND",
  "message": "Customer not found with code: CUST001",
  "correlationId": "123e4567-e89b-12d3-a456-426614174000",
  "timestamp": "2024-01-15T10:30:00",
  "path": "/api/v1/customers/CUST001"
}
```

## 🔒 Security Features

- **OAuth 2.0/JWT Authentication**
- **Role-based Authorization** 
- **Rate Limiting** (100 req/min per user)
- **Data Encryption** (AES-256-GCM for PII)
- **Audit Logging** (comprehensive activity trail)
- **Input Validation** (prevents injection attacks)
- **CORS Protection**
- **Security Headers**

## 📝 Development

### Project Structure
```
src/
├── main/java/com/verizon/vrepair/customer/
│   ├── controller/     # REST controllers
│   ├── service/        # Business logic
│   ├── repository/     # Data access
│   ├── model/          # JPA entities
│   ├── dto/            # API data transfer objects
│   ├── mapper/         # Entity-DTO mapping
│   ├── exception/      # Custom exceptions
│   ├── validation/     # Data validation
│   └── config/         # Configuration classes
├── test/java/          # Unit and integration tests
└── resources/          # Configuration files
```

### Code Quality
- **Test Coverage**: >85% across all layers
- **Code Style**: Google Java Style Guide
- **Static Analysis**: SpotBugs, PMD, Checkstyle
- **Security Scanning**: OWASP dependency check

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: https://vrepair.verizon.com/docs
- **Status Page**: https://status.vrepair.verizon.com

## 📄 License

Verizon Internal Use Only

---

## 🎯 Next Steps

1. **Start the Service**: Run `./start-with-docs.sh`
2. **Explore APIs**: Visit http://localhost:8080/customer-service/swagger-ui.html
3. **Run Tests**: Execute `mvn test`
4. **Review Documentation**: Read `API_Documentation.md`
5. **Deploy**: Follow deployment guide in project summary
