package com.verizon.vrepair.customer.exception;

/**
 * Exception thrown when customer data validation fails.
 * Replaces legacy C++ error code CUSTOMER_VALIDATION_ERROR.
 */
public class CustomerValidationException extends RuntimeException {
    
    private final String validationField;
    
    public CustomerValidationException(String message) {
        super(message);
        this.validationField = null;
    }
    
    public CustomerValidationException(String message, String validationField) {
        super(message);
        this.validationField = validationField;
    }
    
    public CustomerValidationException(String message, Throwable cause) {
        super(message, cause);
        this.validationField = null;
    }
    
    public String getValidationField() {
        return validationField;
    }
}
