#!/bin/bash

# VRepair Customer Management Service - H2 Database Startup Script (Quick Test)

echo "🚀 Starting VRepair Customer Management Service with H2 Database"
echo "=================================================="

# Build the application if needed
if [ ! -f "target/customer-management-service-1.0.0-SNAPSHOT.jar" ]; then
    echo "🔨 Building application..."
    mvn clean package -DskipTests
fi

# Start the Spring Boot application with H2
echo "🌟 Starting Customer Management Service with H2..."
echo "📊 Application will be available at: http://localhost:8080/customer-service"
echo "📖 API Documentation: http://localhost:8080/customer-service/swagger-ui.html"
echo "💾 H2 Database Console: http://localhost:8080/customer-service/h2-console"
echo ""
echo "🔑 H2 Console Login:"
echo "   JDBC URL: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE"
echo "   Username: sa"
echo "   Password: (leave blank)"
echo ""
echo "🛑 Press Ctrl+C to stop the application"
echo "=================================================="

java -jar target/customer-management-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=simple
