#!/usr/bin/env python3
"""
Test script to verify multi-model validation works correctly.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print(f"Project root: {project_root}")

try:
    from src.ui.web.dynamic_ui import call_multi_model_api
    print("SUCCESS: Multi-model API import successful!")
    
    # Test a simple prompt
    test_prompt = "Write a simple Python function that adds two numbers."
    
    print("Testing single model (<PERSON>)...")
    success, result = call_multi_model_api(test_prompt, "test", use_multi_model=False)
    if success:
        print(f"SUCCESS: Single model test passed! Result length: {len(result)} chars")
    else:
        print(f"ERROR: Single model test failed: {result}")
    
    print("\nTesting multi-model validation (Claude Sonnet + Gemini)...")
    success, result = call_multi_model_api(test_prompt, "test", use_multi_model=True)
    if success:
        print(f"SUCCESS: Multi-model test passed! Result length: {len(result)} chars")
        if "MULTI-MODEL VALIDATION REPORT" in result:
            print("SUCCESS: Multi-model report format detected!")
        else:
            print("INFO: Single model fallback used")
    else:
        print(f"ERROR: Multi-model test failed: {result}")
        
except Exception as e:
    print(f"ERROR: Test failed: {e}")
    import traceback
    traceback.print_exc()