package com.verizon.vrepair.customer.service.impl;

import com.verizon.vrepair.customer.model.Customer;
import com.verizon.vrepair.customer.model.CustomerStatus;
import com.verizon.vrepair.customer.model.CustomerType;
import com.verizon.vrepair.customer.repository.CustomerRepository;
import com.verizon.vrepair.customer.service.CustomerService;
import com.verizon.vrepair.customer.validation.CustomerValidator;
import com.verizon.vrepair.customer.exception.CustomerNotFoundException;
import com.verizon.vrepair.customer.exception.CustomerAlreadyExistsException;
import com.verizon.vrepair.customer.exception.CustomerValidationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Customer service implementation.
 * Core business logic migrated from legacy C++ customer management system.
 * 
 * Replaces legacy C++ functions:
 * - create_customer()
 * - update_customer()
 * - find_customer_by_id()
 * - search_customers()
 * - validate_customer()
 */
@Service
@Transactional
public class CustomerServiceImpl implements CustomerService {
    
    private final CustomerRepository customerRepository;
    private final CustomerValidator customerValidator;
    
    // Customer code sequence generators (replaces legacy C++ static counters)
    private static final AtomicLong resSequence = new AtomicLong(100000);
    private static final AtomicLong busSequence = new AtomicLong(100000);
    private static final AtomicLong govSequence = new AtomicLong(100000);
    private static final AtomicLong whoSequence = new AtomicLong(100000);
    
    @Autowired
    public CustomerServiceImpl(CustomerRepository customerRepository, 
                              CustomerValidator customerValidator) {
        this.customerRepository = customerRepository;
        this.customerValidator = customerValidator;
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "customers", key = "#pageable.pageNumber + '_' + #pageable.pageSize + '_' + #pageable.sort")
    public Page<Customer> getAllCustomers(Pageable pageable) {
        return customerRepository.findAll(pageable);
    }
    
    @Override
    @Transactional
    public Customer createCustomer(Customer customer) {
        // Validate customer data (legacy validation rules)
        customerValidator.validateCustomerForCreation(customer);
        
        // Check for duplicate phone number (legacy constraint)
        Optional<Customer> existingCustomer = customerRepository.findByPhoneNumber(
                customer.getBillingTelephoneNumber());
        if (existingCustomer.isPresent()) {
            throw new CustomerAlreadyExistsException(
                "Customer with phone number " + customer.getBillingTelephoneNumber() + " already exists");
        }
        
        // Generate customer code (legacy algorithm)
        String customerCode = generateCustomerCode(customer.getCustomerType());
        customer.setCustomerCode(customerCode);
        
        // Set audit fields
        customer.setCreatedDate(LocalDateTime.now());
        customer.setModifiedDate(LocalDateTime.now());
        customer.setCustomerStatus(CustomerStatus.ACTIVE);
        
        // External validation calls would go here (ETMS, BAAIS integration)
        // validateWithExternalSystems(customer);
        
        Customer savedCustomer = customerRepository.save(customer);
        
        // Clear related caches
        clearCustomerCaches(customerCode);
        
        return savedCustomer;
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "customers", key = "#customerCode")
    public Customer updateCustomer(String customerCode, Customer customer) {
        Customer existingCustomer = findCustomerById(customerCode);
        
        // Validate update data
        customerValidator.validateCustomerForUpdate(customer);
        
        // Apply updates (preserving legacy update logic)
        if (customer.getCustomerName() != null) {
            existingCustomer.setCustomerName(customer.getCustomerName());
        }
        if (customer.getServiceAddress() != null) {
            existingCustomer.setServiceAddress(customer.getServiceAddress());
        }
        if (customer.getContactPhone() != null) {
            existingCustomer.setContactPhone(customer.getContactPhone());
        }
        if (customer.getAlternateContact() != null) {
            existingCustomer.setAlternateContact(customer.getAlternateContact());
        }
        if (customer.getEmailAddress() != null) {
            existingCustomer.setEmailAddress(customer.getEmailAddress());
        }
        if (customer.getSpecialInstructions() != null) {
            existingCustomer.setSpecialInstructions(customer.getSpecialInstructions());
        }
        
        // Update audit fields
        existingCustomer.setModifiedDate(LocalDateTime.now());
        
        Customer updatedCustomer = customerRepository.save(existingCustomer);
        
        // Clear related caches
        clearCustomerCaches(customerCode);
        
        return updatedCustomer;
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "customers", key = "#customerCode")
    public Customer findCustomerById(String customerCode) {
        return customerRepository.findById(customerCode)
                .orElseThrow(() -> new CustomerNotFoundException(
                    "Customer not found with code: " + customerCode));
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "customersByPhone", key = "#phoneNumber")
    public Customer findCustomerByPhoneNumber(String phoneNumber) {
        return customerRepository.findByPhoneNumber(phoneNumber)
                .orElseThrow(() -> new CustomerNotFoundException(
                    "Customer not found with phone number: " + phoneNumber));
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<Customer> searchCustomers(String customerName, String phoneNumber, 
                                         CustomerType customerType, CustomerStatus customerStatus,
                                         String serviceAddress, int page, int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("customerName").ascending());
        
        Page<Customer> customerPage = customerRepository.searchCustomers(
                customerName, phoneNumber, customerType, customerStatus, serviceAddress, pageable);
        
        return customerPage.getContent();
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "customers", key = "#customerCode")
    public Customer deactivateCustomer(String customerCode) {
        Customer customer = findCustomerById(customerCode);
        
        if (!customer.getCustomerStatus().canDeactivate()) {
            throw new CustomerValidationException(
                "Customer cannot be deactivated from status: " + customer.getCustomerStatus());
        }
        
        customer.setCustomerStatus(CustomerStatus.INACTIVE);
        customer.setModifiedDate(LocalDateTime.now());
        
        Customer deactivatedCustomer = customerRepository.save(customer);
        
        // Clear related caches
        clearCustomerCaches(customerCode);
        
        return deactivatedCustomer;
    }
    
    @Override
    @Transactional
    @CacheEvict(value = "customers", key = "#customerCode")
    public Customer activateCustomer(String customerCode) {
        Customer customer = findCustomerById(customerCode);
        
        if (!customer.getCustomerStatus().canActivate()) {
            throw new CustomerValidationException(
                "Customer cannot be activated from status: " + customer.getCustomerStatus());
        }
        
        customer.setCustomerStatus(CustomerStatus.ACTIVE);
        customer.setModifiedDate(LocalDateTime.now());
        
        Customer activatedCustomer = customerRepository.save(customer);
        
        // Clear related caches
        clearCustomerCaches(customerCode);
        
        return activatedCustomer;
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean validateCustomerEligibility(String customerCode) {
        Customer customer = findCustomerById(customerCode);
        
        // Legacy eligibility rules
        if (!customer.getCustomerStatus().isActiveService()) {
            return false;
        }
        
        // Additional business rules would go here
        // - Credit check integration
        // - Service availability validation
        // - Geographic coverage validation
        
        return true;
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "customerStatistics")
    public CustomerStatistics getCustomerStatistics() {
        CustomerRepository.CustomerStatistics stats = customerRepository.getCustomerStatistics();
        
        return new CustomerStatistics(
                stats.getTotalCustomers(),
                stats.getActiveCustomers(),
                stats.getInactiveCustomers(),
                stats.getResidentialCustomers(),
                stats.getBusinessCustomers()
        );
    }
    
    /**
     * Generate customer code using legacy algorithm.
     * Format: [TYPE_PREFIX][6_DIGIT_SEQUENCE]
     * 
     * Replaces legacy C++ function: generate_customer_code()
     */
    private String generateCustomerCode(CustomerType customerType) {
        String prefix = customerType.getCodePrefix();
        long sequence;
        
        switch (customerType) {
            case RESIDENTIAL -> sequence = resSequence.incrementAndGet();
            case BUSINESS -> sequence = busSequence.incrementAndGet();
            case GOVERNMENT -> sequence = govSequence.incrementAndGet();
            case WHOLESALE -> sequence = whoSequence.incrementAndGet();
            default -> throw new IllegalArgumentException("Unknown customer type: " + customerType);
        }
        
        return prefix + String.format("%06d", sequence);
    }
    
    /**
     * Clear customer-related caches.
     */
    private void clearCustomerCaches(String customerCode) {
        // Cache eviction is handled by annotations, but additional cleanup could go here
        // For example, clearing related statistics cache
    }
}
